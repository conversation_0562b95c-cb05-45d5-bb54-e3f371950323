# Agent Evaluation Tool - Complete API Endpoints Reference

This document provides a comprehensive list of all available API endpoints in the Agent Evaluation Tool. All endpoints are prefixed with `/api`.

## Base URL

```
http://localhost:8000/api
```

## Task Management Endpoints

### 1. Create Single Task
- **URL**: `/api/tasks`
- **Method**: `POST`
- **Description**: Create a new evaluation task for a single model
- **Request Body**:
  ```json
  {
    "benchmark": "string",    // Required: tau_bench, bfc, gaia
    "model": "string",        // Required: model name
    "framework": "string",    // Optional: agent framework (for GAIA)
    "params": {}              // Optional: additional parameters
  }
  ```
- **Example**:
  ```bash
  curl -X POST "http://localhost:8000/api/tasks" \
    -H "Content-Type: application/json" \
    -d '{
      "benchmark": "tau_bench",
      "model": "gpt-4",
      "params": {
        "env": "retail",
        "temperature": 0.7,
        "max_tokens": 1000
      }
    }'
  ```

### 2. Create Batch Tasks
- **URL**: `/api/tasks/batch`
- **Method**: `POST`
- **Description**: Create multiple evaluation tasks for different models
- **Request Body**:
  ```json
  {
    "benchmark": "string",     // Required: benchmark name
    "models": ["string"],      // Required: list of model names
    "framework": "string",     // Optional: agent framework
    "params": {}               // Optional: additional parameters
  }
  ```
- **Example**:
  ```bash
  curl -X POST "http://localhost:8000/api/tasks/batch" \
    -H "Content-Type: application/json" \
    -d '{
      "benchmark": "tau_bench",
      "models": ["gpt-4", "claude-3-opus", "llama-3-70b"],
      "params": {
        "env": "retail",
        "temperature": 0.7
      }
    }'
  ```

### 3. List All Tasks
- **URL**: `/api/tasks`
- **Method**: `GET`
- **Description**: Get a list of all evaluation tasks
- **Query Parameters**:
  - `status` (optional): Filter by task status (pending, running, completed, failed, cancelled)
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/tasks"
  curl -X GET "http://localhost:8000/api/tasks?status=completed"
  ```

### 4. Get Task Details
- **URL**: `/api/tasks/{task_id}`
- **Method**: `GET`
- **Description**: Get detailed information about a specific task
- **Path Parameters**:
  - `task_id`: The unique task identifier
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/tasks/12345"
  ```

### 5. Cancel Task
- **URL**: `/api/tasks/{task_id}`
- **Method**: `DELETE`
- **Description**: Cancel a running or pending task
- **Path Parameters**:
  - `task_id`: The unique task identifier
- **Example**:
  ```bash
  curl -X DELETE "http://localhost:8000/api/tasks/12345"
  ```

## Benchmark Management Endpoints

### 6. List All Benchmarks
- **URL**: `/api/benchmarks`
- **Method**: `GET`
- **Description**: Get a list of all available benchmarks
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/benchmarks"
  ```



## Results Management Endpoints

### 7. List All Results
- **URL**: `/api/results`
- **Method**: `GET`
- **Description**: Get a list of all evaluation results
- **Query Parameters**:
  - `benchmark` (optional): Filter by benchmark name
  - `model` (optional): Filter by model name
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/results"
  curl -X GET "http://localhost:8000/api/results?benchmark=tau_bench&model=gpt-4"
  ```

### 8. Get Result Details
- **URL**: `/api/results/{result_id}`
- **Method**: `GET`
- **Description**: Get detailed information about a specific evaluation result
- **Path Parameters**:
  - `result_id`: The unique result identifier
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/results/result_12345"
  ```

### 9. Delete Result
- **URL**: `/api/results/{result_id}`
- **Method**: `DELETE`
- **Description**: Delete a specific evaluation result
- **Path Parameters**:
  - `result_id`: The unique result identifier
- **Example**:
  ```bash
  curl -X DELETE "http://localhost:8000/api/results/result_12345"
  ```

## System Management Endpoints

### 10. Get Worker Statistics
- **URL**: `/api/stats`
- **Method**: `GET`
- **Description**: Get worker pool statistics and system status
- **Example**:
  ```bash
  curl -X GET "http://localhost:8000/api/stats"
  ```
- **Response Example**:
  ```json
  {
    "active_workers": 2,
    "tasks_completed": 15,
    "tasks_failed": 1,
    "tasks_cancelled": 0,
    "total_execution_time": 1234.56,
    "model_stats": {
      "gpt-4": {
        "tasks_completed": 8,
        "tasks_failed": 0,
        "avg_execution_time": 45.2,
        "total_execution_time": 361.6
      }
    }
  }
  ```

## HTTP Status Codes

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

## Error Response Format

When an error occurs, the API returns a JSON response with error details:

```json
{
  "detail": "Error message describing what went wrong"
}
```

## Authentication

Currently, the API does not require authentication. All endpoints are publicly accessible.

## Rate Limiting

No rate limiting is currently implemented, but it's recommended to avoid overwhelming the system with too many concurrent requests.

## Notes

1. All timestamps in responses are Unix timestamps (seconds since epoch)
2. Task IDs and result IDs are UUID strings
3. The `/api` prefix is required for all endpoints
4. Content-Type header should be set to `application/json` for POST requests
5. The API supports CORS for cross-origin requests
