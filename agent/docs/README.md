# AgentEval 文档中心

欢迎来到 AgentEval 文档中心！AgentEval 是一个用于评估大语言模型代理能力的框架，集成了多种基准测试，采用轻量级、可扩展的架构。

## 📚 文档导航

### 🏗️ 系统架构
- **[系统架构文档](system_architecture.md)** - 深入了解 AgentEval 的设计架构
  - 整体架构设计
  - 核心组件介绍
  - 数据流和处理流程
  - 技术栈说明

### 🔧 开发指南
- **[基准测试集成开发指南](benchmark_integration_guide.md)** - 开发者必读，集成新基准测试
  - 完整的集成步骤和最佳实践
  - 适配器开发模板和示例代码
  - 配置类设计和参数验证
  - 推理后端集成方法
  - 测试和调试技巧
  - 故障排除和性能优化

### 🔍 技术细节
- **[参数验证架构](parameter_validation_architecture.md)** - 了解参数验证的设计原理
  - 统一验证架构设计
  - 配置类设计模式
  - 验证流程说明
  - 避免代码冗余的方法

### 🚀 GAIA 评测
- **[GAIA 服务集成](gaia_service_integration.md)** - 服务化GAIA评测方式
  - 动态LLM配置
  - 客户端-服务端架构
  - 自动配置解析
  - 多模型支持

### 📖 API参考
- **[模块详细说明](module_details.md)** - 各模块的详细说明
  - 核心模块功能介绍
  - 接口和数据结构
  - 使用示例和最佳实践

## 🎯 按使用场景分类

### 使用现有基准测试
- [系统架构文档](system_architecture.md) - 了解支持的基准测试和系统组件
- [GAIA 服务集成](gaia_service_integration.md) - 使用GAIA服务化评测

### 集成新的基准测试
- [基准测试集成开发指南](benchmark_integration_guide.md) - 完整集成流程
- [参数验证架构](parameter_validation_architecture.md) - 参数处理最佳实践

### GAIA 评测
- [GAIA 服务集成](gaia_service_integration.md) - 服务化评测方式

### 系统维护和优化
- [系统架构文档](system_architecture.md) - 理解系统组件
- [基准测试集成开发指南](benchmark_integration_guide.md) - 故障排除部分

## 📖 推荐阅读顺序

### 新用户
1. **[系统架构文档](system_architecture.md)** - 了解基本概念和系统设计
2. **[模块详细说明](module_details.md)** - 理解各模块功能
3. **[GAIA 服务集成](gaia_service_integration.md)** - 如果要使用GAIA评测
4. 根据需要阅读其他专题文档

### GAIA 评测用户
1. **[GAIA 服务集成](gaia_service_integration.md)** - 服务化评测方式

### 开发者
1. **[系统架构文档](system_architecture.md)** - 理解整体架构
2. **[参数验证架构](parameter_validation_architecture.md)** - 了解参数处理机制
3. **[基准测试集成开发指南](benchmark_integration_guide.md)** - 学习如何扩展系统

## 🔗 相关资源

### 外部文档
- [FastAPI 官方文档](https://fastapi.tiangolo.com/) - API框架文档
- [Pydantic 官方文档](https://docs.pydantic.dev/) - 数据验证库文档
- [SQLite 文档](https://www.sqlite.org/docs.html) - 数据库文档

### 基准测试相关
- [Tau-Bench](https://github.com/tau-bench/tau-bench) - Tau-Bench 官方仓库
- [Berkeley Function Calling Leaderboard](https://github.com/ShishirPatil/gorilla) - BFC 官方仓库
- [GAIA](https://huggingface.co/gaia-benchmark) - GAIA 基准测试

## 🆘 获取帮助

如果在阅读文档后仍有疑问，可以通过以下方式获取帮助：

1. **查看 FAQ**: 每个文档都包含常见问题解答
2. **搜索 Issues**: 在项目仓库中搜索相关问题
3. **提交 Issue**: 描述具体问题并提供相关信息
4. **社区讨论**: 参与项目社区讨论

## 📝 文档贡献

我们欢迎对文档的改进建议和贡献！

### 如何贡献文档

1. **发现问题**: 在使用过程中发现文档不清楚或有错误
2. **提出建议**: 通过 Issue 或 Pull Request 提出改进建议
3. **编写内容**: 按照现有文档的格式和风格编写
4. **审核发布**: 经过审核后合并到主分支

### 文档编写规范

- 使用清晰的标题层次结构
- 提供具体的代码示例
- 包含必要的截图或图表
- 保持内容的时效性和准确性
- 考虑不同技术水平的读者

---

**文档版本**: 与代码版本保持同步

**维护者**: AgentEval 开发团队
