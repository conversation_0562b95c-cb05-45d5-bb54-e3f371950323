# GAIA Service Integration - Dynamic LLM Configuration

## Overview

This document describes the GAIA evaluation approach using the GAIA service with dynamic LLM configuration. The client sends evaluation requests to a dedicated GAIA service that can dynamically configure different LLM backends based on the client's worker configuration.

## Architecture

### Client-Server Model
- **Client** (`agent/src/adapters/gaia/gaia.py`): Reads GAIA tasks and sends them to the service
- **Service** (`owl/gaia_service/`): Receives requests and performs evaluation with dynamic LLM configuration
- **Configuration**: Client automatically resolves LLM configuration from `worker.yaml` based on model name

### Key Benefits
1. **Dynamic Model Configuration**: Switch between different LLMs without server restart
2. **Centralized Evaluation**: Multiple clients can use the same service
3. **Automatic Configuration Resolution**: Client intelligently maps model names to configurations
4. **Scalability**: Service can handle multiple concurrent evaluation requests

## Configuration Setup

### 1. Worker Configuration (`agent/config/worker.yaml`)

Configure multiple LLM endpoints in your worker configuration:

```yaml
llm:
  default_timeout: 60
  max_retries: 3
  endpoints:
    - name: "openai"
      url: "https://api.openai.com/v1"
      api_key: "your-openai-api-key-here"
      models:
        - "gpt-4"
        - "gpt-3.5-turbo"
        - "gpt-4o"
    - name: "anthropic"
      url: "https://api.anthropic.com/v1"
      api_key: "your-anthropic-api-key-here"
      models:
        - "claude-3-opus"
        - "claude-3-sonnet"
        - "claude-3-haiku"
    - name: "local_qwen_8b"
      url: "http://*************:29998/v1"
      api_key: "1234"
      models:
        - "Qwen/Qwen3-8B"
    - name: "local_qwen_30b"
      url: "http://*************:29998/v1"
      api_key: "1234"
      models:
        - "Qwen/Qwen3-30B-A3B"

# GAIA service configuration
benchmarks:
  gaia:
    enabled: true
    service_url: "http://localhost:8000"
    max_concurrent_requests: 4
    request_timeout: 600
```

### 2. Service Configuration

The GAIA service should be running and accessible at the configured URL (default: `http://localhost:8000`).

## Usage

### 1. Using the Agent Client

Run GAIA evaluation with different models:

```bash
# Evaluate with GPT-4
python -m agent.evaluation_worker \
  --benchmark gaia \
  --model gpt-4 \
  --level 1 \
  --subset 10

# Evaluate with Claude
python -m agent.evaluation_worker \
  --benchmark gaia \
  --model claude-3-sonnet \
  --level 2 \
  --subset 5

# Evaluate with local model
python -m agent.evaluation_worker \
  --benchmark gaia \
  --model "Qwen/Qwen3-8B" \
  --level 1 \
  --subset 20
```

### 2. Direct API Usage

You can also send requests directly to the service:

```bash
curl -X POST "http://localhost:8000/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gaia",
    "model": "gpt-4",
    "params": {
      "query": "What is the capital of France?",
      "task_id": "test-001",
      "level": 1,
      "final_answer": "Paris",
      "timeout": 300
    }
  }'
```

## How It Works

### 1. Automatic Configuration Resolution

When you specify a model name, the client automatically:

1. **Exact Match**: Looks for the model in the `models` list of each endpoint
2. **Smart Match**: Matches model prefixes to platforms (e.g., `gpt-*` → `openai`, `claude-*` → `anthropic`)
3. **Fallback**: Uses the first endpoint if no match is found

### 2. Request Flow

```
User specifies: model="gpt-4"
↓
Client reads GAIA dataset tasks
↓
Client resolves LLM config from worker.yaml:
{
  "model_platform": "openai",
  "model_type": "gpt-4", 
  "api_key": "your-key",
  "url": "https://api.openai.com/v1"
}
↓
Client sends to service:
{
  "query": "What is the capital of France?",  // From GAIA dataset
  "llm_config": { ... },                     // Auto-resolved config
  "task_id": "gaia_001",
  "level": 1,
  "final_answer": "Paris"
}
↓
Service creates workforce with specified LLM
↓
Service performs evaluation and returns results
```

### 3. Service-Side Processing

The service:
1. Receives the request with LLM configuration
2. Dynamically creates a workforce using the specified model
3. Processes the GAIA task using the configured LLM
4. Returns evaluation results with execution traces

## Supported Model Platforms

The system supports all CAMEL framework model platforms:

- **OpenAI**: `gpt-4`, `gpt-3.5-turbo`, `gpt-4o`, etc.
- **Anthropic**: `claude-3-opus`, `claude-3-sonnet`, `claude-3-haiku`
- **VLLM**: Local models like `Qwen/Qwen3-8B`, `Llama-2-7b-chat`, etc.
- **Azure OpenAI**: Azure-hosted OpenAI models
- **Other Platforms**: Groq, LM Studio, OpenRouter, ZhipuAI, Gemini, Mistral, etc.

## Configuration Examples

### OpenAI Models
```yaml
- name: "openai"
  url: "https://api.openai.com/v1"
  api_key: "sk-..."
  models: ["gpt-4", "gpt-3.5-turbo", "gpt-4o"]
```

### Anthropic Models
```yaml
- name: "anthropic"
  url: "https://api.anthropic.com/v1"
  api_key: "sk-ant-..."
  models: ["claude-3-opus", "claude-3-sonnet"]
```

### Local VLLM Models
```yaml
- name: "local_qwen"
  url: "http://localhost:8000/v1"
  api_key: "EMPTY"
  models: ["Qwen/Qwen3-8B", "Qwen/Qwen3-30B"]
```

## Monitoring and Debugging

### 1. Service Health Check
```bash
curl http://localhost:8000/health
```

### 2. Task Status Monitoring
```bash
curl http://localhost:8000/status/{task_id}
```

### 3. Logs
- Client logs: `agent/logs/evaluation_worker.log`
- Service logs: Check service console output

## Getting Started

To start using GAIA evaluation:

1. **Start the GAIA service**: Ensure the service is running
2. **Configure worker.yaml**: Add LLM endpoints and service URL to your `worker.yaml`
3. **Run evaluation**: Use the evaluation commands with your desired model

The client will automatically detect the service configuration and route requests accordingly.

## Troubleshooting

### Common Issues

1. **Service not reachable**: Check if the service is running and the URL is correct
2. **Model not found**: Ensure the model is listed in one of your worker.yaml endpoints
3. **API key issues**: Verify API keys are correct and have sufficient quota
4. **Timeout errors**: Increase request timeout in configuration

### Debug Mode

Enable debug logging to see detailed request/response information:

```bash
export LOG_LEVEL=DEBUG
python -m agent.evaluation_worker --benchmark gaia --model gpt-4
```

This new approach provides a more scalable and flexible way to run GAIA evaluations with different LLM configurations!
