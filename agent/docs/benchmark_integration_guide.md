# 基准测试集成开发指南

## 目录

- [概述](#概述)
- [系统架构理解](#系统架构理解)
- [集成步骤](#集成步骤)
- [配置管理](#配置管理)
- [推理后端集成](#推理后端集成)
- [测试与调试](#测试与调试)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 概述

AgentEval 是一个用于评估大语言模型代理能力的框架，采用适配器模式支持多种基准测试。本指南将详细介绍如何将新的基准测试集成到系统中。

### 核心设计原则

1. **适配器模式**：每个基准测试通过适配器与系统集成
2. **统一推理后端**：使用 AgentEval 的推理后端替代原生模型处理器
3. **保留原有逻辑**：保持基准测试的原有评估逻辑和结果格式
4. **异步执行**：支持异步任务处理和并发执行
5. **配置驱动**：通过配置文件和配置类管理基准测试参数
6. **简化API**：统一的API接口，无需复杂的任务参数

## 系统架构理解

### 核心组件

```
API层 (FastAPI)
    ↓
任务队列 (SQLite)
    ↓
工作器池 (异步)
    ↓
适配器注册表
    ↓
基准测试适配器 ← 推理后端管理器
    ↓
外部基准测试代码
```

### 关键目录结构

```
src/
├── adapters/              # 基准测试适配器
│   ├── base.py           # 基础适配器接口
│   ├── registry.py       # 适配器注册表
│   ├── tau_bench.py      # Tau-Bench 适配器
│   ├── bfc.py           # BFC 适配器
│   ├── gaia.py          # GAIA 适配器
│   └── your_benchmark.py # 新基准测试适配器
├── engine/               # 执行引擎
│   ├── config.py         # 全局配置管理
│   ├── logging_config.py # 日志配置
│   ├── queue.py          # 任务队列
│   └── worker.py         # 工作器
├── inference_backends/   # 推理后端系统
│   ├── manager.py        # 后端管理器
│   ├── openai_backend.py
│   ├── anthropic_backend.py
│   ├── local_backend.py
│   └── google_backend.py
├── benchmark_config/     # 基准测试配置类
│   ├── base.py           # 基础配置类
│   ├── tau_bench.py      # Tau-Bench配置
│   ├── bfc.py           # BFC配置
│   └── gaia.py          # GAIA配置
├── api/
│   ├── routes.py         # API 路由
│   └── models.py         # API 数据模型
├── frameworks/           # Agent框架集成
│   ├── autogen_wrapper.py
│   └── smol_wrapper.py
└── storage/              # 结果存储
    └── db.py

external/                 # 外部基准测试代码
├── tau-bench/           # Tau-Bench 原始代码
├── bfc/                 # BFC 原始代码
├── gaia/                # GAIA 原始代码
└── your-benchmark/      # 新基准测试的原始代码

config/
├── default.yaml         # 默认配置
└── worker.yaml          # 工作器配置
```

## 集成步骤

### 步骤 1：准备外部基准测试代码

1. **获取基准测试代码**
   ```bash
   cd external/
   git clone https://github.com/your-org/your-benchmark.git
   # 或者下载并解压到 external/your-benchmark/
   ```

2. **安装依赖**
   ```bash
   cd external/your-benchmark/
   pip install -r requirements.txt
   # 或者将依赖添加到项目根目录的 requirements.txt
   ```

3. **理解原有接口**
   - 分析基准测试的入口函数
   - 理解参数结构和返回格式
   - 识别模型调用接口

### 步骤 2：创建基准测试配置类

在 `src/benchmark_config/` 目录下创建配置类：

```python
# src/benchmark_config/your_benchmark.py
from pydantic import Field, validator
from typing import Optional, List
from .base import BaseBenchmarkConfig

class YourBenchmarkConfig(BaseBenchmarkConfig):
    """Your benchmark configuration with validation and defaults."""

    # 基准测试特定参数
    dataset: str = Field(default="test", description="Dataset split to use")
    num_samples: int = Field(default=100, ge=1, description="Number of samples to evaluate")
    temperature: float = Field(default=0.0, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: int = Field(default=2048, ge=1, description="Maximum tokens per response")
    timeout: int = Field(default=300, ge=1, description="Timeout in seconds")

    # 可选参数
    custom_prompt: Optional[str] = Field(default=None, description="Custom prompt template")
    categories: Optional[List[str]] = Field(default=None, description="Specific categories to evaluate")

    # 参数验证
    @validator('dataset')
    def validate_dataset(cls, v):
        valid_datasets = ["train", "test", "dev", "validation"]
        if v not in valid_datasets:
            raise ValueError(f"Dataset must be one of {valid_datasets}")
        return v

    @validator('categories', pre=True)
    def validate_categories(cls, v):
        if v is not None and not isinstance(v, list):
            # Convert single string to list
            return [v] if isinstance(v, str) else v
        return v
```

### 步骤 3：开发适配器

创建 `src/adapters/your_benchmark.py`：

```python
# src/adapters/your_benchmark.py
import sys
import asyncio
import importlib
from typing import Dict, Any
import logging
from pathlib import Path

from src.adapters.base import BaseAdapter
from src.inference_backends import backend_manager
from src.benchmark_config.your_benchmark import YourBenchmarkConfig

logger = logging.getLogger(__name__)

class YourBenchmarkAdapter(BaseAdapter):
    """Adapter for Your Benchmark."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the adapter.

        Args:
            config: Adapter configuration from worker.yaml
        """
        super().__init__(config)
        self.repo_path = Path(config.get("repo_path", "./external/your-benchmark"))
        self._benchmark_module = None
        self._initialize_benchmark()

    def _initialize_benchmark(self):
        """Initialize the benchmark by adding to Python path and importing."""
        try:
            # Add benchmark path to sys.path
            benchmark_path = str(self.repo_path.resolve())
            if benchmark_path not in sys.path:
                sys.path.insert(0, benchmark_path)

            # Check if the repo exists
            if not self.repo_path.exists():
                logger.error(f"Benchmark repository not found at {self.repo_path}")
                return

            # Import necessary modules
            self._benchmark_module = importlib.import_module("your_benchmark.main")
            logger.info(f"Successfully initialized benchmark from {self.repo_path}")

        except ImportError as e:
            logger.warning(f"Failed to initialize benchmark due to missing dependencies: {e}")
            logger.warning("Benchmark functionality will be limited. Please install dependencies.")
        except Exception as e:
            logger.error(f"Failed to initialize benchmark: {e}")
            raise

    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute benchmark evaluation.

        Args:
            params: Evaluation parameters

        Returns:
            Evaluation results
        """
        # 1. 验证参数 (使用配置类进行验证)
        validated_params = await self.validate_params(params)

        try:
            if not self._benchmark_module:
                raise RuntimeError("Benchmark not properly initialized. Please check dependencies and repository path.")

            # 2. 获取推理后端
            inference_backend = self._get_inference_backend_by_model(validated_params["model"])
            if not inference_backend:
                raise ValueError(f"No inference backend found for model: {validated_params['model']}")

            # 3. 创建配置对象
            config_params = validated_params.copy()
            config_params["inference_backend"] = inference_backend

            benchmark_config = YourBenchmarkConfig(**config_params)

            # 4. 执行基准测试
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, self._run_benchmark, benchmark_config
            )

            # 5. 处理和返回结果
            return self._process_results(results, validated_params, benchmark_config)

        except Exception as e:
            logger.error(f"Error executing benchmark: {e}")
            raise

    def _get_inference_backend_by_model(self, model_name: str):
        """Get inference backend for a specific model."""
        try:
            # Search through all registered backends to find one that supports this model
            for backend_name, backend in backend_manager.backends.items():
                if hasattr(backend, 'models') and backend.models:
                    if model_name in backend.models:
                        logger.info(f"Found backend '{backend_name}' for model '{model_name}'")
                        return backend

            logger.warning(f"No backend found for model '{model_name}'")
            return None

        except Exception as e:
            logger.warning(f"Failed to get inference backend for model '{model_name}': {e}")
            return None

    def _run_benchmark(self, config: YourBenchmarkConfig):
        """Run the benchmark in a separate thread."""
        # 调用原始基准测试代码
        return self._benchmark_module.run_evaluation(config)

    def _process_results(self, results, params: Dict[str, Any], config: YourBenchmarkConfig):
        """Process benchmark results into standard format."""
        # 计算指标
        total_samples = len(results) if results else 0
        accuracy = self._calculate_accuracy(results) if results else 0.0

        # 创建可序列化的配置字典
        config_dict = config.model_dump_serializable()

        return {
            "benchmark": "your_benchmark",
            "model": params["model"],
            "dataset": params.get("dataset", "test"),
            "total_samples": total_samples,
            "accuracy": accuracy,
            "results": results,
            "config": config_dict
        }

    def _calculate_accuracy(self, results):
        """Calculate accuracy from results."""
        if not results:
            return 0.0

        correct = sum(1 for r in results if r.get("correct", False))
        return correct / len(results)

    async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate parameters using YourBenchmarkConfig.

        Args:
            params: Evaluation parameters

        Returns:
            Validated and normalized parameters

        Raises:
            ValueError: If parameters are invalid
        """
        try:
            # Create config - this handles all validation, normalization, and defaults
            config = YourBenchmarkConfig(**params)

            # Return the validated parameters as a dictionary (excluding runtime-only fields)
            validated_params = config.model_dump()
            # Remove runtime-only fields that are set in execute()
            validated_params.pop("inference_backend", None)
            return validated_params

        except Exception as e:
            raise ValueError(f"Parameter validation failed: {str(e)}")
```

### 步骤 4：注册适配器

修改 `src/adapters/registry.py` 的 `initialize_default_adapters()` 函数：

```python
def initialize_default_adapters() -> None:
    """Initialize default adapters (only once)."""
    global _adapters_initialized

    if _adapters_initialized:
        logger.debug("Default adapters already initialized, skipping")
        return

    # 注册现有适配器
    try:
        from src.adapters.tau_bench import TauBenchAdapter
        register_adapter("tau_bench", TauBenchAdapter)
        logger.info("Registered tau_bench adapter")
    except ImportError as e:
        logger.warning(f"Failed to register tau_bench adapter: {e}")

    try:
        from src.adapters.bfc import BFCAdapter
        register_adapter("bfc", BFCAdapter)
        logger.info("Registered bfc adapter")
    except ImportError as e:
        logger.warning(f"Failed to register bfc adapter: {e}")

    try:
        from src.adapters.gaia import GAIAAdapter
        register_adapter("gaia", GAIAAdapter)
        logger.info("Registered gaia adapter")
    except ImportError as e:
        logger.warning(f"Failed to register gaia adapter: {e}")

    # 注册新的适配器
    try:
        from src.adapters.your_benchmark import YourBenchmarkAdapter
        register_adapter("your_benchmark", YourBenchmarkAdapter)
        logger.info("Registered your_benchmark adapter")
    except ImportError as e:
        logger.warning(f"Failed to register your_benchmark adapter: {e}")

    _adapters_initialized = True
    logger.info("Default adapters initialization completed")
```

### 步骤 5：配置基准测试

在 `config/worker.yaml` 中添加配置：

```yaml
benchmarks:
  your_benchmark:
    enabled: true
    repo_path: "./external/your-benchmark"
    # 其他特定配置参数
    default_dataset: "test"
    default_num_samples: 100
    timeout: 300
```

## 配置管理

### 统一配置架构

AgentEval 采用分层配置管理：

1. **全局配置** (`config/default.yaml`, `config/worker.yaml`)
2. **基准测试配置类** (`src/benchmark_config/`)
3. **运行时参数验证** (适配器中的 `validate_params`)

### 配置文件结构

基准测试配置遵循统一模式：

```yaml
# config/worker.yaml
benchmarks:
  your_benchmark:
    enabled: true                    # 是否启用
    repo_path: "./external/your-benchmark"  # 代码路径
    # 基准测试特定配置
    default_dataset: "test"
    default_num_samples: 100
    timeout: 300
    custom_param: "value"

# LLM 推理后端配置
llm:
  endpoints:
    - name: "openai"
      url: "https://api.openai.com/v1"
      api_key: "${LLM_API_KEY}"
      models: ["gpt-4", "gpt-3.5-turbo"]
    - name: "local"
      url: "http://localhost:8000/v1"
      api_key: "dummy"
      models: ["Qwen/Qwen3-8B"]
```

### 配置类设计

所有配置类都应继承 `BaseBenchmarkConfig`：

```python
from src.benchmark_config.base import BaseBenchmarkConfig
from pydantic import Field, validator
from typing import Optional, List

class YourBenchmarkConfig(BaseBenchmarkConfig):
    """Configuration class with validation and type safety."""

    # model 和 inference_backend 已在基类中定义

    # 添加基准测试特定参数
    dataset: str = Field(default="test", description="Dataset split")
    num_samples: int = Field(default=100, ge=1, description="Number of samples")
    temperature: float = Field(default=0.0, ge=0.0, le=2.0, description="Temperature")
    categories: Optional[List[str]] = Field(default=None, description="Categories to evaluate")

    # 参数验证
    @validator('dataset')
    def validate_dataset(cls, v):
        valid_datasets = ["train", "test", "dev", "validation"]
        if v not in valid_datasets:
            raise ValueError(f"Dataset must be one of {valid_datasets}")
        return v

    @validator('categories', pre=True)
    def validate_categories(cls, v):
        if v is not None and isinstance(v, str):
            return [v]  # Convert single string to list
        return v
```

### 参数验证流程

1. **API层** (`src/api/models.py`): 基本请求格式验证
2. **适配器层** (`validate_params`): 使用配置类进行详细验证
3. **配置类** (`BenchmarkConfig`): 类型检查、默认值、业务逻辑验证
4. **运行时验证**: 模型可用性、资源检查等

## 推理后端集成

### 理解推理后端系统

AgentEval 提供统一的推理后端接口，支持多种 LLM 提供商：

- **OpenAI Backend**: OpenAI API 兼容接口 (支持 OpenAI, Azure OpenAI)
- **Anthropic Backend**: Anthropic Claude API
- **Local Backend**: 本地部署的模型（如 vLLM, Ollama）
- **Google Backend**: Google AI API (Gemini)

### 后端管理器

后端管理器 (`src/inference_backends/manager.py`) 负责：

- 从配置文件加载后端
- 根据模型名称自动选择合适的后端
- 管理后端生命周期

```python
from src.inference_backends import backend_manager

# 初始化后端管理器（通常在启动时完成）
backend_manager.initialize_from_config(config)

# 根据模型获取后端
backend = backend_manager.get_backend_for_model("gpt-4")
backend = backend_manager.get_backend_for_model("claude-3-sonnet")
backend = backend_manager.get_backend_for_model("Qwen/Qwen3-8B")
```

### 在适配器中使用推理后端

```python
def _get_inference_backend_by_model(self, model_name: str):
    """Get inference backend for a specific model."""
    try:
        # Search through all registered backends
        for backend_name, backend in backend_manager.backends.items():
            if hasattr(backend, 'models') and backend.models:
                if model_name in backend.models:
                    logger.info(f"Found backend '{backend_name}' for model '{model_name}'")
                    return backend

        logger.warning(f"No backend found for model '{model_name}'")
        return None

    except Exception as e:
        logger.warning(f"Failed to get inference backend: {e}")
        return None

# 使用后端进行推理
response = inference_backend.completion(
    messages=[{"role": "user", "content": "Hello"}],
    model=model_name,
    temperature=0.7,
    max_tokens=1024
)
```

### 替换原有模型处理器

许多基准测试有自己的模型处理逻辑。集成时的常见模式：

1. **识别模型调用点**：找到原始代码中的模型调用位置
2. **创建适配函数**：将推理后端接口适配到原有接口
3. **修改导入路径**：使用修改后的模块替代原有模块

#### 模式 1：修改原始代码 (推荐)

```python
# 在基准测试代码中添加支持推理后端的函数
def main_with_backend(args, inference_backend):
    """Modified main function that uses AgentEval inference backend."""
    # 替换原有的模型客户端
    original_client = args.client
    args.client = create_client_wrapper(inference_backend)

    # 调用原有逻辑
    result = original_main(args)

    # 恢复原有客户端
    args.client = original_client
    return result

def create_client_wrapper(inference_backend):
    """Create a client wrapper that adapts inference backend to original interface."""
    class ClientWrapper:
        def __init__(self, backend):
            self.backend = backend

        def chat_completion(self, messages, model, **kwargs):
            # 适配到推理后端接口
            return self.backend.completion(
                messages=messages,
                model=model,
                **kwargs
            )

    return ClientWrapper(inference_backend)
```

#### 模式 2：猴子补丁 (适用于难以修改的代码)

```python
def _patch_model_client(self, inference_backend):
    """Patch the original model client with our inference backend."""
    import your_benchmark.model_client as client_module

    original_client = client_module.ModelClient

    class PatchedClient:
        def __init__(self, *args, **kwargs):
            self.backend = inference_backend

        def generate(self, prompt, **kwargs):
            messages = [{"role": "user", "content": prompt}]
            response = self.backend.completion(messages=messages, **kwargs)
            return response.choices[0].message.content

    # 替换原有客户端
    client_module.ModelClient = PatchedClient

    return lambda: setattr(client_module, 'ModelClient', original_client)  # 恢复函数
```

## 测试与调试

### 创建调试脚本

参考现有的调试脚本，为新基准测试创建调试脚本：

```python
# debug/scripts/debug_your_benchmark.py
#!/usr/bin/env python3
"""Debug script for Your Benchmark evaluation."""

import asyncio
import logging
from src.adapters.your_benchmark import YourBenchmarkAdapter
from src.core.config import get_config
from src.core.inference_backends import backend_manager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_adapter_directly():
    """Test the adapter directly."""
    try:
        # 加载配置
        config = get_config("config/worker.yaml")

        # 初始化推理后端
        backend_manager.initialize_from_config(config)

        # 创建适配器
        adapter_config = config.get("benchmarks.your_benchmark", {})
        adapter = YourBenchmarkAdapter(adapter_config)

        # 测试参数
        test_params = {
            "model": "gpt-4",  # 确保这个模型在配置中存在
            "dataset": "test",
            "num_samples": 3,  # 小数量用于快速测试
            "temperature": 0.0
        }

        logger.info(f"Testing with parameters: {test_params}")

        # 执行测试
        result = await adapter.execute(test_params)

        logger.info("✅ Test successful!")
        logger.info(f"Results summary: {result.get('total_samples', 0)} samples, "
                   f"accuracy: {result.get('accuracy', 0):.2%}")

        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_parameter_validation():
    """Test parameter validation."""
    try:
        config = get_config("config/worker.yaml")
        adapter_config = config.get("benchmarks.your_benchmark", {})
        adapter = YourBenchmarkAdapter(adapter_config)

        # 测试有效参数
        valid_params = {"model": "gpt-4", "dataset": "test"}
        validated = await adapter.validate_params(valid_params)
        logger.info(f"✅ Valid params test passed: {validated}")

        # 测试无效参数
        try:
            invalid_params = {"model": "gpt-4", "dataset": "invalid_dataset"}
            await adapter.validate_params(invalid_params)
            logger.error("❌ Invalid params test failed - should have raised error")
        except ValueError as e:
            logger.info(f"✅ Invalid params test passed: {e}")

        return True

    except Exception as e:
        logger.error(f"❌ Parameter validation test failed: {e}")
        return False

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--test-params", action="store_true", help="Test parameter validation")
    parser.add_argument("--test-direct", action="store_true", help="Test adapter directly")
    args = parser.parse_args()

    if args.test_params:
        asyncio.run(test_parameter_validation())
    elif args.test_direct:
        asyncio.run(test_adapter_directly())
    else:
        # 运行所有测试
        asyncio.run(test_parameter_validation())
        asyncio.run(test_adapter_directly())
```

### 测试步骤

#### 1. 直接测试适配器

```bash
# 测试参数验证
python debug/scripts/debug_your_benchmark.py --test-params

# 测试完整执行
python debug/scripts/debug_your_benchmark.py --test-direct

# 运行所有测试
python debug/scripts/debug_your_benchmark.py
```

#### 2. 通过 API 测试

```bash
# 启动服务
python api_server.py &
python evaluation_worker.py &

# 提交任务
curl -X POST http://localhost:8000/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "your_benchmark",
    "model": "gpt-4",
    "params": {"dataset": "test", "num_samples": 5}
  }'

# 批量测试多个模型
curl -X POST http://localhost:8000/api/tasks/batch \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "your_benchmark",
    "models": ["gpt-4", "gpt-3.5-turbo"],
    "params": {"dataset": "test", "num_samples": 3}
  }'
```

#### 3. 检查结果

```bash
# 查看任务状态
curl http://localhost:8000/api/tasks/{task_id}

# 查看所有任务
curl http://localhost:8000/api/tasks

# 查看基准测试列表
curl http://localhost:8000/api/benchmarks

# 查看结果
curl http://localhost:8000/api/results

# 查看具体结果
curl http://localhost:8000/api/results/{result_id}
```

### 调试技巧

1. **日志配置**：在 `config/default.yaml` 中调整日志级别
2. **断点调试**：使用 IDE 断点或 `pdb.set_trace()`
3. **小数据集测试**：使用 `num_samples=1` 进行快速测试
4. **模拟推理后端**：创建 mock 后端用于测试


## 故障排除

### 常见问题

#### 1. 导入错误

**问题**：`ImportError: No module named 'your_benchmark'`

**解决方案**：
- 检查 `repo_path` 配置是否正确
- 确保外部基准测试代码已下载
- 验证 Python 路径设置

```python
# 在适配器中添加调试信息
logger.info(f"Adding path to sys.path: {benchmark_path}")
logger.info(f"Current sys.path: {sys.path}")
```

#### 2. 推理后端未找到

**问题**：`No inference backend found for model: gpt-4`

**解决方案**：
- 检查 `config/worker.yaml` 中的 LLM 配置
- 确保模型名称在后端的 `models` 列表中
- 验证 API 密钥设置

```yaml
llm:
  endpoints:
    - name: "openai"
      url: "https://api.openai.com/v1"
      api_key: "${LLM_API_KEY}"
      models: ["gpt-4", "gpt-3.5-turbo"]  # 确保包含所需模型
```

#### 3. 参数验证失败

**问题**：`Parameter validation failed: Dataset must be one of ['train', 'test', 'dev']`

**解决方案**：
- 检查配置类中的验证逻辑
- 确保 API 请求参数正确
- 查看配置类的默认值设置


### 获取帮助

1. **查看现有实现**：参考 `tau_bench.py`, `bfc.py`, `gaia.py` 的实现
2. **查看文档**：阅读 `docs/` 目录下的其他文档
3. **检查配置**：确保 `config/worker.yaml` 配置正确
4. **社区支持**：在项目仓库中提交 issue

