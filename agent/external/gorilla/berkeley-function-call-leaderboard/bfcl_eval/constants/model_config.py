from dataclasses import dataclass
from typing import Optional

from bfcl_eval.model_handler.api_inference.claude import <PERSON><PERSON><PERSON><PERSON>
from bfcl_eval.model_handler.api_inference.cohere import Co<PERSON><PERSON>andler
from bfcl_eval.model_handler.api_inference.databricks import Databricks<PERSON>andler
from bfcl_eval.model_handler.api_inference.deepseek import DeepSeekAPIHandler
from bfcl_eval.model_handler.api_inference.dm_cito import DMCitoHandler
from bfcl_eval.model_handler.api_inference.fireworks import FireworksHandler
from bfcl_eval.model_handler.api_inference.functionary import FunctionaryHandler
from bfcl_eval.model_handler.api_inference.gemini import <PERSON><PERSON>and<PERSON>
from bfcl_eval.model_handler.api_inference.gogoagent import GoGoAgentHandler
from bfcl_eval.model_handler.api_inference.gorilla import Go<PERSON><PERSON>andler
from bfcl_eval.model_handler.api_inference.grok import <PERSON><PERSON><PERSON>and<PERSON>
from bfcl_eval.model_handler.api_inference.mining import <PERSON><PERSON>andler
from bfcl_eval.model_handler.api_inference.mistral import MistralHandler
from bfcl_eval.model_handler.api_inference.nemotron import NemotronHandler
from bfcl_eval.model_handler.api_inference.nexus import NexusHandler
from bfcl_eval.model_handler.api_inference.nova import NovaHandler
from bfcl_eval.model_handler.api_inference.novita import NovitaHandler
from bfcl_eval.model_handler.api_inference.nvidia import NvidiaHandler
from bfcl_eval.model_handler.api_inference.openai import OpenAIHandler
from bfcl_eval.model_handler.api_inference.qwen import QwenAPIHandler
from bfcl_eval.model_handler.api_inference.writer import WriterHandler
from bfcl_eval.model_handler.api_inference.yi import YiHandler
from bfcl_eval.model_handler.local_inference.bielik import BielikHandler
from bfcl_eval.model_handler.local_inference.deepseek import DeepseekHandler
from bfcl_eval.model_handler.local_inference.deepseek_coder import DeepseekCoderHandler
from bfcl_eval.model_handler.local_inference.deepseek_reasoning import DeepseekReasoningHandler
from bfcl_eval.model_handler.local_inference.falcon_fc import Falcon3FCHandler
from bfcl_eval.model_handler.local_inference.gemma import GemmaHandler
from bfcl_eval.model_handler.local_inference.glaive import GlaiveHandler
from bfcl_eval.model_handler.local_inference.glm import GLMHandler
from bfcl_eval.model_handler.local_inference.granite import GraniteHandler
from bfcl_eval.model_handler.local_inference.hammer import HammerHandler
from bfcl_eval.model_handler.local_inference.hermes import HermesHandler
from bfcl_eval.model_handler.local_inference.llama import LlamaHandler
from bfcl_eval.model_handler.local_inference.llama_3_1 import LlamaHandler_3_1
from bfcl_eval.model_handler.local_inference.minicpm import MiniCPMHandler
from bfcl_eval.model_handler.local_inference.minicpm_fc import MiniCPMFCHandler
from bfcl_eval.model_handler.local_inference.mistral_fc import MistralFCHandler
from bfcl_eval.model_handler.local_inference.phi import PhiHandler
from bfcl_eval.model_handler.local_inference.phi_fc import PhiFCHandler
from bfcl_eval.model_handler.local_inference.quick_testing_oss import QuickTestingOSSHandler
from bfcl_eval.model_handler.local_inference.qwen import QwenHandler
from bfcl_eval.model_handler.local_inference.qwen_fc import QwenFCHandler
from bfcl_eval.model_handler.local_inference.salesforce_llama import SalesforceLlamaHandler
from bfcl_eval.model_handler.local_inference.salesforce_qwen import SalesforceQwenHandler
from bfcl_eval.model_handler.local_inference.think_agent import ThinkAgentHandler
from bfcl_eval.model_handler.api_inference.ling import LingAPIHandler

# -----------------------------------------------------------------------------
# A mapping of model identifiers to their respective model configurations.
# Each key corresponds to the model id passed to the `--model` argument
# in both generation and evaluation commands.
# Make sure to update the `supported_models.py` file as well when updating this map.
# -----------------------------------------------------------------------------


@dataclass
class ModelConfig:
    """
    Model configuration class for storing model metadata and settings.

    Attributes:
        model_name (str): [Not Used] Name of the model as used in the API or on Hugging Face.
        display_name (str): Model name as it should appear on the leaderboard.
        url (str): Reference URL for the model or hosting service.
        org (str): Organization providing the model.
        license (str): License under which the model is released.
        model_handler (str): Handler name for invoking the model.
        input_price (Optional[float]): USD per million input tokens (None for open source models).
        output_price (Optional[float]): USD per million output tokens (None for open source models).
        is_fc_model (bool): True if this model is used in Function-Calling mode, otherwise False for Prompt-based mode.
        underscore_to_dot (bool): True if model does not support '.' in function names, in which case we will replace '.' with '_'. Currently this only matters for checker.  TODO: We should let the tool compilation step also take this into account.

    """

    model_name: str
    display_name: str
    url: str
    org: str
    license: str

    model_handler: str

    # Prices are in USD per million tokens; open source models have None
    input_price: Optional[float] = None
    output_price: Optional[float] = None

    # True if the model is in function-calling mode, False if in prompt mode
    is_fc_model: bool = True

    # True if this model does not allow '.' in function names
    underscore_to_dot: bool = False


# Inference through API calls
api_inference_model_map = {
    "gorilla-openfunctions-v2": ModelConfig(
        model_name="gorilla-openfunctions-v2",
        display_name="Gorilla-OpenFunctions-v2 (FC)",
        url="https://gorilla.cs.berkeley.edu/blogs/7_open_functions_v2.html",
        org="Gorilla LLM",
        license="Apache 2.0",
        model_handler=GorillaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "DeepSeek-R1-0528": ModelConfig(
        model_name="DeepSeek-R1-0528",
        display_name="DeepSeek-R1-0528 (Prompt)",
        url="https://api-docs.deepseek.com/news/news250528",
        org="DeepSeek",
        license="MIT",
        model_handler=DeepSeekAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "DeepSeek-R1-0528-FC": ModelConfig(
        model_name="DeepSeek-R1-0528-FC",
        display_name="DeepSeek-R1-0528 (FC)",
        url="https://api-docs.deepseek.com/news/news250528",
        org="DeepSeek",
        license="MIT",
        model_handler=DeepSeekAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "DeepSeek-V3-0324-FC": ModelConfig(
        model_name="DeepSeek-V3-0324",
        display_name="DeepSeek-V3-0324 (FC)",
        url="https://api-docs.deepseek.com/news/news250325",
        org="DeepSeek",
        license="DeepSeek License",
        model_handler=DeepSeekAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4.5-preview-2025-02-27": ModelConfig(
        model_name="gpt-4.5-preview-2025-02-27",
        display_name="GPT-4.5-Preview-2025-02-27 (Prompt)",
        url="https://openai.com/index/introducing-gpt-4-5/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=75,
        output_price=150,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4.5-preview-2025-02-27-FC": ModelConfig(
        model_name="gpt-4.5-preview-2025-02-27-FC",
        display_name="GPT-4.5-Preview-2025-02-27 (FC)",
        url="https://openai.com/index/introducing-gpt-4-5/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=75,
        output_price=150,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4.1-2025-04-14-FC": ModelConfig(
        model_name="gpt-4.1-2025-04-14-FC",
        display_name="GPT-4.1-2025-04-14 (FC)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=2,
        output_price=8,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4.1-2025-04-14": ModelConfig(
        model_name="gpt-4.1-2025-04-14",
        display_name="GPT-4.1-2025-04-14 (Prompt)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=2,
        output_price=8,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4.1-mini-2025-04-14-FC": ModelConfig(
        model_name="gpt-4.1-mini-2025-04-14-FC",
        display_name="GPT-4.1-mini-2025-04-14 (FC)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.4,
        output_price=1.6,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4.1-mini-2025-04-14": ModelConfig(
        model_name="gpt-4.1-mini-2025-04-14",
        display_name="GPT-4.1-mini-2025-04-14 (Prompt)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.4,
        output_price=1.6,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4.1-nano-2025-04-14-FC": ModelConfig(
        model_name="gpt-4.1-nano-2025-04-14-FC",
        display_name="GPT-4.1-nano-2025-04-14 (FC)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.1,
        output_price=0.4,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4.1-nano-2025-04-14": ModelConfig(
        model_name="gpt-4.1-nano-2025-04-14",
        display_name="GPT-4.1-nano-2025-04-14 (Prompt)",
        url="https://openai.com/index/gpt-4-1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.1,
        output_price=0.4,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "o1-2024-12-17-FC": ModelConfig(
        model_name="o1-2024-12-17-FC",
        display_name="o1-2024-12-17 (FC)",
        url="https://openai.com/o1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=15,
        output_price=60,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "o1-2024-12-17": ModelConfig(
        model_name="o1-2024-12-17",
        display_name="o1-2024-12-17 (Prompt)",
        url="https://openai.com/o1/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=15,
        output_price=60,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "o3-mini-2025-01-31-FC": ModelConfig(
        model_name="o3-mini-2025-01-31-FC",
        display_name="o3-mini-2025-01-31 (FC)",
        url="https://openai.com/index/openai-o3-mini/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=1.1,
        output_price=4,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "o3-mini-2025-01-31": ModelConfig(
        model_name="o3-mini-2025-01-31",
        display_name="o3-mini-2025-01-31 (Prompt)",
        url="https://openai.com/index/openai-o3-mini/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=1.1,
        output_price=4,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4o-2024-11-20": ModelConfig(
        model_name="gpt-4o-2024-11-20",
        display_name="GPT-4o-2024-11-20 (Prompt)",
        url="https://openai.com/index/hello-gpt-4o/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=2.5,
        output_price=10,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4o-2024-11-20-FC": ModelConfig(
        model_name="gpt-4o-2024-11-20-FC",
        display_name="GPT-4o-2024-11-20 (FC)",
        url="https://openai.com/index/hello-gpt-4o/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=2.5,
        output_price=10,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gpt-4o-mini-2024-07-18": ModelConfig(
        model_name="gpt-4o-mini-2024-07-18",
        display_name="GPT-4o-mini-2024-07-18 (Prompt)",
        url="https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.15,
        output_price=0.6,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gpt-4o-mini-2024-07-18-FC": ModelConfig(
        model_name="gpt-4o-mini-2024-07-18-FC",
        display_name="GPT-4o-mini-2024-07-18 (FC)",
        url="https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/",
        org="OpenAI",
        license="Proprietary",
        model_handler=OpenAIHandler,
        input_price=0.15,
        output_price=0.6,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "claude-3-opus-20240229": ModelConfig(
        model_name="claude-3-opus-20240229",
        display_name="Claude-3-Opus-20240229 (Prompt)",
        url="https://www.anthropic.com/news/claude-3-family",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=15,
        output_price=75,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "claude-3-opus-20240229-FC": ModelConfig(
        model_name="claude-3-opus-20240229-FC",
        display_name="Claude-3-Opus-20240229 (FC)",
        url="https://www.anthropic.com/news/claude-3-family",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=15,
        output_price=75,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "claude-3-7-sonnet-20250219": ModelConfig(
        model_name="claude-3-7-sonnet-20250219",
        display_name="Claude-3.7-Sonnet-20250219 (Prompt)",
        url="https://www.anthropic.com/news/claude-3-7-sonnet",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=3,
        output_price=15,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "claude-3-7-sonnet-20250219-FC": ModelConfig(
        model_name="claude-3-7-sonnet-20250219-FC",
        display_name="Claude-3.7-Sonnet-20250219 (FC)",
        url="https://www.anthropic.com/news/claude-3-7-sonnet",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=3,
        output_price=15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "claude-3-5-sonnet-20241022": ModelConfig(
        model_name="claude-3-5-sonnet-20241022",
        display_name="Claude-3.5-Sonnet-20241022 (Prompt)",
        url="https://www.anthropic.com/news/3-5-models-and-computer-use",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=3,
        output_price=15,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "claude-3-5-sonnet-20241022-FC": ModelConfig(
        model_name="claude-3-5-sonnet-20241022-FC",
        display_name="Claude-3.5-Sonnet-20241022 (FC)",
        url="https://www.anthropic.com/news/3-5-models-and-computer-use",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=3,
        output_price=15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "claude-3-5-haiku-20241022": ModelConfig(
        model_name="claude-3-5-haiku-20241022",
        display_name="claude-3.5-haiku-20241022 (Prompt)",
        url="https://www.anthropic.com/news/3-5-models-and-computer-use",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=1,
        output_price=5,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "claude-3-5-haiku-20241022-FC": ModelConfig(
        model_name="claude-3-5-haiku-20241022-FC",
        display_name="claude-3.5-haiku-20241022 (FC)",
        url="https://www.anthropic.com/news/3-5-models-and-computer-use",
        org="Anthropic",
        license="Proprietary",
        model_handler=ClaudeHandler,
        input_price=1,
        output_price=5,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "nova-pro-v1.0": ModelConfig(
        model_name="nova-pro-v1.0",
        display_name="Amazon-Nova-Pro-v1:0 (FC)",
        url="https://aws.amazon.com/cn/ai/generative-ai/nova/",
        org="Amazon",
        license="Proprietary",
        model_handler=NovaHandler,
        input_price=0.8,
        output_price=3.2,
        is_fc_model=False,
        underscore_to_dot=True,
    ),
    "nova-lite-v1.0": ModelConfig(
        model_name="nova-lite-v1.0",
        display_name="Amazon-Nova-Lite-v1:0 (FC)",
        url="https://aws.amazon.com/cn/ai/generative-ai/nova/",
        org="Amazon",
        license="Proprietary",
        model_handler=NovaHandler,
        input_price=0.06,
        output_price=0.24,
        is_fc_model=False,
        underscore_to_dot=True,
    ),
    "nova-micro-v1.0": ModelConfig(
        model_name="nova-micro-v1.0",
        display_name="Amazon-Nova-Micro-v1:0 (FC)",
        url="https://aws.amazon.com/cn/ai/generative-ai/nova/",
        org="Amazon",
        license="Proprietary",
        model_handler=NovaHandler,
        input_price=0.035,
        output_price=0.14,
        is_fc_model=False,
        underscore_to_dot=True,
    ),
    "open-mistral-nemo-2407": ModelConfig(
        model_name="open-mistral-nemo-2407",
        display_name="Open-Mistral-Nemo-2407 (Prompt)",
        url="https://mistral.ai/news/mistral-nemo/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.15,
        output_price=0.15,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "open-mistral-nemo-2407-FC": ModelConfig(
        model_name="open-mistral-nemo-2407-FC",
        display_name="Open-Mistral-Nemo-2407 (FC)",
        url="https://mistral.ai/news/mistral-nemo/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.15,
        output_price=0.15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "mistral-large-2411": ModelConfig(
        model_name="mistral-large-2411",
        display_name="mistral-large-2411 (Prompt)",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=2,
        output_price=6,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "mistral-large-2411-FC": ModelConfig(
        model_name="mistral-large-2411-FC",
        display_name="mistral-large-2411 (FC)",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=2,
        output_price=6,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "mistral-small-2503": ModelConfig(
        model_name="mistral-small-2503",
        display_name="Mistral-Small-2503 (Prompt)",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.1,
        output_price=0.3,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "mistral-small-2503-FC": ModelConfig(
        model_name="mistral-small-2503-FC",
        display_name="Mistral-small-2503 (FC)",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.1,
        output_price=0.3,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "mistral-medium-2505": ModelConfig(
        model_name="mistral-medium-2505",
        display_name="Mistral-Medium-2505",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.4,
        output_price=2,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "mistral-medium-2505-FC": ModelConfig(
        model_name="mistral-medium-2505-FC",
        display_name="Mistral-Medium-2505 (FC)",
        url="https://docs.mistral.ai/guides/model-selection/",
        org="Mistral AI",
        license="Proprietary",
        model_handler=MistralHandler,
        input_price=0.4,
        output_price=2,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "firefunction-v2-FC": ModelConfig(
        model_name="firefunction-v2-FC",
        display_name="FireFunction-v2 (FC)",
        url="https://huggingface.co/fireworks-ai/firefunction-v2",
        org="Fireworks",
        license="Apache 2.0",
        model_handler=FireworksHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Nexusflow-Raven-v2": ModelConfig(
        model_name="Nexusflow-Raven-v2",
        display_name="Nexusflow-Raven-v2 (FC)",
        url="https://huggingface.co/Nexusflow/NexusRaven-V2-13B",
        org="Nexusflow",
        license="Apache 2.0",
        model_handler=NexusHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gemini-2.0-flash-lite-001-FC": ModelConfig(
        model_name="gemini-2.0-flash-lite-001-FC",
        display_name="Gemini-2.0-Flash-Lite-001 (FC)",
        url="https://deepmind.google/technologies/gemini/flash-lite/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0.075,
        output_price=0.3,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gemini-2.0-flash-lite-001": ModelConfig(
        model_name="gemini-2.0-flash-lite-001",
        display_name="Gemini-2.0-Flash-Lite-001 (Prompt)",
        url="https://deepmind.google/technologies/gemini/flash-lite/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0.075,
        output_price=0.3,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gemini-2.0-flash-001-FC": ModelConfig(
        model_name="gemini-2.0-flash-001-FC",
        display_name="Gemini-2.0-Flash-001 (FC)",
        url="https://deepmind.google/technologies/gemini/flash/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0.15,
        output_price=0.6,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gemini-2.0-flash-001": ModelConfig(
        model_name="gemini-2.0-flash-001",
        display_name="Gemini-2.0-Flash-001 (Prompt)",
        url="https://deepmind.google/technologies/gemini/flash/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0.15,
        output_price=0.6,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gemini-2.5-pro-preview-05-06-FC": ModelConfig(
        model_name="gemini-2.5-pro-preview-05-06-FC",
        display_name="Gemini-2.5-Pro-Preview-05-06 (FC)",
        url="https://deepmind.google/technologies/gemini/pro/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0,
        output_price=0,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "gemini-2.5-pro-preview-05-06": ModelConfig(
        model_name="gemini-2.5-pro-preview-05-06",
        display_name="Gemini-2.5-Pro-Preview-05-06 (Prompt)",
        url="https://deepmind.google/technologies/gemini/pro/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0,
        output_price=0,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "gemini-2.0-flash-thinking-exp-01-21": ModelConfig(
        model_name="gemini-2.0-flash-thinking-exp-01-21",
        display_name="Gemini-2.0-Flash-Thinking-Exp-01-21 (Prompt)",
        url="https://deepmind.google/technologies/gemini/flash-thinking/",
        org="Google",
        license="Proprietary",
        model_handler=GeminiHandler,
        input_price=0,
        output_price=0,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meetkai/functionary-small-v3.1-FC": ModelConfig(
        model_name="meetkai/functionary-small-v3.1-FC",
        display_name="Functionary-Small-v3.1 (FC)",
        url="https://huggingface.co/meetkai/functionary-small-v3.1",
        org="MeetKai",
        license="MIT",
        model_handler=FunctionaryHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "meetkai/functionary-medium-v3.1-FC": ModelConfig(
        model_name="meetkai/functionary-medium-v3.1-FC",
        display_name="Functionary-Medium-v3.1 (FC)",
        url="https://huggingface.co/meetkai/functionary-medium-v3.1",
        org="MeetKai",
        license="MIT",
        model_handler=FunctionaryHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "databricks-dbrx-instruct": ModelConfig(
        model_name="databricks-dbrx-instruct",
        display_name="DBRX-Instruct (Prompt)",
        url="https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm",
        org="Databricks",
        license="Databricks Open Model",
        model_handler=DatabricksHandler,
        input_price=2.25,
        output_price=6.75,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "command-r-plus-FC": ModelConfig(
        model_name="command-r-plus-FC",
        display_name="Command-R-Plus (FC)",
        url="https://txt.cohere.com/command-r-plus-microsoft-azure",
        org="Cohere For AI",
        license="cc-by-nc-4.0",
        model_handler=CohereHandler,
        input_price=3,
        output_price=15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "command-r7b-12-2024-FC": ModelConfig(
        model_name="command-r7b-12-2024-FC",
        display_name="Command R7B (FC)",
        url="https://cohere.com/blog/command-r7b",
        org="Cohere",
        license="cc-by-nc-4.0",
        model_handler=CohereHandler,
        input_price=0.0375,
        output_price=0.15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "command-a-03-2025-FC": ModelConfig(
        model_name="command-a-03-2025-FC",
        display_name="Command A (FC)",
        url="https://cohere.com/blog/command-a",
        org="Cohere",
        license="CC-BY-NC 4.0 License (w/ Acceptable Use Addendum)",
        model_handler=CohereHandler,
        input_price=2.5,
        output_price=10,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "snowflake/arctic": ModelConfig(
        model_name="snowflake/arctic",
        display_name="Snowflake/snowflake-arctic-instruct (Prompt)",
        url="https://huggingface.co/Snowflake/snowflake-arctic-instruct",
        org="Snowflake",
        license="apache-2.0",
        model_handler=NvidiaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "nvidia/llama-3.1-nemotron-ultra-253b-v1": ModelConfig(
        model_name="nvidia/llama-3.1-nemotron-ultra-253b-v1",
        display_name="Llama-3.1-Nemotron-Ultra-253B-v1 (FC)",
        url="https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1",
        org="NVIDIA",
        license="nvidia-open-model-license",
        model_handler=NemotronHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "nvidia/nemotron-4-340b-instruct": ModelConfig(
        model_name="nvidia/nemotron-4-340b-instruct",
        display_name="Nemotron-4-340b-instruct (Prompt)",
        url="https://huggingface.co/nvidia/nemotron-4-340b-instruct",
        org="NVIDIA",
        license="nvidia-open-model-license",
        model_handler=NvidiaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "BitAgent/GoGoAgent": ModelConfig(
        model_name="BitAgent/GoGoAgent",
        display_name="GoGoAgent",
        url="https://gogoagent.ai",
        org="BitAgent",
        license="Proprietary",
        model_handler=GoGoAgentHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "palmyra-x-004": ModelConfig(
        model_name="palmyra-x-004",
        display_name="palmyra-x-004 (FC)",
        url="https://writer.com/engineering/actions-with-palmyra-x-004/",
        org="Writer",
        license="Proprietary",
        model_handler=WriterHandler,
        input_price=5,
        output_price=12,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "grok-3-beta-FC": ModelConfig(
        model_name="grok-3-beta-FC",
        display_name="Grok-3-beta (FC)",
        url="https://docs.x.ai/docs/models",
        org="xAI",
        license="Proprietary",
        model_handler=GrokHandler,
        input_price=3,
        output_price=15,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "grok-3-beta": ModelConfig(
        model_name="grok-3-beta",
        display_name="Grok-3-beta (Prompt)",
        url="https://docs.x.ai/docs/models",
        org="xAI",
        license="Proprietary",
        model_handler=GrokHandler,
        input_price=3,
        output_price=15,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "grok-3-mini-beta-FC": ModelConfig(
        model_name="grok-3-mini-beta-FC",
        display_name="Grok-3-mini-beta (FC)",
        url="https://docs.x.ai/docs/models",
        org="xAI",
        license="Proprietary",
        model_handler=GrokHandler,
        input_price=0.3,
        output_price=0.5,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "grok-3-mini-beta": ModelConfig(
        model_name="grok-3-mini-beta",
        display_name="Grok-3-mini-beta (Prompt)",
        url="https://docs.x.ai/docs/models",
        org="xAI",
        license="Proprietary",
        model_handler=GrokHandler,
        input_price=0.3,
        output_price=0.5,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-0.6b-FC": ModelConfig(
        model_name="qwen3-0.6b-FC",
        display_name="Qwen3-0.6B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-0.6B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-0.6b": ModelConfig(
        model_name="qwen3-0.6b",
        display_name="Qwen3-0.6B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-0.6B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-1.7b-FC": ModelConfig(
        model_name="qwen3-1.7b-FC",
        display_name="Qwen3-1.7B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-1.7B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-1.7b": ModelConfig(
        model_name="qwen3-1.7b",
        display_name="Qwen3-1.7B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-1.7B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-4b-FC": ModelConfig(
        model_name="qwen3-4b-FC",
        display_name="Qwen3-4B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-4B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-4b": ModelConfig(
        model_name="qwen3-4b",
        display_name="Qwen3-4B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-4B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-8b-FC": ModelConfig(
        model_name="qwen3-8b-FC",
        display_name="Qwen3-8B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-8B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-8b": ModelConfig(
        model_name="qwen3-8b",
        display_name="Qwen3-8B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-8B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-14b-FC": ModelConfig(
        model_name="qwen3-14b-FC",
        display_name="Qwen3-14B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-14B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-14b": ModelConfig(
        model_name="qwen3-14b",
        display_name="Qwen3-14B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-14B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-32b-FC": ModelConfig(
        model_name="qwen3-32b-FC",
        display_name="Qwen3-32B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-32b": ModelConfig(
        model_name="qwen3-32b",
        display_name="Qwen3-32B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-30b-a3b-FC": ModelConfig(
        model_name="qwen3-30b-a3b-FC",
        display_name="Qwen3-30B-A3B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-30B-A3B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-30b-a3b": ModelConfig(
        model_name="qwen3-30b-a3b",
        display_name="Qwen3-30B-A3B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-30B-A3B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwen3-235b-a22b-FC": ModelConfig(
        model_name="qwen3-235b-a22b-FC",
        display_name="Qwen3-235B-A22B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-235B-A22B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen3-235b-a22b": ModelConfig(
        model_name="qwen3-235b-a22b",
        display_name="Qwen3-235B-A22B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-235B-A22B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "qwq-32b-FC": ModelConfig(
        model_name="qwq-32b-FC",
        display_name="QwQ-32B (FC)",
        url="https://huggingface.co/Qwen/QwQ-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwq-32b": ModelConfig(
        model_name="qwq-32b",
        display_name="QwQ-32B (Prompt)",
        url="https://huggingface.co/Qwen/QwQ-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "xiaoming-14B": ModelConfig(
        model_name="xiaoming-14B",
        display_name="xiaoming-14B (Prompt)",
        url="https://www.mininglamp.com/",
        org="Mininglamp",
        license="Proprietary",
        model_handler=MiningHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "DM-Cito-8B": ModelConfig(
        model_name="DM-Cito-8B",
        display_name="DM-Cito-8B (Prompt)",
        url="https://www.mininglamp.com/",
        org="Mininglamp",
        license="Proprietary",
        model_handler=DMCitoHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Ling/ling-lite-v1.5": ModelConfig(
        model_name="Ling/ling-lite-v1.5",
        display_name="ling-lite-v1.5 (Prompt)",
        url="https://huggingface.co/inclusionAI/Ling-lite-1.5",
        org="Ling",
        license="MIT",
        model_handler=LingAPIHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
}

# Inference through local hosting
local_inference_model_map = {
    "deepseek-ai/DeepSeek-R1": ModelConfig(
        model_name="deepseek-ai/DeepSeek-R1",
        display_name="DeepSeek-R1 (Prompt) (Local)",
        url="https://huggingface.co/deepseek-ai/DeepSeek-R1",
        org="DeepSeek",
        license="MIT",
        model_handler=DeepseekReasoningHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "google/gemma-3-1b-it": ModelConfig(
        model_name="google/gemma-3-1b-it",
        display_name="Gemma-3-1b-it (Prompt)",
        url="https://blog.google/technology/developers/gemma-3/",
        org="Google",
        license="gemma-terms-of-use",
        model_handler=GemmaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "google/gemma-3-4b-it": ModelConfig(
        model_name="google/gemma-3-4b-it",
        display_name="Gemma-3-4b-it (Prompt)",
        url="https://blog.google/technology/developers/gemma-3/",
        org="Google",
        license="gemma-terms-of-use",
        model_handler=GemmaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "google/gemma-3-12b-it": ModelConfig(
        model_name="google/gemma-3-12b-it",
        display_name="Gemma-3-12b-it (Prompt)",
        url="https://blog.google/technology/developers/gemma-3/",
        org="Google",
        license="gemma-terms-of-use",
        model_handler=GemmaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "google/gemma-3-27b-it": ModelConfig(
        model_name="google/gemma-3-27b-it",
        display_name="Gemma-3-27b-it (Prompt)",
        url="https://blog.google/technology/developers/gemma-3/",
        org="Google",
        license="gemma-terms-of-use",
        model_handler=GemmaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.1-8B-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct-FC",
        display_name="Llama-3.1-8B-Instruct (FC)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler_3_1,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.1-8B-Instruct": ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        display_name="Llama-3.1-8B-Instruct (Prompt)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.1-70B-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-3.1-70B-Instruct-FC",
        display_name="Llama-3.1-70B-Instruct (FC)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler_3_1,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.1-70B-Instruct": ModelConfig(
        model_name="meta-llama/Llama-3.1-70B-Instruct",
        display_name="Llama-3.1-70B-Instruct (Prompt)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.2-1B-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-3.2-1B-Instruct-FC",
        display_name="Llama-3.2-1B-Instruct (FC)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.2-3B-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-3.2-3B-Instruct-FC",
        display_name="Llama-3.2-3B-Instruct (FC)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-3.3-70B-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-3.3-70B-Instruct-FC",
        display_name="Llama-3.3-70B-Instruct (FC)",
        url="https://llama.meta.com/llama3",
        org="Meta",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-4-Scout-17B-16E-Instruct-FC": ModelConfig(
        model_name="meta-llama/Llama-4-Scout-17B-16E-Instruct-FC",
        display_name="Llama-4-Scout-17B-16E-Instruct (FC)",
        url="https://huggingface.co/meta-llama/Llama-4-Scout-17B-16E-Instruct",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8-FC": ModelConfig(
        model_name="meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8-FC",
        display_name="Llama-4-Maverick-17B-128E-Instruct-FP8 (FC)",
        url="https://huggingface.co/meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Salesforce/Llama-xLAM-2-70b-fc-r": ModelConfig(
        model_name="Salesforce/Llama-xLAM-2-70b-fc-r",
        display_name="xLAM-2-70b-fc-r (FC)",
        url="https://huggingface.co/Salesforce/Llama-xLAM-2-70b-fc-r",
        org="Salesforce",
        license="cc-by-nc-4.0",
        model_handler=SalesforceLlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Salesforce/Llama-xLAM-2-8b-fc-r": ModelConfig(
        model_name="Salesforce/Llama-xLAM-2-8b-fc-r",
        display_name="xLAM-2-8b-fc-r (FC)",
        url="https://huggingface.co/Salesforce/Llama-xLAM-2-8b-fc-r",
        org="Salesforce",
        license="cc-by-nc-4.0",
        model_handler=SalesforceLlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Salesforce/xLAM-2-32b-fc-r": ModelConfig(
        model_name="Salesforce/xLAM-2-32b-fc-r",
        display_name="xLAM-2-32b-fc-r (FC)",
        url="https://huggingface.co/Salesforce/xLAM-2-32b-fc-r",
        org="Salesforce",
        license="cc-by-nc-4.0",
        model_handler=SalesforceQwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Salesforce/xLAM-2-3b-fc-r": ModelConfig(
        model_name="Salesforce/xLAM-2-3b-fc-r",
        display_name="xLAM-2-3b-fc-r (FC)",
        url="https://huggingface.co/Salesforce/xLAM-2-3b-fc-r",
        org="Salesforce",
        license="cc-by-nc-4.0",
        model_handler=SalesforceQwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Salesforce/xLAM-2-1b-fc-r": ModelConfig(
        model_name="Salesforce/xLAM-2-1b-fc-r",
        display_name="xLAM-2-1b-fc-r (FC)",
        url="https://huggingface.co/Salesforce/xLAM-2-1b-fc-r",
        org="Salesforce",
        license="cc-by-nc-4.0",
        model_handler=SalesforceQwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "mistralai/Ministral-8B-Instruct-2410": ModelConfig(
        model_name="mistralai/Ministral-8B-Instruct-2410",
        display_name="Ministral-8B-Instruct-2410 (FC)",
        url="https://huggingface.co/mistralai/Ministral-8B-Instruct-2410",
        org="Mistral AI",
        license="Mistral AI Research License",
        model_handler=MistralFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "microsoft/phi-4": ModelConfig(
        model_name="microsoft/phi-4",
        display_name="Phi-4 (Prompt)",
        url="https://huggingface.co/microsoft/phi-4",
        org="Microsoft",
        license="MIT",
        model_handler=PhiHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "microsoft/Phi-4-mini-instruct": ModelConfig(
        model_name="microsoft/Phi-4-mini-instruct",
        display_name="Phi-4-mini-instruct (Prompt)",
        url="https://huggingface.co/microsoft/Phi-4-mini-instruct",
        org="Microsoft",
        license="MIT",
        model_handler=PhiHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "microsoft/Phi-4-mini-instruct-FC": ModelConfig(
        model_name="microsoft/Phi-4-mini-instruct-FC",
        display_name="Phi-4-mini-instruct (FC)",
        url="https://huggingface.co/microsoft/Phi-4-mini-instruct",
        org="Microsoft",
        license="MIT",
        model_handler=PhiFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "ibm-granite/granite-20b-functioncalling": ModelConfig(
        model_name="ibm-granite/granite-20b-functioncalling",
        display_name="Granite-20b-FunctionCalling (FC)",
        url="https://huggingface.co/ibm-granite/granite-20b-functioncalling",
        org="IBM",
        license="Apache-2.0",
        model_handler=GraniteHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=True,
    ),
    "MadeAgents/Hammer2.1-7b": ModelConfig(
        model_name="MadeAgents/Hammer2.1-7b",
        display_name="Hammer2.1-7b (FC)",
        url="https://huggingface.co/MadeAgents/Hammer2.1-7b",
        org="MadeAgents",
        license="cc-by-nc-4.0",
        model_handler=HammerHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "MadeAgents/Hammer2.1-3b": ModelConfig(
        model_name="MadeAgents/Hammer2.1-3b",
        display_name="Hammer2.1-3b (FC)",
        url="https://huggingface.co/MadeAgents/Hammer2.1-3b",
        org="MadeAgents",
        license="qwen-research",
        model_handler=HammerHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "MadeAgents/Hammer2.1-1.5b": ModelConfig(
        model_name="MadeAgents/Hammer2.1-1.5b",
        display_name="Hammer2.1-1.5b (FC)",
        url="https://huggingface.co/MadeAgents/Hammer2.1-1.5b",
        org="MadeAgents",
        license="cc-by-nc-4.0",
        model_handler=HammerHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "MadeAgents/Hammer2.1-0.5b": ModelConfig(
        model_name="MadeAgents/Hammer2.1-0.5b",
        display_name="Hammer2.1-0.5b (FC)",
        url="https://huggingface.co/MadeAgents/Hammer2.1-0.5b",
        org="MadeAgents",
        license="cc-by-nc-4.0",
        model_handler=HammerHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "THUDM/glm-4-9b-chat": ModelConfig(
        model_name="THUDM/glm-4-9b-chat",
        display_name="GLM-4-9b-Chat (FC)",
        url="https://huggingface.co/THUDM/glm-4-9b-chat",
        org="THUDM",
        license="glm-4",
        model_handler=GLMHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=True,
    ),
    "Qwen/Qwen3-0.6B-FC": ModelConfig(
        model_name="Qwen/Qwen3-0.6B-FC",
        display_name="Qwen3-0.6B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-0.6B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-0.6B": ModelConfig(
        model_name="Qwen/Qwen3-0.6B",
        display_name="Qwen3-0.6B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-0.6B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-1.7B-FC": ModelConfig(
        model_name="Qwen/Qwen3-1.7B-FC",
        display_name="Qwen3-1.7B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-1.7B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-1.7B": ModelConfig(
        model_name="Qwen/Qwen3-1.7B",
        display_name="Qwen3-1.7B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-1.7B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-4B-FC": ModelConfig(
        model_name="Qwen/Qwen3-4B-FC",
        display_name="Qwen3-4B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-4B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-4B": ModelConfig(
        model_name="Qwen/Qwen3-4B",
        display_name="Qwen3-4B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-4B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-8B-FC": ModelConfig(
        model_name="Qwen/Qwen3-8B-FC",
        display_name="Qwen3-8B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-8B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-8B": ModelConfig(
        model_name="Qwen/Qwen3-8B",
        display_name="Qwen3-8B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-8B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-14B-FC": ModelConfig(
        model_name="Qwen/Qwen3-14B-FC",
        display_name="Qwen3-14B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-14B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-14B": ModelConfig(
        model_name="Qwen/Qwen3-14B",
        display_name="Qwen3-14B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-14B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-32B-FC": ModelConfig(
        model_name="Qwen/Qwen3-32B-FC",
        display_name="Qwen3-32B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-32B": ModelConfig(
        model_name="Qwen/Qwen3-32B",
        display_name="Qwen3-32B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-30B-A3B-FC": ModelConfig(
        model_name="Qwen/Qwen3-30B-A3B-FC",
        display_name="Qwen3-30B-A3B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-30B-A3B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-30B-A3B": ModelConfig(
        model_name="Qwen/Qwen3-30B-A3B",
        display_name="Qwen3-30B-A3B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-30B-A3B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-235B-A22B-FC": ModelConfig(
        model_name="Qwen/Qwen3-235B-A22B-FC",
        display_name="Qwen3-235B-A22B (FC)",
        url="https://huggingface.co/Qwen/Qwen3-235B-A22B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "Qwen/Qwen3-235B-A22B": ModelConfig(
        model_name="Qwen/Qwen3-235B-A22B",
        display_name="Qwen3-235B-A22B (Prompt)",
        url="https://huggingface.co/Qwen/Qwen3-235B-A22B",
        org="Qwen",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "Team-ACE/ToolACE-2-8B": ModelConfig(
        model_name="Team-ACE/ToolACE-2-8B",
        display_name="ToolACE-2-8B (FC)",
        url="https://huggingface.co/Team-ACE/ToolACE-2-8B",
        org="Huawei Noah & USTC",
        license="Apache-2.0",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "openbmb/MiniCPM3-4B": ModelConfig(
        model_name="openbmb/MiniCPM3-4B",
        display_name="MiniCPM3-4B (Prompt)",
        url="https://huggingface.co/openbmb/MiniCPM3-4B",
        org="openbmb",
        license="Apache-2.0",
        model_handler=MiniCPMHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "openbmb/MiniCPM3-4B-FC": ModelConfig(
        model_name="openbmb/MiniCPM3-4B-FC",
        display_name="MiniCPM3-4B-FC (FC)",
        url="https://huggingface.co/openbmb/MiniCPM3-4B",
        org="openbmb",
        license="Apache-2.0",
        model_handler=MiniCPMFCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "watt-ai/watt-tool-8B": ModelConfig(
        model_name="watt-ai/watt-tool-8B",
        display_name="watt-tool-8B (FC)",
        url="https://huggingface.co/watt-ai/watt-tool-8B/",
        org="Watt AI Lab",
        license="Apache-2.0",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "watt-ai/watt-tool-70B": ModelConfig(
        model_name="watt-ai/watt-tool-70B",
        display_name="watt-tool-70B (FC)",
        url="https://huggingface.co/watt-ai/watt-tool-70B/",
        org="Watt AI Lab",
        license="Apache-2.0",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "ZJared/Haha-7B": ModelConfig(
        model_name="ZJared/Haha-7B",
        display_name="Haha-7B",
        url="https://huggingface.co/ZJared/Haha-7B",
        org="TeleAI",
        license="Apache 2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "speakleash/Bielik-11B-v2.3-Instruct": ModelConfig(
        model_name="speakleash/Bielik-11B-v2.3-Instruct",
        display_name="Bielik-11B-v2.3-Instruct (Prompt)",
        url="https://huggingface.co/speakleash/Bielik-11B-v2.3-Instruct",
        org="SpeakLeash & ACK Cyfronet AGH",
        license="Apache 2.0",
        model_handler=BielikHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "NovaSky-AI/Sky-T1-32B-Preview": ModelConfig(
        model_name="NovaSky-AI/Sky-T1-32B-Preview",
        display_name="Sky-T1-32B-Preview (Prompt)",
        url="https://huggingface.co/NovaSky-AI/Sky-T1-32B-Preview",
        org="NovaSky-AI",
        license="apache-2.0",
        model_handler=QwenHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "tiiuae/Falcon3-10B-Instruct-FC": ModelConfig(
        model_name="tiiuae/Falcon3-10B-Instruct-FC",
        display_name="Falcon3-10B-Instruct (FC)",
        url="https://huggingface.co/tiiuae/Falcon3-10B-Instruct",
        org="TII UAE",
        license="falcon-llm-license",
        model_handler=Falcon3FCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "tiiuae/Falcon3-7B-Instruct-FC": ModelConfig(
        model_name="tiiuae/Falcon3-7B-Instruct-FC",
        display_name="Falcon3-7B-Instruct (FC)",
        url="https://huggingface.co/tiiuae/Falcon3-7B-Instruct",
        org="TII UAE",
        license="falcon-llm-license",
        model_handler=Falcon3FCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "tiiuae/Falcon3-3B-Instruct-FC": ModelConfig(
        model_name="tiiuae/Falcon3-3B-Instruct-FC",
        display_name="Falcon3-3B-Instruct (FC)",
        url="https://huggingface.co/tiiuae/Falcon3-3B-Instruct",
        org="TII UAE",
        license="falcon-llm-license",
        model_handler=Falcon3FCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "tiiuae/Falcon3-1B-Instruct-FC": ModelConfig(
        model_name="tiiuae/Falcon3-1B-Instruct-FC",
        display_name="Falcon3-1B-Instruct (FC)",
        url="https://huggingface.co/tiiuae/Falcon3-1B-Instruct",
        org="TII UAE",
        license="falcon-llm-license",
        model_handler=Falcon3FCHandler,
        input_price=None,
        output_price=None,
        is_fc_model=True,
        underscore_to_dot=False,
    ),
    "uiuc-convai/CoALM-8B": ModelConfig(
        model_name="uiuc-convai/CoALM-8B",
        display_name="CoALM-8B",
        url="https://huggingface.co/uiuc-convai/CoALM-8B",
        org="UIUC + Oumi",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "uiuc-convai/CoALM-70B": ModelConfig(
        model_name="uiuc-convai/CoALM-70B",
        display_name="CoALM-70B",
        url="https://huggingface.co/uiuc-convai/CoALM-70B",
        org="UIUC + Oumi",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "uiuc-convai/CoALM-405B": ModelConfig(
        model_name="uiuc-convai/CoALM-405B",
        display_name="CoALM-405B",
        url="https://huggingface.co/uiuc-convai/CoALM-405B",
        org="UIUC + Oumi",
        license="Meta Llama 3 Community",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "BitAgent/BitAgent-8B": ModelConfig(
        model_name="BitAgent/BitAgent-8B",
        display_name="BitAgent-8B",
        url="https://huggingface.co/BitAgent/BitAgent-8B/",
        org="Bittensor",
        license="Apache-2.0",
        model_handler=LlamaHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "ThinkAgents/ThinkAgent-1B": ModelConfig(
        model_name="ThinkAgents/ThinkAgent-1B",
        display_name="ThinkAgent-1B (FC)",
        url="https://huggingface.co/ThinkAgents/ThinkAgent-1B",
        org="ThinkAgents",
        license="apache-2.0",
        model_handler=ThinkAgentHandler,
        input_price=None,
        output_price=None,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
}

# Inference through third-party inference platforms for open-source models
third_party_inference_model_map = {
    # Novita AI
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-novita": ModelConfig(
        model_name="meta-llama/llama-4-maverick-17b-128e-instruct-fp8-novita",
        display_name="Llama-4-Maverick-17B-128E-Instruct-FP8 (Prompt) (Novita)",
        url="https://huggingface.co/meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=NovitaHandler,
        input_price=0.2,
        output_price=0.85,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-FC-novita": ModelConfig(
        model_name="meta-llama/llama-4-maverick-17b-128e-instruct-fp8-FC-novita",
        display_name="Llama-4-Maverick-17B-128E-Instruct-FP8 (FC) (Novita)",
        url="https://huggingface.co/meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=NovitaHandler,
        input_price=0.2,
        output_price=0.85,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "meta-llama/llama-4-scout-17b-16e-instruct-novita": ModelConfig(
        model_name="meta-llama/llama-4-scout-17b-16e-instruct-novita",
        display_name="Llama-4-Scout-17B-16E-Instruct (Prompt) (Novita)",
        url="https://huggingface.co/meta-llama/Llama-4-Scout-17B-16E-Instruct",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=NovitaHandler,
        input_price=0.1,
        output_price=0.5,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
    "meta-llama/llama-4-scout-17b-16e-instruct-FC-novita": ModelConfig(
        model_name="meta-llama/llama-4-scout-17b-16e-instruct-FC-novita",
        display_name="Llama-4-Scout-17B-16E-Instruct (FC) (Novita)",
        url="https://huggingface.co/meta-llama/Llama-4-Scout-17B-16E-Instruct",
        org="Meta",
        license="Meta Llama 4 Community",
        model_handler=NovitaHandler,
        input_price=0.1,
        output_price=0.5,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen/qwq-32b-FC-novita": ModelConfig(
        model_name="qwen/qwq-32b-FC-novita",
        display_name="Qwen/QwQ-32B (FC) (Novita)",
        url="https://huggingface.co/Qwen/QwQ-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=NovitaHandler,
        input_price=0.18,
        output_price=0.2,
        is_fc_model=True,
        underscore_to_dot=True,
    ),
    "qwen/qwq-32b-novita": ModelConfig(
        model_name="qwen/qwq-32b-novita",
        display_name="Qwen/QwQ-32B (Prompt) (Novita)",
        url="https://huggingface.co/Qwen/QwQ-32B",
        org="Qwen",
        license="apache-2.0",
        model_handler=NovitaHandler,
        input_price=0.18,
        output_price=0.2,
        is_fc_model=False,
        underscore_to_dot=False,
    ),
}


MODEL_CONFIG_MAPPING = {
    **api_inference_model_map,
    **local_inference_model_map,
    **third_party_inference_model_map,
}
