{"question_id": 1, "text": " Design a feature for a social media website to recommend articles to users based on how similar the articles are to their previously liked articles.\\n", "category": "generic"}
{"question_id": 2, "text": " The user is interested in a tool to find relationships between medical terms.\\n", "category": "generic"}
{"question_id": 3, "text": " As a journalist, I am curious about speech sentiment analysis in a group of people in a crowd. I want to extract features from the audio to run sentiment analysis.\\n", "category": "generic"}
{"question_id": 4, "text": " A chat service needs a way to compare and cluster similar sentences from users in different languages. Find a suitable feature extraction method to achieve this.\\n", "category": "generic"}
{"question_id": 5, "text": " I am an interior designer and want to showcase a modern living room with a fireplace and a large window overlooking a forest. Create an image according to this description.\\n", "category": "generic"}
{"question_id": 6, "text": " We need a product description for an image-based online store platform that will help customers understand the specifics of the product.\\n", "category": "generic"}
{"question_id": 7, "text": " Create a program to generate a description for an image provided as input.\\n", "category": "generic"}
{"question_id": 8, "text": " I am a financial analyst, and I receive report after report filled with charts helping to explain trends and data in my field. However, I also need to have this information in tabular format. Please help me extract a linearized table from this chart.\\n", "category": "generic"}
{"question_id": 9, "text": " We are building an automatic video generation platform based on user-provided text. We need a reliable model to convert text instructions into appropriate videos.\\n", "category": "generic"}
{"question_id": 10, "text": " How can I extract video content from a text file? Provide a code sample to generate the video based on the text.\\n", "category": "generic"}
{"question_id": 11, "text": " We are developing a mobile app to demonstrate the AI's ability to generate a short video from text. The app focuses on processing written stories into video.\\n", "category": "generic"}
{"question_id": 12, "text": " Hey, I want to analyze images in my phone gallery and answer questions about them.\\n", "category": "generic"}
{"question_id": 13, "text": " My company wants to develop an application that will analyze images in relation to food and answer questions about them. We want it to handle questions like \\\"what is in the dish\\\" and \\\"how many calories does it have\\\".\\n", "category": "generic"}
{"question_id": 14, "text": " We have received an invoice document, and would like to extract the total amount from it.\\n###Input: {'question': 'What is the total amount?', 'context': 'Invoice information for order ABC_123\\\\nProduct: Widget A, Quantity: 10, Price: $5 each\\\\nProduct: Widget B, Quantity: 5, Price: $3 each\\\\nProduct: Widget C, Quantity: 15, Price: $2 each\\\\nSubtotal: $75, Tax: $6.38, Total Amount Due: $81.38'}\\n", "category": "generic"}
{"question_id": 15, "text": " As a clerk in a school, you want to extract information from some student enrollment forms. These forms contain students' details such as Name, age, and address.\\n", "category": "generic"}
{"question_id": 16, "text": " Find a model that can be used to predict the properties of molecules based on their graph representations.\\n", "category": "generic"}
{"question_id": 17, "text": " Estimate the depth of a pool using computational depth estimation, given an underwater photo.\\n", "category": "generic"}
{"question_id": 18, "text": " I need technology that can analyze images and estimate their depth in a single camera.\\n", "category": "generic"}
{"question_id": 19, "text": " The client is a real estate company working on virtual tours. We need to help them estimate depth in images of houses.\\n", "category": "generic"}
{"question_id": 20, "text": " Assist me in setting up an image classifier that can recognize objects within an image.\\n", "category": "generic"}
{"question_id": 21, "text": " Identify an object within an image based on textual description. For example, find a dog in the image.\\n", "category": "generic"}
{"question_id": 22, "text": " Our client is an AI gaming company and we need to develop a bot for the game Valorant. The bot should detect objects like dropped spike, enemy, planted spike, and teammate within the game.\\n", "category": "generic"}
{"question_id": 23, "text": " A client from real estate agency needs to get a list of objects present in a series of pictures to prepare their property listings.\\n", "category": "generic"}
{"question_id": 24, "text": " We are developing an application for smartphones which automatically separates elements in a user's photo, and we need to implement this feature.\\n", "category": "generic"}
{"question_id": 25, "text": " I have a picture of a room demonstrating a mixture of objects. The model needs to seperate the objects and label them accordingly.\\n", "category": "generic"}
{"question_id": 26, "text": " We want to randomly generate high-quality images of celebrity faces.\\n", "category": "generic"}
{"question_id": 27, "text": " Generate a new image based on the online database of bedroom art.\\n", "category": "generic"}
{"question_id": 28, "text": " I run an online store that sells butterfly-themed products. Please generate an image of a cute butterfly for our social media page.\\n", "category": "generic"}
{"question_id": 29, "text": " We need a video-based AI model for security purposes. We want the AI to check and categorize footage based on existing security guidelines.\\n", "category": "generic"}
{"question_id": 30, "text": " A new project demands to classify videos for a social media platform. Let us create a video classification pipeline.\\n", "category": "generic"}
{"question_id": 31, "text": " I am an insurance adjustor. I need a zero-shot image classifier that will tell me whether a car has been involved in a major accident or had minor damages.\\n", "category": "generic"}
{"question_id": 32, "text": " I want to analyze a medical image to find out if it's an X-ray, an MRI scan, or a CT scan.\\n", "category": "generic"}
{"question_id": 33, "text": " We are building a quiz application where the image will be shown, and we have to choose a dressings matching that image. Please help in classifying the image.\\n", "category": "generic"}
{"question_id": 34, "text": " We're developing a chatbot that can quickly identify and describe images for our Chinese-speaking users.\\n", "category": "generic"}
{"question_id": 35, "text": " We would like to understand the sentiment of user's messages in a customer support chat system.\\n", "category": "generic"}
{"question_id": 36, "text": " As a book store owner, I want to classify customer reviews into positive and negative sentiments.\\n", "category": "generic"}
{"question_id": 37, "text": " I am the owner of a news website. I have several consumers' comments about our publishing news. I want to analyze the sentiments of these comments.\\n", "category": "generic"}
{"question_id": 38, "text": " Our business is expanding to international markets. Analyze the sentiment of the following customer review to better understand their satisfaction with our product: \\\"\\u00a1Esto es maravilloso! Me encanta.\\\"\\n###Input: \\\"\\u00a1Esto es maravilloso! Me encanta.\\\"\\n", "category": "generic"}
{"question_id": 39, "text": " We are a forum moderator team looking for a solution to classify comments into toxic or non-toxic categories.\\n", "category": "generic"}
{"question_id": 40, "text": " My company is launching a social media campaign. We need an AI-based system that would automatically analyze the sentiment of any user-generated reviews or tweets concerning our product.\\n", "category": "generic"}
{"question_id": 41, "text": " I have jobs descriptions in French for sales manager, please highlight names of organizations or cities within the text.\\n###Input: \\\"La soci\\u00e9t\\u00e9 de Paris est sp\\u00e9cialis\\u00e9e dans la vente de v\\u00e9hicules \\u00e9lectriques. Responsable des ventes, vous travaillerez au sein d'une \\u00e9quipe dynamique dans l'agence de Lyon. Vous \\u00eates charg\\u00e9(e) de d\\u00e9velopper le portefeuille client et d'assurer la satisfaction des clients existants. Dans ce contexte, vous devrez travailler en lien \\u00e9troit avec le directeur commercial et les autres \\u00e9quipes de l'entreprise. Une exp\\u00e9rience pr\\u00e9alable chez Renault est un atout.\\\"\\n", "category": "generic"}
{"question_id": 42, "text": " In order to have a better understanding of our clients, I'd like to identify the names of people and organizations mentioned in the following customer review.\\n###Input: \\\"I recently purchased a MacBook Pro from Apple Inc. and had a fantastic customer support experience. John from their tech support team was incredibly helpful and professional.\\\"\\n", "category": "generic"}
{"question_id": 43, "text": " I am building a social media app that requires people to write fascinating stories rather than boring sentences. Detect named entities in a sentence by using an NER model.\\n", "category": "generic"}
{"question_id": 44, "text": " We have a large dataset of customer orders in the form of a table. Help us answer questions about this data.\\n", "category": "generic"}
{"question_id": 45, "text": " Gather information about annual income and age demographics of employees to predict retirement patterns. Make sure to identify top employees for potential promotions.\\n", "category": "generic"}
{"question_id": 46, "text": " To track our sales data, we need to find total sales of a specific product based on a table containing sales information per week.\\n", "category": "generic"}
{"question_id": 47, "text": " I have a table containing information about various animals and their important characteristics. I need the system to answer a query to provide information about the tallest animal in the table.\\n", "category": "generic"}
{"question_id": 48, "text": " You are building an app that allows users to find quick answers to textbook questions. Users will send a message with the question, and the answer should be detected directly from the textbook content.\\n", "category": "generic"}
{"question_id": 49, "text": " We want to make sure clarify some questions about the legal implications of a new partnership contract for a real estate development project.\\n###Input: We hereby grant the Licensee the exclusive right to develop, construct, operate and promote the Project, as well as to manage the daily operations of the Licensed Facilities during the Term. In consideration for the grant of the License, the Licensee shall pay to the Licensor the full amount of Ten Million (10,000,000) Dollars within thirty (30) days after the execution hereof.\\n", "category": "generic"}
{"question_id": 50, "text": " Help me setup a tinyroberta model from deepset for Question and Answer. Provide a sample input and output.\\n", "category": "generic"}
{"question_id": 51, "text": " I want to build a tool to answer questions automatically from a given document. Which model do you recommend for this task?\\n", "category": "generic"}
{"question_id": 52, "text": " We have a French news agency and we want to categorize the news articles based on sports, politics, and science.\\n", "category": "generic"}
{"question_id": 53, "text": " I need a solution to detect whether a piece of news is talking about technology, sports, or politics.\\n", "category": "generic"}
{"question_id": 54, "text": " I want to build a chatbot that is used by language learners who want to communicate in French while they only know English. Generate a response for an English message.\\n", "category": "generic"}
{"question_id": 55, "text": " Translate the following text from French to English: \\u201cLe syst\\u00e8me \\u00e9ducatif fran\\u00e7ais est compos\\u00e9 d'\\u00e9coles maternelles, d'\\u00e9coles \\u00e9l\\u00e9mentaires, de coll\\u00e8ges et de lyc\\u00e9es.\\u201d\\n###Input: Le syst\\u00e8me \\u00e9ducatif fran\\u00e7ais est compos\\u00e9 d'\\u00e9coles maternelles, d'\\u00e9coles \\u00e9l\\u00e9mentaires, de coll\\u00e8ges et de lyc\\u00e9es.\\n", "category": "generic"}
{"question_id": 56, "text": " I want to translate a text from one language to another.\\n", "category": "generic"}
{"question_id": 57, "text": " Our team member has written a long article that needs to be published on a company blog. Can you provide a shorter summary to be used as a snippet on the landing page?\\n###Input: \\\"Apple Inc. reported its quarterly earnings results yesterday. The company posted a record-breaking revenue of $123.9 billion for the first quarter of 2022, up by 11% from the same period last year. The increase was fueled by stronger demand for iPhones, iPads, and Macs, as well as continued growth in its services segment. Apple's operating profit for the quarter came in at $38.3 billion, up 17% from a year earlier. The results surpassed analysts' expectations, who had anticipated revenue of around $118 billion. This strong performance is largely attributed to the successful launch of the iPhone 13, which has enjoyed robust sales since its debut in September. Apple CEO Tim Cook said in a statement, \\\"Our record-breaking quarter reflects the strength of our entire ecosystem, from our innovative products and services to the unmatched dedication of our teams around the world.\\\" Despite the ongoing global supply chain disruptions, Apple has managed to maintain its growth trajectory, thanks in part to its vertically integrated operations and nimble supply chain management. The company is expected to face stiffer competition going forward, particularly in the smartphone market, as rivals introduce new devices and increased pricing pressures.\\\"\\n", "category": "generic"}
{"question_id": 58, "text": " Write a summary of a conference held by the World Health Organization discussing the impacts of climate change on human health.\\n###Input: Over the past week, the World Health Organization held a conference discussing the impacts of climate change on human health. The conference brought together leading experts from around the world to examine the current problems affecting people's health due to changing environmental conditions. The topics of discussion included increased occurrence of heat-related illnesses, heightened rates of vector-borne diseases, and the growing problem of air pollution. The conference concluded with a call to action for governments and organizations to invest in mitigating and adapting to the negative consequences of climate change for the sake of public health.\\n", "category": "generic"}
{"question_id": 59, "text": " Please provide a brief overview of a news article.\\n###Input: A new study suggests that eating chocolate at least once a week can lead to better cognition. The study, published in the journal Appetite, analyzed data from over 900 adults and found that individuals who consumed chocolate at least once a week performed better on cognitive tests than those who consumed chocolate less frequently. Researchers believe that the beneficial effects of chocolate on cognition may be due to the presence of flavonoids, which have been shown to be antioxidant-rich and to improve brain blood flow.\\n", "category": "generic"}
{"question_id": 60, "text": " I developed a document generation app, I need to create a summary of a long article given as input to provide to my users before they read the full article.\\n", "category": "generic"}
{"question_id": 61, "text": " We need a quick summary of a news article we found online. Can you help us with that?\\n###Input: Videos that say approved vaccines are dangerous and cause autism, cancer or infertility are among those that will be taken down, the company said. The policy includes the termination of accounts of anti-vaccine influencers. Tech giants have been criticised for not doing more to counter false health information on their sites. In July, US President Joe Biden said social media platforms were largely responsible for people's scepticism in getting vaccinated by spreading misinformation, and appealed for them to address the issue. YouTube, which is owned by Google, said 130,000 videos were removed from its platform since last year, when it implemented a ban on content spreading misinformation about Covid vaccines. In a blog post, the company said it had seen false claims about Covid jabs spill over into misinformation about vaccines in general. The new policy covers long-approved vaccines, such as those against measles or hepatitis B. We're expanding our medical misinformation policies on YouTube with new guidelines on currently administered vaccines that are approved and confirmed to be safe and effective by local health authorities and the WHO, the post said, referring to the World Health Organization.\\n", "category": "generic"}
{"question_id": 62, "text": " We'd like our chatbot to act as a fictional character for engaging with our users.\\n", "category": "generic"}
{"question_id": 63, "text": " Write a story about a spaceship journey to a distant planet in search of a new home for humanity.\\n", "category": "generic"}
{"question_id": 64, "text": " I want to write a story about a brave knight and a dragon but I'm unable to come up with a good start. Help me with that.\\n", "category": "generic"}
{"question_id": 65, "text": " I need a text analysis tool that can automatically predict the most plausible missing text in a given sentence.\\n", "category": "generic"}
{"question_id": 66, "text": " Help me fill in the blanks in the following Chinese sentence: \\\"\\u4e0a\\u6d77\\u662f\\u4e2d\\u56fd\\u7684[MASK]\\u5927\\u57ce\\u5e02\\u3002\\\"\\n###Input: \\u4e0a\\u6d77\\u662f\\u4e2d\\u56fd\\u7684[MASK]\\u5927\\u57ce\\u5e02\\u3002\\n", "category": "generic"}
{"question_id": 67, "text": " We are building a source code autocompletion tool which will complete the code snippet containing a masked token.\\n", "category": "generic"}
{"question_id": 68, "text": " I work for a Japanese company, and my manager needs me to take a look at a request from a client. I can understand fluent Japanese, but I need a little help filling in missing words from the text.\\n", "category": "generic"}
{"question_id": 69, "text": " We are building a platform to compare and contrast user input sentences with existing sentences in our database. It should return similar results.\\n", "category": "generic"}
{"question_id": 70, "text": " I need a method to compare the similarity between two sentences to be used within a meme generator, so we can produce a meme with a similar caption.\\n", "category": "generic"}
{"question_id": 71, "text": " A student is writing a research paper and needs help with finding similar articles in order to include them in the literature review section.\\n", "category": "generic"}
{"question_id": 72, "text": " Create a solution to convert a given Japanese sentence into a speech audio file.\\n", "category": "generic"}
{"question_id": 73, "text": " We are working on a transcription service for our customers. We need a way to convert audio files into text.\\n", "category": "generic"}
{"question_id": 74, "text": " We are creating an online video conference service, and we need to detect when two or more speakers are speaking at the same time in the audio.\\n", "category": "generic"}
{"question_id": 75, "text": " Our company develops smart speaker devices that involve interaction with the user. We need to transcribe the input from the users with the maintained accent or language.\\n", "category": "generic"}
{"question_id": 76, "text": " One of our clients is facing noise issues on their audio recordings. Can you help them to remove the noise from the audio?\\n", "category": "generic"}
{"question_id": 77, "text": " We are a media company and we have a large volume of Chinese language audio files. We want to transcribe the audios into chinese text.\\n", "category": "generic"}
{"question_id": 78, "text": " Help us improve the listener experience from our customers by enhancing the audio of noisy recordings.\\n", "category": "generic"}
{"question_id": 79, "text": " Our company is working on a project to automatically translate spoken English audio to spoken Hokkien audio. We need a speech-to-speech translation model.\\n", "category": "generic"}
{"question_id": 80, "text": " We are a startup developing voice assistants. We need a keyword spotting system that can recognize user commands.\\n", "category": "generic"}
{"question_id": 81, "text": " The model needs to have speech recognition capability to identify languages in a given audio file.\\n", "category": "generic"}
{"question_id": 82, "text": " I have just recorded a meeting, I want to find the best segments from the audio where people are speaking, and construct a summary.\\n", "category": "generic"}
{"question_id": 83, "text": " I am running a wine store, and I am looking for a machine learning model that can help me classify the quality of wine based on some given features.\\n", "category": "generic"}
{"question_id": 84, "text": " Build a simple application to predict the survival status of passengers on the Titanic based on their age, gender, and passenger class.\\n", "category": "generic"}
{"question_id": 85, "text": " I need to estimate CO2 emissions from vehicles based on their characteristics, such as engine size, transmission type, and miles traveled.\\n", "category": "generic"}
{"question_id": 86, "text": " We have been asked to predict future criminal re-offense from a given dataset. What model should we adopt and how do we proceed?\\n", "category": "generic"}
{"question_id": 87, "text": " Our company's goal is to predict carbon emissions based on the given features of the compound.\\n", "category": "generic"}
{"question_id": 88, "text": " The factory wants to make its production process more eco-friendly. Calculate the carbon emissions for given data.\\n", "category": "generic"}
{"question_id": 89, "text": " We want to predict the carbon emissions of a new line of electric vehicles for an annual report. Automate the process of loading a regression model, then calculate the forecast of emissions for this year.\\n", "category": "generic"}
{"question_id": 90, "text": " We are planning to launch a website which provides tips to people for their daily lives. Can you please build a model to predict the appropriate amount of tips?\\n", "category": "generic"}
{"question_id": 91, "text": " We have a robotic arm in our warehouse that needs to be trained to optimize loading and unloading tasks. The robotic arm is based on the CartPole environment.\\n", "category": "generic"}
{"question_id": 92, "text": " There is an upcoming event called \\\"Space Party\\\" and we need a representative image for the event. Can you assist us in creating an image containing a party in space with astronauts and aliens having fun together?\\n", "category": "generic"}
{"question_id": 93, "text": " We're creating a promotional image for a wildlife-themed event. We need to display two tigers in a natural setting.\\n", "category": "generic"}
{"question_id": 94, "text": " We have a collection of low-resolution images of movie characters, and we need to upscale those images to get a more detailed high-resolution image.\\n", "category": "generic"}
{"question_id": 95, "text": " I want you to create a function that generates captions for a list of images.\\n", "category": "generic"}
{"question_id": 96, "text": " We need a tool to help us generate textual descriptions for images and videos related to our product.\\n", "category": "generic"}
{"question_id": 97, "text": " We need to build an AI-powered tool to assist visually impaired users in understanding their surroundings by answering questions about images.\\n", "category": "generic"}
{"question_id": 98, "text": " I'm a nutritionist and want to help my clients by answering questions about their meals. They will send me an image of their food and ask me a question about it, like \\\"Is this vegan?\\\" or \\\"How many calories do you think it contains?\\\"\\n", "category": "generic"}
{"question_id": 99, "text": " Our client is a legal firm that needs assistance in extracting specific information from a large number of legal documents. Automate the process of answering questions related to these documents.\\n", "category": "generic"}
{"question_id": 100, "text": " In a healthcare company, we are trying to create an automated system for answering patient-related questions based on their medical documents. We need a solution using NLP.\\n", "category": "generic"}
{"question_id": 101, "text": " Develop a program which can answer questions related to a scanned document.\\n", "category": "generic"}
{"question_id": 102, "text": " I have received a PDF document and a question. My task is to find the answer part in the document.\\n", "category": "generic"}
{"question_id": 103, "text": " An interior design firm builds a software to understand the depth of rooms captured in photographs for remodeling activities.\\n", "category": "generic"}
{"question_id": 104, "text": " We are running an autonomous vehicle company and want to implement a depth estimation module for the real-time video feed captured by our camera.\\n", "category": "generic"}
{"question_id": 105, "text": " Our team wants to create a new app for autonomous vehicles. For that, we need to estimate the depth of the field from images.\\n", "category": "generic"}
{"question_id": 106, "text": " As a city planner, I need to measure the depth of spaces in a series of images taken from streets.\\n", "category": "generic"}
{"question_id": 107, "text": " In our online ecommerce platform, we want to build an AI app to automatically recognize the type of products. It should be able to identify common items like clothing, electronics, furniture, and more.\\n", "category": "generic"}
{"question_id": 108, "text": " We need to recognize the breed of dog in the given image.\\n", "category": "generic"}
{"question_id": 109, "text": " Develop a solution that can categorize an image of a cell phone, laptop, or smartwatch as one of these respective device types.\\n", "category": "generic"}
{"question_id": 110, "text": " Build a system to help companies identify logos from a collection of images.\\n", "category": "generic"}
{"question_id": 111, "text": " Develop a pipeline that detects objects present in an image using computer vision.\\n", "category": "generic"}
{"question_id": 112, "text": " Assit me to process and segment an image for further analysis.\\n", "category": "generic"}
{"question_id": 113, "text": " We need to analyze satellite images to categorize the types of land use. For this purpose, I need to segment the images and identify different objects.\\n", "category": "generic"}
{"question_id": 114, "text": " We are a city planning department and want to evaluate the city layout. Analyze the image we provide to segment and understand the various urban elements.\\n", "category": "generic"}
{"question_id": 115, "text": " My company develops drones for agriculture purposes, and we need a model to segment aerial images accurately.\\n", "category": "generic"}
{"question_id": 116, "text": " I want to generate images from text descriptions and use the scribble images as control inputs for my project.\\n", "category": "generic"}
{"question_id": 118, "text": " We want to recommend workouts to our users, based on the type of sports they enjoy. Help us classify sports videos.\\n", "category": "generic"}
{"question_id": 119, "text": " We need to classify videos showing different actions for our new video moderation system.\\n", "category": "generic"}
{"question_id": 120, "text": " I need the AI to tell if an image is of a cat or a dog, regardless of its breed or species.\\n", "category": "generic"}
{"question_id": 121, "text": " We have a set of pictures for pets (dogs and cats). We need to offer an AI-based solution to classify the pictures given the pet name.\\n", "category": "generic"}
{"question_id": 122, "text": " Our startup team is now building an app for diagnosing plant diseases based on images. We need to get the diagnosis for different types of plant issues.\\n", "category": "generic"}
{"question_id": 123, "text": " We need to analyze customer reviews and find out how well our new product is doing in the market.\\n", "category": "generic"}
{"question_id": 124, "text": " A new tutoring company is founded, and they want a tutoring AI. To do so, they need help in creating better explanations for a chemistry concept.\\n", "category": "generic"}
{"question_id": 125, "text": " Create a function that can determine if a given text is a question or a statement.\\n", "category": "generic"}
{"question_id": 126, "text": " I want to create a system that can answer questions by sorting out possible answers to a question.\\n", "category": "generic"}
{"question_id": 127, "text": " We have a news article and we need to extract all the entities like the names of people, organizations, and locations.\\n", "category": "generic"}
{"question_id": 128, "text": " We are purchasing a CRM system to keep track of our customers and their organizations. We want to extract useful entities from customer emails automatically.\\n", "category": "generic"}
{"question_id": 129, "text": " As a researcher, I am trying to find an answer to my question in a table containing information about animals and their characteristics.\\n", "category": "generic"}
{"question_id": 130, "text": " A teacher wants to create a quiz for her students. We are now working on the questions and answers for the quiz that be arranged in a table format.\\n", "category": "generic"}
{"question_id": 131, "text": " I work for a financial company that stores all of its data in tables. We need a way to extract key information efficiently by asking natural language questions.\\n", "category": "generic"}
{"question_id": 132, "text": " We have a dataset related to coffee and tea prices. We need to answer a question on who sells hot chocolate and their prices.\\n###Input: {\\\"table\\\": [[\\\"Shop\\\", \\\"Drink\\\", \\\"Price\\\"], [\\\"Cafe A\\\", \\\"Coffee\\\", \\\"3.00\\\"], [\\\"Cafe B\\\", \\\"Tea\\\", \\\"2.50\\\"], [\\\"Cafe C\\\", \\\"Hot Chocolate\\\", \\\"4.50\\\"], [\\\"Cafe D\\\", \\\"Hot Chocolate\\\", \\\"3.75\\\"]], \\\"queries\\\": [\\\"Which shops sell hot chocolate and what are their prices?\\\"]}\\n", "category": "generic"}
{"question_id": 133, "text": " A company is running a survey and they want to know how many respondents have given a specific answer for each question of the survey.\\n", "category": "generic"}
{"question_id": 134, "text": " Extract information about a non-compete clause from a legal document with a context related to data protection.\\n###Input: \\\"The data protection provisions set forth in this agreement shall be in effect for a period of 2 years after the termination of services. The non-compete clause states that the service provider is prohibited from providing similar services to any competitor within a 50-mile radius and during the 1-year period following termination of services.\\\"\\n", "category": "generic"}
{"question_id": 135, "text": " Tell me the day of the game when it was played given the following context: \\\"The game was played on February 7, 2016 at Levi's Stadium in the San Francisco Bay Area at Santa Clara, California.\\\"\\n###Input: {'context': \\\"The game was played on February 7, 2016 at Levi's Stadium in the San Francisco Bay Area at Santa Clara, California.\\\", 'question': \\\"What day was the game played on?\\\"}\\n", "category": "generic"}
{"question_id": 136, "text": " We need to identify the relationship between two sentences whether they are contradictory, entail each other, or neutral.\\n", "category": "generic"}
{"question_id": 137, "text": " I am a climate change agency, looking to have my research summaries translated into Chinese for international audiences.\\n", "category": "generic"}
{"question_id": 138, "text": " We are building an app to summarize long articles for users. We need a solution to create a condensed summary of the given text.\\n", "category": "generic"}
{"question_id": 139, "text": " A news agency wants to summaries their international news articles daily.\\n", "category": "generic"}
{"question_id": 140, "text": " Design a smart home system that can have conversations with the user for controlling the home appliances.\\n", "category": "generic"}
{"question_id": 141, "text": " An educational publishing company is developing a language comprehension program for elementary school students. They want a system that can generate a short story based on a given prompt.\\n", "category": "generic"}
{"question_id": 142, "text": " Can you initiate a conversation with an AI model that plays the role of a friend who just got back from a conference?\\n", "category": "generic"}
{"question_id": 143, "text": " As a software developer, I want a program that can automatically generate code snippets for me based on natural language descriptions.\\n", "category": "generic"}
{"question_id": 144, "text": " Provide a short summary of an article about cryptocurrency investment risks.\\n###Input: Cryptocurrencies have become exceedingly popular among investors seeking higher returns and diversification in their portfolios. However, investing in these digital currencies carries several inherent risks. Market volatility is a major factor \\u2013 cryptocurrencies can experience wild price swings, sometimes even within hours or minutes. This high volatility makes it difficult to predict the future value of the investments and can result in significant losses. Furthermore, the lack of regulatory oversight and security concerns may also lead to potential frauds and hacks, exposing investors to additional risk. Lastly, the environmental impact of mining digital currencies like Bitcoin has come under scrutiny, questioning the long-term sustainability of the cryptocurrency market.\\n", "category": "generic"}
{"question_id": 145, "text": " You have just met a person that speaks French. As a hotel manager, you need to tell them, \\\"Welcome to our hotel, we hope you enjoy your stay.\\\" in French.\\n", "category": "generic"}
{"question_id": 146, "text": " They are planning a trip to Germany and want to spend some leisure time in the parks of Munich, find out how to ask a question about the location of parks in Munich in German.\\n", "category": "generic"}
{"question_id": 147, "text": " We are a company offering speech to text services. We need to summarize the conversion and make it open-ended question.\\n", "category": "generic"}
{"question_id": 148, "text": " To help me with my writing, I need an AI tool that can fill in the gaps for me when I get stuck. It should be able to complete phrases or sentences.\\n", "category": "generic"}
{"question_id": 149, "text": " A writer needs help with generating the next word in the phrase \\\"The dog jumped over the\\\" __.\\n", "category": "generic"}
{"question_id": 151, "text": " Help me find similarity scores for different restaurant reviews.\\n", "category": "generic"}
{"question_id": 152, "text": " Our company is building an automated assistance system for the visually impaired. We need a tool to convert text into spoken instructions.\\n", "category": "generic"}
{"question_id": 153, "text": " I would like to create an application where users may listen to translations of English sentences. I need a Text-to-Speech model to support this functionality.\\n", "category": "generic"}
{"question_id": 154, "text": " We are a language learning app platform. We need a text-to-speech tool to read a sentence in multiple languages for our users.\\n", "category": "generic"}
{"question_id": 155, "text": " I need to create an audio output that translates the given text to speech for a French audiobook assistant.\\n", "category": "generic"}
{"question_id": 156, "text": " We are opening a platform where users can record their own podcast and host it on our platform, can you help us to convert the audio into text automatically?\\n", "category": "generic"}
{"question_id": 157, "text": " We are currently working on the development of a speech-to-text application for transcription purposes. Can you help generate the required transcription code?\\n", "category": "generic"}
{"question_id": 158, "text": " We are building an app for tour guides to transcribe what they say during the tour. Then the transcript will be translated into sign language.\\n", "category": "generic"}
{"question_id": 159, "text": " I am an English teacher. I have recently met a Hokkien speaker. I am looking to translate a conversational sentence from English to Hokkien to facilitate communication.\\n", "category": "generic"}
{"question_id": 160, "text": " Determine the keyword spoken in a recorded audio file.\\n", "category": "generic"}
{"question_id": 161, "text": " Determine which speaker an audio segment belongs to using the provided audio file.\\n", "category": "generic"}
{"question_id": 162, "text": " We have a database of customer voices and are trying to build a voice recognition product so we can recognize customer voices when they call. How should we process and classify?\\n", "category": "generic"}
{"question_id": 163, "text": " We are developing a voice-controlled drone. Please identify the spoken command in the audio clip provided.\\n###Input: \\\"audio_clip.wav\\\"\\n", "category": "generic"}
{"question_id": 164, "text": " Make a summary video for our last team meeting. The audio from the video must identify who is speaking and when.\\n", "category": "generic"}
{"question_id": 165, "text": " I want to estimate the price of a house based on its features using this API. Please provide the code.\\n", "category": "generic"}
{"question_id": 166, "text": " Our company wants to predict housing prices in the US based on given features. Help us use the trained model to predict the prices.\\n", "category": "generic"}
{"question_id": 167, "text": " An environmental organization would like to use our Carbon Emissions prediction model to estimate CO2 emissions of different configurations of vehicles.\\n", "category": "generic"}
{"question_id": 168, "text": " I am a data analyst working in pollution detection, find a model and develop a piece of code for me for environment monitoring.\\n", "category": "generic"}
{"question_id": 169, "text": " We want to develop an intelligent prosthetic leg that can improve walking. Use a decision transformer to predict actions to be taken.\\n", "category": "generic"}
{"question_id": 170, "text": " You want to create a bot that can play the Pong No Frameskip-v4 game with exceptional skill.\\n", "category": "generic"}
{"question_id": 171, "text": " I am a game developer working on a game project involving moving carts. I need to use reinforcement learning to improve the game experience.\\n", "category": "generic"}
{"question_id": 172, "text": " Develop a soccer playing agent that can outperform its opponents in a 2v2 environment.\\n", "category": "generic"}
{"question_id": 173, "text": " We are tasked to analyze text for a Russian newspaper to help understand general sentiment and trends in the text.\\n", "category": "generic"}
{"question_id": 174, "text": " We want to generate an image from a textual description for our PowerPoint presentation.\\n", "category": "generic"}
{"question_id": 175, "text": " A new manga has been released and we would like to provide a manga reader app with translations. Get the text from the manga image.\\n", "category": "generic"}
{"question_id": 176, "text": " We are building a social media site which creates automatic captions for users when they post a picture\\n", "category": "generic"}
{"question_id": 177, "text": " There is robot in our factory which reads the image from the production line and then generate a text output based on the image.\\n", "category": "generic"}
{"question_id": 178, "text": " I am a filmmaker, and I need to make a short video based on a scene description from a script.\\n", "category": "generic"}
{"question_id": 179, "text": " I'm an author and want to create a short video based on a brief passage from my book. Can you generate a video based on this text?\\n", "category": "generic"}
{"question_id": 180, "text": " I want to build an AI model that can analyze images and answer questions about the content of the image.\\n", "category": "generic"}
{"question_id": 181, "text": " We are trying to develop an application that helps tourists get information about attractions by analyzing images they take and responding to questions.\\n", "category": "generic"}
{"question_id": 182, "text": " We have the financial documents of a company and we want to extract information about the cash flow. Modify the model so it can answer the questions related to the cash flow.\\n", "category": "generic"}
{"question_id": 183, "text": " Please generate a correct building plan leveraging the data given.\\n", "category": "generic"}
{"question_id": 184, "text": " Help our drone video analyzing app estimate the depth in drone footage.\\n", "category": "generic"}
{"question_id": 185, "text": " As a salesperson, I need to analyze customer invoices to answer questions about the total amount, tax, and due date from an image file.\\n", "category": "generic"}
{"question_id": 186, "text": " We need to find out the depth information of a room for monitoring purposes.\\n", "category": "generic"}
{"question_id": 187, "text": " We are creating an autonomous car and need to estimate the depth of objects in a given scene.\\n", "category": "generic"}
{"question_id": 188, "text": " Help us create an AI solution to automatically label images taken by a security camera.\\n", "category": "generic"}
{"question_id": 189, "text": " Develop a software to classify an image from a URL into a thousand categories.\\n", "category": "generic"}
{"question_id": 190, "text": " Our delivery drones need to detect and avoid obstacles while flying. Develop a solution for them to detect objects in their path.\\n", "category": "generic"}
{"question_id": 191, "text": " Develop a code to recognize objects in images using deformable-detr model.\\n", "category": "generic"}
{"question_id": 192, "text": " I need to extract tables from a set of scanned document images to simplify data analysis.\\n", "category": "generic"}
{"question_id": 193, "text": " Our customers wish to automatically detect shoplifters in the store using a surveillance camera. Help them to implement object detection and identify potential thieves.\\n", "category": "generic"}
{"question_id": 194, "text": " Create an object detector that can detect blood cells in an image, such as platelets, red blood cells, and white blood cells.\\n", "category": "generic"}
{"question_id": 195, "text": " I am a real-estate agent working on a project where I need to convert images of room plans to a better visual representation.\\n", "category": "generic"}
{"question_id": 196, "text": " We recently received low resolution images of newly released products and need to upscale them for better quality.\\n", "category": "generic"}
{"question_id": 197, "text": " As a toy company, we are designing a new toy line. We'd like you to create an image of a toy robot using relevant text prompts as control input.\\n", "category": "generic"}
{"question_id": 198, "text": " I'm developing a game that needs more Minecraft skins for some characters. How can I generete them with this model?\\n", "category": "generic"}
{"question_id": 199, "text": " Find me a generative model to create cat images in 256x256 resolution.\\n", "category": "generic"}
{"question_id": 200, "text": " Our organization works with video surveillance. We need a system to analyze the videos and classify various events happening inside the video.\\n", "category": "generic"}
{"question_id": 201, "text": " People in my company need an automatic solution to categorize videos based on their content. The system should be able to recognize the main theme of a video with high accuracy.\\n", "category": "generic"}
{"question_id": 202, "text": " We want to build a product to classify images of pets into different categories.\\n", "category": "generic"}
{"question_id": 203, "text": " We are developing an app to classify food images. We have a set of images and want to use a pre-trained model for classification.\\n", "category": "generic"}
{"question_id": 205, "text": " The company wants to use geolocalization techniques to identify the location of a given image.\\n", "category": "generic"}
{"question_id": 206, "text": " To improve customer service, the company is developing an AI-based chatbot that can help generate multiple paraphrases of a given customer query. We want to know if the generated paraphrases are adequate.\\n", "category": "generic"}
{"question_id": 207, "text": " We need to analyze user reviews for our app to determine whether they are positive, negative, or neutral.\\n", "category": "generic"}
{"question_id": 208, "text": " Give me a tool to detect named entities in multiple languages for my news portal analysis.\\n", "category": "generic"}
{"question_id": 209, "text": " We are building a fiction-sharing platform for multiple languages. Extract named entities from the given text.\\n", "category": "generic"}
{"question_id": 210, "text": " Someone asked the question \\\"What is the capital of Sweden?\\\" in the context \\\"Stockholm is the beautiful capital of Sweden, which is known for its high living standards and great attractions.\\\"\\n###Input: {\\\"question\\\": \\\"What is the capital of Sweden?\\\", \\\"context\\\": \\\"Stockholm is the beautiful capital of Sweden, which is known for its high living standards and great attractions.\\\"}\\n", "category": "generic"}
{"question_id": 211, "text": " Write an overview for astronomers about how Jupiter became the largest planet in our solar system.\\n", "category": "generic"}
{"question_id": 212, "text": " Create a virtual assistant that can provide answers to trivia questions about history.\\n", "category": "generic"}
{"question_id": 213, "text": " I want to build a system that can answer questions from users reading a book.\\n", "category": "generic"}
{"question_id": 214, "text": " Our customer support team needs an AI assistant to handle customer inquiries. It should be able to understand and evaluate if the answer provided by the assistant is contradictory, neutral or entails the customer's question.\\n", "category": "generic"}
{"question_id": 215, "text": " Use a zero-shot classifier to classify news headlines into three categories: sports, technology, and politics.\\n", "category": "generic"}
{"question_id": 216, "text": " Analyze a German text and classify it into different categories like crime, tragedy, or theft.\\n", "category": "generic"}
{"question_id": 217, "text": " Create a tool to determine the logical relationship between two given sentences.\\n", "category": "generic"}
{"question_id": 218, "text": " A media company needs to summarize a news article in order to make it easy for their audience to understand the main points quickly.\\n", "category": "generic"}
{"question_id": 219, "text": " We want to implement a customer service chatbot to engage with website visitors and provide support.\\n", "category": "generic"}
{"question_id": 220, "text": " As the project manager of a company who receives long emails, my goal is to summarize them and extract the most important information.\\n", "category": "generic"}
{"question_id": 221, "text": " I am the user and I want to start a multi-turn conversation with this model.\\n", "category": "generic"}
{"question_id": 222, "text": " Create a Python function named \\\"summarize_russian_text\\\" that accepts a Russian text as input and returns a brief summary of the text.\\n", "category": "generic"}
{"question_id": 223, "text": " We want to implement a chatbot on our website to provide quick responses to customer inquiries.\\n", "category": "generic"}
{"question_id": 224, "text": " We want the AI to have an interesting conversation and ask it general knowledge questions.\\n", "category": "generic"}
{"question_id": 225, "text": " We need to create a dialogue in Russian for our educational app. It should cover a general greeting and asking about the users' well-being.\\n", "category": "generic"}
{"question_id": 226, "text": " The company wants to create a chatbot to help answer customer questions regarding the chatbot's consciousness. We need to be able to generate sensible responses.\\n", "category": "generic"}
{"question_id": 227, "text": " Create interesting variations of the given phrase 'How can I improve my time management skills?'.\\n###Input: How can I improve my time management skills?\\n", "category": "generic"}
{"question_id": 228, "text": " A customer wants to convert an input Korean text into a summary. Provide a solution for it.\\n", "category": "generic"}
{"question_id": 229, "text": " Our company is providing translation services for business clients. We need to translate English contracts to French.\\n", "category": "generic"}
{"question_id": 230, "text": " We are an online platform that offers various Chinese language courses for students. Now we need to autoplay a fill-in-the-blank video game for our users.\\n", "category": "generic"}
{"question_id": 231, "text": " We want to determine the similarity between two sentences, \\\"I love going to the park\\\" and \\\"My favorite activity is visiting the park\\\".\\n", "category": "generic"}
{"question_id": 232, "text": " We have an audiobook app in the Chinese language, and we want to convert the Chinese text to speech.\\n", "category": "generic"}
{"question_id": 233, "text": " We want to build an audiobook service. We need to convert text to speech for the user to listen.\\n", "category": "generic"}
{"question_id": 234, "text": " I want to convert English speech into different language speech. I have some audio in English language and I would like to convert that into Spanish.\\n", "category": "generic"}
{"question_id": 235, "text": " We are trying to transcribe audios. Can you help with giving us an ASR model?\\n", "category": "generic"}
{"question_id": 236, "text": " Our blog platform accepts voice notes, and we want to transcribe these notes to text.\\n", "category": "generic"}
{"question_id": 237, "text": " Create a program to separate music and vocals from an audio file using a pretrained model.\\n", "category": "generic"}
{"question_id": 238, "text": " \\nCreate an application that automatically separates vocals from a song for karaoke nights.\\n", "category": "generic"}
{"question_id": 239, "text": " One of our clients need high recognition capability of denoising audio streams for improving sound quality.\\n", "category": "generic"}
{"question_id": 240, "text": " A podcast company reached out to us wanting to change their speaker's voice in a recorded podcast.\\n", "category": "generic"}
{"question_id": 241, "text": " Our research team is focused on the analysis and separation of complex audio recordings. We need a model to be able to separate speaker voices from mixed sound.\\n", "category": "generic"}
{"question_id": 242, "text": " A Romanian-speaking person wants to communicate with an English-speaking friend over the phone using our platform. Please provide a method to translate their speech in real-time.\\n", "category": "generic"}
{"question_id": 243, "text": " Implement an application that recognizes languages by analyzing an online audio file.\\n", "category": "generic"}
{"question_id": 244, "text": " A toy company wants to use your software to recognize spoken numbers (0-9) in English by young children for an interactive game. Please create an identifier.\\n", "category": "generic"}
{"question_id": 245, "text": " Develop an automated podcast recommender system to suggest content that matches user's taste based on their favorite podcast speakers.\\n", "category": "generic"}
{"question_id": 246, "text": " We are developing an app that can help mediate virtual therapy sessions. We need a feature that can detect emotions in the voice of the user.\\n", "category": "generic"}
{"question_id": 247, "text": " We are engineering a green consulting firm. We need to predict carbon emissions with input features from customer's dataset.\\n", "category": "generic"}
{"question_id": 248, "text": " Develop a system that can detect voices in a podcast to find out if guests are speaking or not. \\n", "category": "generic"}
{"question_id": 249, "text": " Develop a machine learning product to help home buyers predict house prices in the US.\\n", "category": "generic"}
{"question_id": 250, "text": " A game developer needs to predict the HP of a new Pokemon character based on several attributes. Provide the code to make that prediction.\\n", "category": "generic"}
{"question_id": 251, "text": " Let's create a smart agent that can learn to play CartPole-v0 using reinforcement learning.\\n", "category": "generic"}
{"question_id": 252, "text": " The sports equipment company I work for wants to build a virtual soccer training game. The agent must be able to effectively play soccer against another team.\\n", "category": "generic"}
{"question_id": 253, "text": " We want to build a smart-cars training system for our team. I need you to solve the gym environment 'MountainCar-v0'.\\n", "category": "generic"}
{"question_id": 254, "text": " We are a group of teachers looking to create an automated summary based on the student's essays.\\n", "category": "generic"}
{"question_id": 255, "text": " An important project is under development, and we need to know the positional relations between various keywords of a given medical text.\\n", "category": "generic"}
{"question_id": 256, "text": " Build a system for detecting hate speech from social media comments in Korean.\\n", "category": "generic"}
{"question_id": 257, "text": " Our team is working on a recommendation system for a news article app. We should understand the semantic similarity of various texts.\\n", "category": "generic"}
{"question_id": 258, "text": " We are developing an AI chatbot for a Russian company. I want to extract features from the clients' text messages in Russian language.\\n", "category": "generic"}
{"question_id": 259, "text": " I am an artist who wants to create a new piece of artwork based on the prompt \\\"A futuristic city under the ocean\\\".\\n", "category": "generic"}
{"question_id": 260, "text": " Create a character-like artwork image based on the phrase 'anime-style girl with a guitar'.\\n", "category": "generic"}
{"question_id": 261, "text": " We are designing an application that can generate images based on textual descriptions. We need to load a model capable of performing text-to-image conversion.\\n", "category": "generic"}
{"question_id": 262, "text": " A food delivery app wants to help users understand ingredients in the food item by analyzing the images. We need a solution to process the food images and give textual information about the items.\\n", "category": "generic"}
{"question_id": 263, "text": " The company wants to create a social media application like Pinterest that generates captions for users' images. Show us how to create this functionality.\\n", "category": "generic"}
{"question_id": 264, "text": " We are an analytics company that needs help in extracting data tables from plots and charts efficiently.\\n", "category": "generic"}
{"question_id": 265, "text": " Our marketing team wants a video commercial for our product. They are looking for a video with a couple sitting in a cafe and laughing while using our product.\\n", "category": "generic"}
{"question_id": 266, "text": " A company is having an ad campaign with a superhero theme. They want to generate a video of Spiderman surfing to showcase as a social media ad.\\n", "category": "generic"}
{"question_id": 267, "text": " We are developing a solution where we can visually detect the medical conditions of patients. Can you write code to determine the condition based on the image provided?\\n", "category": "generic"}
{"question_id": 268, "text": " Help me to build a visual-question-answering model that takes a question and an image as input and returns an answer based on the image.\\n", "category": "generic"}
{"question_id": 269, "text": " Implement an optical text recognition system for documents that can answer a simple question about the document's content.\\n", "category": "generic"}
{"question_id": 270, "text": " Our company deals with insurance claims. We need a smart assistant who can retrieve information from documents, especially invoices, such as total amount, date of invoice, and name of the service provider.\\n", "category": "generic"}
{"question_id": 271, "text": " We have an AI-powered document management system that needs to answer questions based on the content of a given document.\\n", "category": "generic"}
{"question_id": 272, "text": " Take a look at this document image and tell me the answer to my question: \\\"What is the total amount due?\\\".\\n###Input: {\\\"image_url\\\": \\\"https://example.com/document_invoice.jpg\\\", \\\"question\\\": \\\"What is the total amount due?\\\"}\\n", "category": "generic"}
{"question_id": 273, "text": " I am designing a parking spot detector for a car parking management solution. I want the model to estimage the depth of the car park.\\n", "category": "generic"}
{"question_id": 274, "text": " We are researching multi-view 3D scanning, and we would like to improve our depth estimation with a pretrained model.\\n", "category": "generic"}
{"question_id": 275, "text": " I am building an app to detect objects in an image from a URL. How can I experiment with object detection models using transformers?\\n", "category": "generic"}
{"question_id": 276, "text": " I have a picture of my backyard, but I am not sure if a cat sneaked in. Can you help me find out if it is a cat in the picture?\\n", "category": "generic"}
{"question_id": 277, "text": " Our hospital needs to analyze digital blood samples in order to detect and count platelets, red blood cells, and white blood cells.\\n", "category": "generic"}
{"question_id": 278, "text": " Develop a script that extracts a table from a given document and then visualize the results.\\n", "category": "generic"}
{"question_id": 279, "text": " We are now working on an architectural image generation project to generate different images of architecture.\\n", "category": "generic"}
{"question_id": 280, "text": " We are designing a book cover where the book is about love and roses. We have the idea of \\\"A head full of roses.\\\"\\n", "category": "generic"}
{"question_id": 281, "text": " Help me generate a realistic bedroom interior image that can be used as reference for a 3D model being created for a virtual reality game.\\n", "category": "generic"}
{"question_id": 282, "text": " You feel lonely and need to generate a picture of a butterfly. How would you proceed?\\n", "category": "generic"}
{"question_id": 283, "text": " We need an AI-generated insect image for our biology article about African habitats.\\n", "category": "generic"}
{"question_id": 284, "text": " Our client is involved in a sports organization, and they require a solution to classify sports videos efficiently.\\n", "category": "generic"}
{"question_id": 285, "text": " We need to create a content filter for images submitted by users in our online community. We want to detect and filter out adult content and offensive images.\\n", "category": "generic"}
{"question_id": 286, "text": " We are an animal rescue organization, and we are looking for a way to automatically identify if images uploaded to our site contain cats, dogs, or birds.\\n", "category": "generic"}
{"question_id": 287, "text": " Create a machine learning-based image recognition tool that can identify whether an animal in an image is a cat or a dog.\\n", "category": "generic"}
{"question_id": 288, "text": " I want to find out whether a given review is positive or negative. I need an API that can help me with sentiment analysis.\\n", "category": "generic"}
{"question_id": 289, "text": " I am a traveler and I want to know the best time to visit a Chinese historical site. Take a look at an image and tell me whether it is a good time to visit the site or not.\\n", "category": "generic"}
{"question_id": 290, "text": " Organizing a special event and want to identify the place where street pictures were taken for invitations.\\n", "category": "generic"}
{"question_id": 291, "text": " Our client wants us to monitor consumer sentiment on their e-commerce platform. We must identify low-rated product reviews in six languages: English, Dutch, German, French, Italian, and Spanish.\\n", "category": "generic"}
{"question_id": 292, "text": " Write a script for an online forum moderator that will help them to detect gibberish text in a post.\\n", "category": "generic"}
{"question_id": 293, "text": " As a restaurant owner, I want to know if the total revenue for last week met our target revenue.\\n", "category": "generic"}
{"question_id": 294, "text": " We are a sports analysis company that processes data from spreadsheets with game statistics. We need to identify the player who has scored the maximum goals in a given match.\\n###Input: \\\"What player scored the most goals?\\\", \\\"Player,Goals\\\\nA,2\\\\nB,3\\\\nC,1\\\"\\n", "category": "generic"}
{"question_id": 295, "text": " A manager in our company requires an aggregated answer of the highest and lowest sales numbers for a given period to evaluate the perfomance.\\n", "category": "generic"}
{"question_id": 296, "text": " A doctor needs to find medical information in a large document. We are building an AI model to help them extract answers to their questions. \\n", "category": "generic"}
{"question_id": 297, "text": " We are building a news classification system, and for Spanish articles, we have to categorize them into different sections.\\n", "category": "generic"}
{"question_id": 298, "text": " In my project, I need to classify news articles into their respective categories, such as technology, sports, and politics, without providing any labeled training data.\\n", "category": "generic"}
{"question_id": 299, "text": " I have a Catalan language text and I need to publish it as a blog article in Spanish, so I want to translate my Catalan text into Spanish.\\n", "category": "generic"}
{"question_id": 300, "text": " Generate an automatic summarization of an executive's meeting notes without sharing too much details.\\n", "category": "generic"}
{"question_id": 301, "text": " A customer needs to translate a French email they received into Spanish.\\n", "category": "generic"}
{"question_id": 302, "text": " I am a writer, and I have written a long article, but I need to come up with a summary for my article to publish.\\n", "category": "generic"}
{"question_id": 303, "text": " Your company has launched a new chatbot service. We need to create conversations with the customers to provide information about our products and answer their queries.\\n", "category": "generic"}
{"question_id": 304, "text": " We are a travel company trying to offer personalized experiences to our customers. We want to implement a conversational AI to help users get information about travel destinations.\\n", "category": "generic"}
{"question_id": 305, "text": " Write a tweet on the topic of \\\"The Future of AI in Education\\\".\\n", "category": "generic"}
{"question_id": 306, "text": " Create a poem about nature for a school assignment.\\n###Input: Once upon a time, in a land of greenery and beauty,\\n", "category": "generic"}
{"question_id": 307, "text": " We want to code a loading spinner to display when our system is undergoing maintenance. Can you generate the code for us?\\n###Input: <noinput>\\n", "category": "generic"}
{"question_id": 308, "text": " I am an English teacher. What kind of creative writing exercise could I give to my students that they could write a story using the words \\\"moon\\\", \\\"rabbit\\\", \\\"forest\\\", and \\\"magic\\\"?\\n", "category": "generic"}
{"question_id": 309, "text": " Our team needs to develop a tool that can translate articles from English to German in real-time.\\n", "category": "generic"}
{"question_id": 310, "text": " Design an efficient search engine that retrieves most relevant articles based on a pool of long text documents.\\n", "category": "generic"}
{"question_id": 311, "text": " Our designer hired me to create an application to automatically translate colors from a design. Usually, our designer likes to work with English, to get the color in Italian.\\n", "category": "generic"}
{"question_id": 312, "text": " I am building an AI to create in work report, this model should be able to complete the sentence for the report.\\n", "category": "generic"}
{"question_id": 313, "text": " A language learning application wants to generate fill-in-the-blank questions for learners. We need to mask sentences with a keyword to create these questions.\\n", "category": "generic"}
{"question_id": 314, "text": " Our client has a medical report and we are trying to assist him in finding relevant information.\\n", "category": "generic"}
{"question_id": 315, "text": " I am a data scientist and need to find which among given documents is similar, provide me the example code.\\n", "category": "generic"}
{"question_id": 316, "text": " My company wants to analyze and cluster different movie scripts. I need a suggestion to embed the movie dialogue text into dense vector representations.\\n", "category": "generic"}
{"question_id": 317, "text": " Analyze how characters in a book are connected and if they share any similarity based on their conversation.\\n", "category": "generic"}
{"question_id": 318, "text": " We are a company specializing in building automation systems for homes. We'd like to convert our written rules into an audio format for user interaction purposes.\\n", "category": "generic"}
{"question_id": 319, "text": " We are developing a virtual assistant. One of its major functionalities is to convert written text to speech.\\n", "category": "generic"}
{"question_id": 320, "text": " I have a recorded meeting between multiple participants. Identify when two or more people are speaking simultaneously.\\n", "category": "generic"}
{"question_id": 321, "text": " I have a collection of audio recordings from interviews that I need transcribed into text. How can I achieve this using automatic speech recognition?\\n", "category": "generic"}
{"question_id": 322, "text": " A Russia-based online lesson website needs an English subtitle for the recorded lessons of the students.\\n", "category": "generic"}
{"question_id": 323, "text": " My lecture has been recorded, and I would like to transcribe the audio to create a transcript of my presentation.\\n", "category": "generic"}
{"question_id": 324, "text": " Provide the code to enhance a single audio track, possibly containing dialogue, music and background noise, extracted from a video game.\\n", "category": "generic"}
{"question_id": 325, "text": " We're working with a voiceover company, and they're looking for a tool to help them change the voice style of voiceovers while keeping the same content.\\n", "category": "generic"}
{"question_id": 326, "text": " Develop a listening assistant device for audiobooks that is capable of detecting and reducing noise.\\n", "category": "generic"}
{"question_id": 327, "text": " The company needs to separate voice from background noise in a recorded podcast episode.\\n", "category": "generic"}
{"question_id": 328, "text": " Develop a method to detect which languages are being spoken in international conference calls.\\n", "category": "generic"}
{"question_id": 329, "text": " Develop a system that can detect a keyword in a short audio clip. Make sure it recognizes a variety of keywords.\\n", "category": "generic"}
{"question_id": 330, "text": " I am the director of a movie theater chain, and we are thinking of implementing a chat product to get movie goers to classify movie reviews as positive or negative through AI.\\n", "category": "generic"}
{"question_id": 331, "text": " You have been provided with the dataset of plants, and your task is to identify the species of plants among Iris Setosa, Iris Versicolor, and Iris Virginica.\\n", "category": "generic"}
{"question_id": 332, "text": " Our energy company is trying to identify anomalies in the energy consumption data. Could you perform anomaly detection on the time series data?\\n", "category": "generic"}
{"question_id": 333, "text": " Our company seeks a solution to reduce the carbon emissions due to our operations. We have collected historical data about our emissions. We are looking for a machine learning model to predict the carbon emissions based on this data.\\n", "category": "generic"}
{"question_id": 334, "text": " Create a program that predicts carbon emissions for new data using the given model.\\n", "category": "generic"}
{"question_id": 335, "text": " A company has collected data about fish measurements and weights. We need to estimate a fish's weight based on its measurements.\\n", "category": "generic"}
{"question_id": 336, "text": " Our company manages a restaurant. We need to analyze customers' tips and predict how much tip a new customer would give based on their total bill, sex, smoker, day, time, and party size. We should use a pre-trained model.\\n", "category": "generic"}
{"question_id": 337, "text": " This time, we want to build a player against the user with reinforcement learning. Can you help?\\n", "category": "generic"}
{"question_id": 338, "text": " We are building a robot for indoor navigation. We need to configure it to recognize its surroundings and interact with objects in the environment.\\n", "category": "generic"}
{"question_id": 340, "text": " We are building a medical knowledge-based management system. We need to process and extract features from biomedical entity names.\\n", "category": "generic"}
{"question_id": 341, "text": " A product is built that analyzes book reviews in order to determine how similar two examples from multiple books are to each other.\\n", "category": "generic"}
{"question_id": 342, "text": " Write a welcome email to a new employee joining the company.\\n", "category": "generic"}
{"question_id": 343, "text": " We need to extract useful features from Russian text for downstream tasks.\\n", "category": "generic"}
{"question_id": 344, "text": " We are working on a project to detect patterns and correlations in a dataset that contains a mix of code segments and comments. Find a suitable model to complete this task.\\n", "category": "generic"}
{"question_id": 345, "text": " The marketing department wants to use AI-generated images for their next social media campaign. They want a high-resolution image of a vintage sports car racing through a desert landscape during sunset. \\n", "category": "generic"}
{"question_id": 346, "text": " My company is working on a children's storybook. I need to generate images based on the text descriptions of scenes in the story.\\n", "category": "generic"}
{"question_id": 347, "text": " Design a text-based interface where you can input text and get an image description from the given text.\\n", "category": "generic"}
{"question_id": 348, "text": " I am designing an e-commerce website, and I want a program to generate images based on written descriptions to be used as mock product images.\\n", "category": "generic"}
{"question_id": 349, "text": " In our company, we have to generate descriptive captions for photographs related to the products.\\n", "category": "generic"}
{"question_id": 350, "text": " We are building a visual tour guide application for smartphones. The app should be able to identify landmarks and provide information about them. I want to use the BLIP-2 model for this task.\\n", "category": "generic"}
{"question_id": 351, "text": " I need a tool to analyze a chart image and generate a summary of the information contained within the chart.\\n", "category": "generic"}
{"question_id": 352, "text": " Generate a video for a cooking show intro with the text \\\"Chef John's Culinary Adventures.\\\"\\n", "category": "generic"}
{"question_id": 353, "text": " We are doing a creative commercial. We require a video that represents a person walking along a beach.\\n", "category": "generic"}
{"question_id": 354, "text": " Help me to build a self-learning assistant capable of answering questions about an image.\\n", "category": "generic"}
{"question_id": 355, "text": " We have an online journal application that requires users to upload documents in which we need to automatically answer the questions related to the uploaded document.\\n", "category": "generic"}
{"question_id": 356, "text": " An educational company is building a document question answering system for their students. They want the system to answer questions about the contents of textbooks and study guides quickly and accurately.\\n", "category": "generic"}
{"question_id": 357, "text": " Our company focuses on predicting molecular properties. We need the best model to tackle this problem for our project. We heard about Graphormer and want to start with a pretrained model.\\n", "category": "generic"}
{"question_id": 358, "text": " We have a company that processes loan applications. Give us the answer for a question, based on a document.\\n###Input: Our company policy restricts the loan applicant's eligibility to the citizens of United States. The applicant needs to be 18 years old or above and their monthly salary should at least be $4,000. FetchTypeOfYear: 2019. Can anyone with a monthly salary of $3,000 apply?\\n", "category": "generic"}
{"question_id": 359, "text": " Our company is setting up an online shop for selling computer parts. We need a system that can identify the components in images uploaded by users.\\n", "category": "generic"}
{"question_id": 360, "text": " I want to classify the images of houseplants to find out their type, like whether it's a cactus, fern, or succulent.\\n", "category": "generic"}
{"question_id": 361, "text": " I have few picture from my album, and I would like to find out whether it is a hotdog or not.\\n", "category": "generic"}
{"question_id": 362, "text": " Our client is building an application that converts scanned tables into structured data. We need to detect the rows and columns in a given table image.\\n", "category": "generic"}
{"question_id": 363, "text": " I am hosting a vegan food event and I want to know if any of the dishes contains meat from the picture I have taken.\\n", "category": "generic"}
{"question_id": 364, "text": " In a kitchen, as a robot chef, we need to identify different objects such as fruits and dishes.\\n", "category": "generic"}
{"question_id": 365, "text": " We need to segment clothes in photos for our online fashion store.\\n", "category": "generic"}
{"question_id": 366, "text": " Automate the process of creating a normal map from a given object image.\\n", "category": "generic"}
{"question_id": 367, "text": " We have a picture of a landscape, and we'd like to add a building and a river in this picture.\\n", "category": "generic"}
{"question_id": 368, "text": " Our client, an architecture firm, needs a rendering of a \\\"luxury living room with a fireplace\\\" as a visualization for a prestigious project.\\n", "category": "generic"}
{"question_id": 369, "text": " We would like to restore an old and blurry image of our grandparents for an anniversary present.\\n", "category": "generic"}
{"question_id": 370, "text": " A creative artist wants to generate a painting from a given input text titled \\\"A head full of roses,\\\" utilizing the image provided in the API description.\\n", "category": "generic"}
{"question_id": 371, "text": " I would like to generate a high-quality image of a church using unconditional image generation.\\n", "category": "generic"}
{"question_id": 372, "text": " Recently, my company is asked to produce new portraits for the entire team. Therefore, I am looking for an AI which can generate human faces from scratch.\\n", "category": "generic"}
{"question_id": 373, "text": " Our team wants to make a cat-themed video game. We would like to design various cartoon cat characters through AI-generated images.\\n", "category": "generic"}
{"question_id": 374, "text": " Can you teach me to build a program that will analyze video and describe what's happening in natural language?\\n", "category": "generic"}
{"question_id": 375, "text": " In a security context, the company is starting a project to detect any signs of violence in video streams from its CCTV cameras. It's essential to analyze the video content for any violent behaviors.\\n", "category": "generic"}
{"question_id": 376, "text": " Create a sports highlight generator that takes video clips as input and identifies the category of the sports activity happening in the video.\\n", "category": "generic"}
{"question_id": 377, "text": " I am a movie director and I need to detect the genre of a movie based on its actions.\\n", "category": "generic"}
{"question_id": 378, "text": " Bob is designing an app for his city. He needs to quickly identify if the provided image has a bike or a car. Design a model that recognizes a bike or car and provides classification output for the given image.\\n", "category": "generic"}
{"question_id": 379, "text": " I need to analyze the sentiment of tweets to find out which products are getting positive customer feedback.\\n", "category": "generic"}
{"question_id": 380, "text": " We are building a content moderation system. Our clients upload the content, it can be generated by human or AI. We want to have a filtering API to advise on the original text if it is generated by GPT-2.\\n", "category": "generic"}
{"question_id": 381, "text": " I have a list of comments related to stocks. Check the overall sentiment towards each stock.\\n", "category": "generic"}
{"question_id": 382, "text": " I'd like to give a potted plant to my friend. Write me a short and simple plant care instruction.\\n", "category": "generic"}
{"question_id": 383, "text": " I need to extract biomedical entities from a given set of case reports to make it easier for researchers to analyze the data.\\n", "category": "generic"}
{"question_id": 384, "text": " I need an AI feature capable of extracting names of people, organizations, and locations from various news articles in different languages.\\n", "category": "generic"}
{"question_id": 385, "text": " We are a consulting firm, and we want to easily identify company names from texts.\\n", "category": "generic"}
{"question_id": 386, "text": " Our company is making a chatbot that needs to extract information from a paragraph. Get the named entities in the paragraph.\\n", "category": "generic"}
{"question_id": 387, "text": " A journalist is looking for historical Olympic host cities and wants to find the year when Beijing hosted the games.\\n###Input: {\\\"table\\\": {\\n    \\\"year\\\": [1896, 1900, 1904, 2004, 2008, 2012],\\n    \\\"city\\\": [\\\"Athens\\\", \\\"Paris\\\", \\\"St. Louis\\\", \\\"Athens\\\", \\\"Beijing\\\", \\\"London\\\"]},\\n    \\\"query\\\": \\\"Select the year when Beijing hosted the Olympic games\\\"}\\n", "category": "generic"}
{"question_id": 388, "text": " I am a Korean exchange student in the United States. I am building a Korean Table Question Answering assistant to help me with my studies.\\n", "category": "generic"}
{"question_id": 389, "text": " Create an API that processes large data sets of financial transactions and can deliver information on the number of transactions and their monetary value, based on a date range.\\n", "category": "generic"}
{"question_id": 390, "text": " A product manager needs an explanation related to model conversion. They are confused why it is important. Can you please help them by providing an answer?\\n###Input: \\n{\\n 'question': 'Why is model conversion important?',\\n 'context': 'The option to convert models between FARM and transformers gives freedom to the user and let people easily switch between frameworks.'\\n}\\n", "category": "generic"}
{"question_id": 391, "text": " We are building a memory game where a description is displayed for a few seconds and later on, questions on what is shown comes up for the user to remember.\\n", "category": "generic"}
{"question_id": 392, "text": " We are a medical research team working on a COVID-19 project. We need assistance in question answering related to the pandemic and related research papers.\\n", "category": "generic"}
{"question_id": 393, "text": " The customer is writing a book about the solar system and just needs the answer to a question: How long does it take for Mars to orbit the sun?\\n", "category": "generic"}
{"question_id": 394, "text": " I am designing a quiz app that caters to blind users. The app should interpret the images and answer questions based on the image contents.\\n", "category": "generic"}
{"question_id": 395, "text": " The company develops a digital assistant that can answer questions about software products. Implement a feature to provide answers to user questions.\\n", "category": "generic"}
{"question_id": 396, "text": " I'm talking to a new person online. Can this API help keep me safe by filtering out any inappropriate messages they send me?\\n", "category": "generic"}
{"question_id": 397, "text": " I want to build a personal assistant to check the logical relationship between two sentences, especially when I have a lot of texts in English.\\n", "category": "generic"}
{"question_id": 398, "text": " I am reading a book and, whenever I engage with an interesting topic, I write a short sentence summarizing that topic. I would like to have an assistant that, given a sentence, automatically classifies that topic among 'technology', 'literature', and 'science'.\\n", "category": "generic"}
{"question_id": 399, "text": " Determine if one Russian sentence logically contradicts the information provided by another Russian sentence.\\n", "category": "generic"}
{"question_id": 400, "text": " The assignment is to develop a software for translating sentences from Spanish to English at a near-human performance.\\n", "category": "generic"}
{"question_id": 401, "text": " I am an author. I have a new book that needs a summary. I want the summary to resemble the style of SparkNotes.\\n", "category": "generic"}
{"question_id": 402, "text": " An editor wants to summarize his article in French.\\n###Input: \\\"Selon un rapport r\\u00e9cent, les constructeurs automobiles pr\\u00e9voient d'acc\\u00e9l\\u00e9rer la production de voitures \\u00e9lectriques et de r\\u00e9duire la production de voitures \\u00e0 moteur \\u00e0 combustion interne. Les constructeurs pr\\u00e9voient de consacrer davantage de ressources \\u00e0 la recherche et au d\\u00e9veloppement de technologies pour am\\u00e9liorer l'efficacit\\u00e9 des batteries, r\\u00e9duire les co\\u00fbts de production et \\u00e9tendre le r\\u00e9seau de recharge. Les gouvernements du monde entier continuent de soutenir l'adoption de la mobilit\\u00e9 \\u00e9lectrique \\u00e0 travers des incitations financi\\u00e8res et des objectifs ambitieux de r\\u00e9duction des \\u00e9missions de CO2.\\\"\\n", "category": "generic"}
{"question_id": 403, "text": " Design a chatbot for a school website, which can answer queries related to admissions, classes, teachers, and extracurriculars.\\n", "category": "generic"}
{"question_id": 404, "text": " We have a long article, and we want to generate a summary to understand the main points of the article.\\n", "category": "generic"}
{"question_id": 405, "text": " We need to prepare some sample conversations featuring frequently asked questions for helping customers with our products.\\n", "category": "generic"}
{"question_id": 406, "text": " Can you give me advice on how to choose the best video game for me?\\n###Input: {\\\"instruction\\\": \\\"what is the best way to choose a video game?\\\", \\\"knowledge\\\": \\\"Some factors to consider when choosing a video game are personal preferences, genre, graphics, gameplay, storyline, platform, and reviews from other players or gaming websites.\\\", \\\"dialog\\\": [\\\"What type of video games do you prefer playing?\\\", \\\"I enjoy action-adventure games and a decent storyline.\\\"]}\\n", "category": "generic"}
{"question_id": 407, "text": " Write a script to translate the following French sentence into English: \\\"Je t\\u2019aime.\\\"\\n", "category": "generic"}
{"question_id": 408, "text": " Your company is developing a chatbot and requires accurate summaries of lengthy dialogues without losing context.\\n", "category": "generic"}
{"question_id": 409, "text": " I want a system that can translate an article from English to French automatically.\\n", "category": "generic"}
{"question_id": 410, "text": " We want our System to generate possible user queries for a document provided as a text input.\\n", "category": "generic"}
{"question_id": 411, "text": " Our client wants to create marketing slogans. Help them by completing this slogan \\\"Customer satisfaction is our top <mask>.\\\"\\n", "category": "generic"}
{"question_id": 412, "text": " I need a French language model to help me fill in the blanks in given sentences.\\n", "category": "generic"}
{"question_id": 413, "text": " We would like to build a semantic text search system that can find similar documents in a repository based on a given description.\\n", "category": "generic"}
{"question_id": 415, "text": " I want to create a playlist of similar songs based on lyrics. How can I compare the sentences and find similar ones?\\n", "category": "generic"}
{"question_id": 416, "text": " I\\u2019m putting together a dating site where users can submit questions they'd like the matching algorithm to ask. I want to suggest questions like the ones they have already submitted. Can you provide me with a model to do that?\\n", "category": "generic"}
{"question_id": 417, "text": " I'm working on a phonebot, and I need the bot to be able to read a sensitive warning message to the users.\\n", "category": "generic"}
{"question_id": 418, "text": " Our customer wants to have this Chinese text \\\"<Chinese_text>\\\" read aloud as an audio file in female voice.\\n###Input: \\\"<Chinese_text>\\\": \\\"\\u4f60\\u597d\\uff0c\\u6b22\\u8fce\\u6765\\u5230\\u6570\\u5b57\\u4e16\\u754c\\u3002\\\"\\n", "category": "generic"}
{"question_id": 419, "text": " Develop a text-to-speech model for our mobile app to read news articles for our users.\\n", "category": "generic"}
{"question_id": 420, "text": " You are an Indian in Telugu pandit teaching kids how to pronounce conventional holy prayers.. Teach me mantras in Telugu synthesized by human like voice pronunciation.\\n", "category": "generic"}
{"question_id": 421, "text": " We are a French company creating an interactive voice response system for our call centers, and we need to convert text to speech for our telephony prompts.\\n", "category": "generic"}
{"question_id": 422, "text": " Create Japanese audio from the following text: \\\"\\u3053\\u3093\\u306b\\u3061\\u306f\\u3001\\u79c1\\u305f\\u3061\\u306f\\u3042\\u306a\\u305f\\u306e\\u52a9\\u3051\\u304c\\u5fc5\\u8981\\u3067\\u3059\\u3002\\\"\\n", "category": "generic"}
{"question_id": 423, "text": " We need to create automated transcripts from recorded podcasts that include punctuation for better readability.\\n", "category": "generic"}
{"question_id": 424, "text": " Convert the audio file of a phone interview to text for further analysis.\\n", "category": "generic"}
{"question_id": 426, "text": " I want to use speech enhancement for an audio call of a coworker.\\n", "category": "generic"}
{"question_id": 427, "text": " How do I separate the speakers from an audio file using the pre-trained ConvTasNet_Libri2Mix_sepclean_8k model from Hugging Face?\\n", "category": "generic"}
{"question_id": 428, "text": " A travel app we work on can translate language of a guide in real-time. We are targeting Spanish-speaking tourists.\\n", "category": "generic"}
{"question_id": 429, "text": " Design an audio description system for a user who needs help translating spoken English audio to spoken Hokkien audio.\\n", "category": "generic"}
{"question_id": 430, "text": " Implement a method to perform speech-to-speech translation between Hokkien and English using the xm_transformer_s2ut_hk-en model.\\n", "category": "generic"}
{"question_id": 431, "text": " A company wants to analyze the sentiment of the customer feedback in their Spanish-speaking call center. Generate a script for this task.\\n", "category": "generic"}
{"question_id": 432, "text": " Our company is working on an AI-powered language learning app for German. We need to build a feature that classifies emotions in German speech.\\n", "category": "generic"}
{"question_id": 433, "text": " We are a voice assistant service, and we need to verify the speaker identity for enhanced security when users access the system.\\n", "category": "generic"}
{"question_id": 434, "text": " I run a call center and I need a system that can identify the person on the other end of the line by analyzing their voice.\\n", "category": "generic"}
{"question_id": 435, "text": " A transcription service wants to identify which speaker said which words in an audio file. Help them with this task.\\n", "category": "generic"}
{"question_id": 436, "text": " I am running a chain of wine stores and I want to categorize wines and recommend them based on their quality.\\n", "category": "generic"}
{"question_id": 437, "text": " I own an e-commerce platform and I need to predict which customers will make a purchase based on their browsing behavior.\\n", "category": "generic"}
{"question_id": 438, "text": " Predict the income category of a person, based on their demographic information. The model should use Tensorflow decision trees to make binary classifications.\\n", "category": "generic"}
{"question_id": 439, "text": " A travel agency needs a system to predict whether a client's vacation will be successful based on their chosen destination, accommodation, and travel style. We want to provide suggestions for clients who may need additional support.\\n", "category": "generic"}
{"question_id": 440, "text": " A new city planning company wants to estimate carbon emissions for different types of buildings to provide sustainable living solutions. We need a model to classify the carbon emissions.\\n", "category": "generic"}
{"question_id": 441, "text": " We want to evaluate the carbon footprint of a construction project based on the tabular data of material consumption.\\n", "category": "generic"}
{"question_id": 442, "text": " Our company is an environmental consultancy firm. Determine the carbon emissions of different facilities based on the provided data.\\n", "category": "generic"}
{"question_id": 443, "text": " I need assistance in predicting carbon emissions of a city based on historical data. Use the dataset provided to predict future carbon emissions.\\n", "category": "generic"}
{"question_id": 444, "text": " We have some clients asking for estimating CO2 emissions based on their historic data, which is in a CSV file.\\n", "category": "generic"}
{"question_id": 445, "text": " Determine the electricity consumption of a residential area based on historical data.\\n", "category": "generic"}
{"question_id": 446, "text": " A soccer simulation company wants to use a reinforcement learning agent that can play SoccerTwos effectively.\\n", "category": "generic"}
{"question_id": 447, "text": " Our company is developing a game, and they want to include an AI agent to play the game. We need to evaluate its performance on the CartPole-v1 environment for consistency.\\n", "category": "generic"}
{"question_id": 448, "text": " We need help from a AI to continuously develop a virtual environment for our production robots.\\n", "category": "generic"}
{"question_id": 449, "text": " We are a news agency that wants to extract useful features from Korean news articles for a content recommendation service.\\n", "category": "generic"}
{"question_id": 450, "text": " We want to automatically generate hashtags for the provided image URL to improve social media post performance.\\n", "category": "generic"}
{"question_id": 451, "text": " I am building a recommendation engine for TV shows. Can you evaluate the TV shows using a BERT-based model trained on sentence embedding to find the most similar TV shows based on description?\\n", "category": "generic"}
{"question_id": 452, "text": " I want to create a solution that can answer questions related to an image of my pet dogs.\\n", "category": "generic"}
{"question_id": 453, "text": " Design an explanation on how to use Pix2Struct to analyze and generate text based on visuals such as graphs and charts.\\n", "category": "generic"}
{"question_id": 454, "text": " Design a product that allows users to read street signs in a foreign language.\\n", "category": "generic"}
{"question_id": 455, "text": " I have a text question about an image, and I would like to receive an appropriate answer.\\n", "category": "generic"}
{"question_id": 456, "text": " Create software that extracts answers from input documents when given a set of questions.\\n", "category": "generic"}
{"question_id": 457, "text": " We received a scanned document with a lot of information. We need to go through it and find answers to specific questions. Create a program to extract information from the document.\\n", "category": "generic"}
{"question_id": 458, "text": " In a pharmaceutical company, we are working on developing a new drug, and I want you to help classify the molecular structures of my dataset.\\n", "category": "generic"}
{"question_id": 459, "text": " Calculate the distance between the objects in an image for an autonomous vehicle driving in a parking lot.\\n", "category": "generic"}
{"question_id": 460, "text": " We want a technology to be developed in the field of autonomous vehicles. Provide an example to estimate the depth of the environment using monocular images.\\n", "category": "generic"}
{"question_id": 461, "text": " As a construction company, we want to have a system to estimate the depth of images taken from different construction sites.\\n", "category": "generic"}
{"question_id": 462, "text": " Our team has built a Japanese language learning app for students. We want to make sure that the user-submitted image is an anime art created by humans and not AI-generated.\\n", "category": "generic"}
{"question_id": 463, "text": " We need to classify the type of an image for an inventory.\\n", "category": "generic"}
{"question_id": 464, "text": " Imagine we run an e-commerce platform and we need to tag new products with relevant categories based on their product images.\\n", "category": "generic"}
{"question_id": 465, "text": " We are building an AI-based camera system to ensure safety on a construction site. Detect workers wearing hard hats in a given image.\\n", "category": "generic"}
{"question_id": 466, "text": " My client needs help with security. They have a surveillance camera set up and we need to identify if any unknown person enters their property.\\n", "category": "generic"}
{"question_id": 467, "text": " We need a system to control the access into parking lot. Analyze an image and find out the license plate numbers to detect whether it's an authorized vehicle or not.\\n", "category": "generic"}
{"question_id": 468, "text": " Analyze an image of an urban scene to identify and separate regions with different semantics, such as streets, pedestrians, buildings, and vehicles.\\n", "category": "generic"}
{"question_id": 469, "text": " We are developing an application to help customers visualize themselves wearing clothes available on our e-commerce website. We need identification of clothing items in the image.\\n", "category": "generic"}
{"question_id": 470, "text": " We need an image segmentation solution for our smart city planning project. The solution should be able to handle different types of segmentation tasks, from semantic to instance to panoptic.\\n", "category": "generic"}
{"question_id": 471, "text": " We have a road safety app that helps identify potholes. Can we use an image segmentation model to detect if there are potholes in the pictures of roads?\\n", "category": "generic"}
{"question_id": 472, "text": " We are making a mobile app related to fitness. We need to estimate the human pose from an image of a user performing an exercise.\\n", "category": "generic"}
{"question_id": 473, "text": " I wish to edit my images by detecting straight lines and controlling the diffusion models in the image's diffusion process.\\n", "category": "generic"}
{"question_id": 474, "text": " A photographer wants to create artistic interpretations of some of her pictures. Let's help her to get variated styles of her image.\\n", "category": "generic"}
{"question_id": 475, "text": " We have a client that manages a car website for selling used cars and they need new images of cars for the website.\\n", "category": "generic"}
{"question_id": 476, "text": " I am interested in building a wall of generated pictures for my gallery. My specifications include a size of 256x256 pixels.\\n", "category": "generic"}
{"question_id": 477, "text": " We are a product company selling personalized gadgets, and we want to build a recommender system that shows generated, high-resolution images of human faces on our website.\\n", "category": "generic"}
{"question_id": 478, "text": " I want to build an AI-based software that can identify the activities or actions in a video clip.\\n", "category": "generic"}
{"question_id": 479, "text": " The marketing team wants a tool to quickly classify new advertisement videos.\\n", "category": "generic"}
{"question_id": 480, "text": " A sports league wants to analyze their videos and extract information on game highlights.\\n", "category": "generic"}
{"question_id": 481, "text": " We are a sports broadcasting company, and we need to automatically identify the sports events taking place in the videos we receive.\\n", "category": "generic"}
{"question_id": 482, "text": " My organization wants to create a video categorization tool to classify and categorize various videos. Utilize the appropriate API to build this video classifier.\\n", "category": "generic"}
{"question_id": 483, "text": " We have a surveillance camera in our backyard. We would like to analyze the captured videos to recognize the activities taking place in the backyard.\\n", "category": "generic"}
{"question_id": 484, "text": " We need to classify an image's content and check if it contains a cat or a dog.\\n", "category": "generic"}
{"question_id": 485, "text": " Analyze the type of plants in the image provided and provide the name of the probable plant.\\n", "category": "generic"}
{"question_id": 486, "text": " I want to organize my images based on the scene content. The categories I want are landscape, cityscape, beach, forest, and animals.\\n", "category": "generic"}
{"question_id": 487, "text": " We run an online store for selling electronic devices. We need to classify product images from our inventory and organize them.\\n", "category": "generic"}
{"question_id": 488, "text": " Our customer wants to analyze the sentiment of their customers' feedback. The feedback is in Spanish.\\n", "category": "generic"}
{"question_id": 489, "text": " A stock investor is looking to analyze the sentiment of a stock forum, such as StockTwits, to gain insights into the market sentiment for a specific stock.\\n", "category": "generic"}
{"question_id": 490, "text": " A forum moderator wants a tool to assess user-generated comments for toxic content. How does this model help?\\n", "category": "generic"}
{"question_id": 491, "text": " I am developing a news analysis platform. I need to predict the named entities from the articles.\\n", "category": "generic"}
{"question_id": 492, "text": " We need to analyze the user's text for extracting entities and improve our virtual assistant interaction.\\n", "category": "generic"}
{"question_id": 493, "text": " Extract the named entities from a given text snippet.\\n###Input: \\\"On June 7th, Jane Smith visited the Empire State Building in New York with an entry fee of 35 dollars.\\\"\\n", "category": "generic"}
{"question_id": 494, "text": " As a surveillance analyst, my main task this week is to find out all the names and locations mentioned in the online chat rooms.\\n", "category": "generic"}
{"question_id": 495, "text": " I need a program to identify the entities like persons, locations, organizations, and other names in a given German text.\\n", "category": "generic"}
{"question_id": 496, "text": " We are a multi-national company that would like to better understand global events and extract relevant named entities across 9 languages (de, en, es, fr, it, nl, pl, pt, ru). We would like to utilize a pre-trained NER model.\\n", "category": "generic"}
{"question_id": 497, "text": " We have a multimedia app in the Korean language. To deal with customer queries automatically, we want to incorporate question answering capability.\\n", "category": "generic"}
{"question_id": 498, "text": " Guide me to create a quiz project where I will summarize an article into a paragraph and from the summary I will develop a question with some multiple options. I need to check the correct answer for that question.\\n", "category": "generic"}
{"question_id": 499, "text": " I have a website text about technology and I want to know if it represents a positive sentiment or a negative one.\\n", "category": "generic"}
{"question_id": 500, "text": " Translate a French website description containing information about the company, services, and copyright notice into English.\\n###Input: \\\"Bienvenue sur notre site ! Nous sommes une entreprise sp\\u00e9cialis\\u00e9e dans la gestion des projets informatiques et la cr\\u00e9ation de logiciels sur mesure. Nos services incluent la conception, le d\\u00e9veloppement, la maintenance et le support 24/7. Tous droits r\\u00e9serv\\u00e9s.\\\"\\n", "category": "generic"}
{"question_id": 501, "text": " We have a list of documents written in multiple Romance languages, including texts in French, Spanish, and Italian. We want to make these texts accessible to our English-speaking audience by translating them.\\n", "category": "generic"}
{"question_id": 502, "text": " We have an international audience for our website and require our website content to be translated into multiple languages for better understanding.\\n", "category": "generic"}
{"question_id": 503, "text": " I am working for the review section of a book company. I want to convert a book summary into a positive book review.\\n", "category": "generic"}
{"question_id": 504, "text": " We are a company that wants to expand globally. We need to translate our website content from English to Italian.\\n", "category": "generic"}
{"question_id": 505, "text": " Our company is working on a news website. We want to present summaries of news articles written in French to the users. \\n", "category": "generic"}
{"question_id": 506, "text": " We are an international business and need to translate our documents from French to Spanish.\\n", "category": "generic"}
{"question_id": 507, "text": " The company needs to summarize articles for its news application. Provide guidelines to use PEGASUS for this purpose.\\n", "category": "generic"}
{"question_id": 508, "text": " You are working as a news curator for a media company and need to make a short summary of a long news article.\\n", "category": "generic"}
{"question_id": 509, "text": " Assemble a conversational bot for my online business to answer questions regarding our products.\\n", "category": "generic"}
{"question_id": 510, "text": " We are developing a game and need a text-based AI to control the main character's behavior. Help us use this chatbot based on the Joshua character.\\n", "category": "generic"}
{"question_id": 511, "text": " We are creating an AI assistant for banking clients. The customers should be able to talk to the bot to do various tasks. We need to have a meaningful dialogue with them.\\n", "category": "generic"}
{"question_id": 512, "text": " We want to facilitate a conversation with our Russian customers. Build a model that can respond to customer inquiries in Russian.\\n", "category": "generic"}
{"question_id": 513, "text": " We are launching a new blog and need a paragraph with tips on how to take care of houseplants.\\n", "category": "generic"}
{"question_id": 514, "text": " The development team is trying to create a function in Python to print \\\"Hello, World!\\\" but they're not sure how to proceed. Generate this function for them.\\n", "category": "generic"}
{"question_id": 515, "text": " We need to generate a motivational quote related to sports.\\n", "category": "generic"}
{"question_id": 516, "text": " Give me a tool that I can use to generate stories based on a starting phrase.\\n", "category": "generic"}
{"question_id": 517, "text": " Our PR team requires a machine learning approach to creative sentence generation for marketing content creation.\\n", "category": "generic"}
{"question_id": 518, "text": " We are developing an AI-powered code review system. Our model should provide a short summary of the provided code snippet.\\n", "category": "generic"}
{"question_id": 519, "text": " I want a language model that can fill in short blanks for example sentences, quizzes, or trivia questions.\\n", "category": "generic"}
{"question_id": 520, "text": " As a language teacher, I sometimes need help completing Dutch sentences with the most suitable word. Can you fill in the gaps?\\n", "category": "generic"}
{"question_id": 521, "text": " Translate some lyrics of a Portuguese song into English\\n", "category": "generic"}
{"question_id": 522, "text": " We are a news portal website and recently acquired a breaking news article. Determine the similarity between the main text of this article and other articles in our database.\\n", "category": "generic"}
{"question_id": 523, "text": " As a business assistant of an international company, find the most relevant sentence among a list of sentences that answers a specific question.\\n###Input: {\\\"question\\\": \\\"What is the main purpose of photosynthesis?\\\", \\\"sentences\\\": [\\\"Photosynthesis is the process used by plants to convert light energy into chemical energy to fuel their growth.\\\", \\\"The Eiffel Tower is a famous landmark in Paris.\\\", \\\"Photosynthesis also produces oxygen as a byproduct, which is necessary for life on Earth.\\\"]}\\n", "category": "generic"}
{"question_id": 524, "text": " We need to cluster customer reviews based on their content similarity to understand the common issues customers are facing.\\n", "category": "generic"}
{"question_id": 525, "text": " Create a program to calculate sentence similarity scores between a list of sentences.\\n", "category": "generic"}
{"question_id": 526, "text": " We need to find a similar Chinese sentence to my source sentence for a suggestion.\\n", "category": "generic"}
{"question_id": 527, "text": " I am building a voice assistant for my mobile app. Give me an example of how I can implement this Text-to-Speech model.\\n", "category": "generic"}
{"question_id": 528, "text": " Our team wants to transcribe audio files into text including punctuation marks for usability studies.\\n", "category": "generic"}
{"question_id": 529, "text": " Your client is a podcast aggregator website looking to transcribe episodes and display transcriptions on the site. Help them transcribe an audio file containing spoken English words.\\n", "category": "generic"}
{"question_id": 530, "text": " Our app offers assistance to people with hearing problems by enhancing the clarity of speech. We need a feature to clean and enhance the audio.\\n", "category": "generic"}
{"question_id": 531, "text": " Find a way to separate the background music and vocal from an audio file.\\n", "category": "generic"}
{"question_id": 532, "text": " An educational software company needs an advanced method to separate voices from background noise. Develop a solution.\\n", "category": "generic"}
{"question_id": 533, "text": " Develop a voice command security system that distinguishes between specific command phrases like \\\"disarm security\\\" or \\\"activate alarm\\\".\\n", "category": "generic"}
{"question_id": 534, "text": " I have an audio recording that I want to analyze to understand the emotion of the speaker.\\n", "category": "generic"}
{"question_id": 535, "text": " We are working on an assistant that can help tourists translate spoken language from one language to another. Let's build a solution for Hokkien to English translation.\\n", "category": "generic"}
{"question_id": 536, "text": " Trying to implement an assistant that detects user's emotions on a given audio file to help them manage their emotions better.\\n", "category": "generic"}
{"question_id": 537, "text": " We are building a customer service analytics software. The software should recognize the voice of the customers.\\n", "category": "generic"}
{"question_id": 538, "text": " We are developing a voice-controlled device. Help categorize spoken commands into specific keywords to trigger various actions.\\n", "category": "generic"}
{"question_id": 539, "text": " Develop an emotion analysis system to understand customer satisfaction over the phone for a telecommunication company in Russia.\\n", "category": "generic"}
{"question_id": 540, "text": " We are a company that provides transcription services. We require voice activity detection in our audio recordings.\\n", "category": "generic"}
{"question_id": 541, "text": " We need to predict the survival of passengers on the Titanic based on certain demographics like age, gender, etc.\\n", "category": "generic"}
{"question_id": 542, "text": " Implement a system that predicts the carbon emissions in a given dataset.\\n", "category": "generic"}
{"question_id": 543, "text": " Predict the carbon emissions of several power plants based on their characteristics.\\n", "category": "generic"}
{"question_id": 544, "text": " Our robotics team needs a way to stabilize a two-wheeled self-balancing robot. Would you suggest and apply any RL algorithm for this purpose?\\n", "category": "generic"}
{"question_id": 545, "text": " We have a large text dataset and want to extract some important features from it for our data analysis.\\n", "category": "generic"}
{"question_id": 546, "text": " I'm working on a biomedical research project and need to extract features from a set of entity names to help me understand their relationships.\\n", "category": "generic"}
{"question_id": 547, "text": " Our company is working on an app that allows music producers to detect beats in a sample. We want to use the Hubert-large-ll60k model for this.\\n", "category": "generic"}
{"question_id": 548, "text": " As an art director, generating ideas from descriptions can be difficult. Produce an image of a serene lake at sunset.\\n", "category": "generic"}
{"question_id": 549, "text": " There is a new small restaurant opening and they need a store sign featuring a kangaroo eating pizza.\\n", "category": "generic"}
{"question_id": 550, "text": " Design a promotional poster for a new line of summer clothing featuring people wearing the clothes with a beach background.\\n", "category": "generic"}
{"question_id": 551, "text": " We are developing an application for Japanese language learners. We need to extract text from Japanese manga pages for our users.\\n", "category": "generic"}
{"question_id": 552, "text": " I am a museum curator, I need to learn more about an artwork in my archive so I can create better exhibition materials.\\n", "category": "generic"}
{"question_id": 553, "text": " We want an application that can answer questions about an image. For example, how many people are in this photo?\\n", "category": "generic"}
{"question_id": 554, "text": " I am developing a home security software which can detect intruders entering the house. In case any door or object is tampered, the application will ask, \\\"Who entered the room?\\\", to assist quick analysis of the CCTV recordings.\\n", "category": "generic"}
{"question_id": 555, "text": " Our business is growing rapidly, and we've received an increasing number of questions related to product images. We need the model to provide answers based on images.\\n", "category": "generic"}
{"question_id": 556, "text": " Our company receives invoices in different formats. We need to extract specific information from these documents to process payments and keep records.\\n", "category": "generic"}
{"question_id": 557, "text": " Design an AI algorithm to answer questions from scanned documents.\\n", "category": "generic"}
{"question_id": 558, "text": " I want to analyze the text and images in a document and extract answers to questions based on the content.\\n", "category": "generic"}
{"question_id": 559, "text": " Locate specific information from an invoice image, such as total amount due, invoice number, and due date.\\n", "category": "generic"}
{"question_id": 560, "text": " The company director needs a summary of a recent financial report. You should provide the answer of following question: What were the total revenues for the last quarter?\\n###Input: In the last quarter, the company's total revenues were reported at $3.2 million with a gross profit of $1.5 million. The operating expenses during the same quarter were $1 million.\\n", "category": "generic"}
{"question_id": 561, "text": " You must create a tool to assess safety risks automatically in construction sites by estimating the distance of tools and workers.\\n", "category": "generic"}
{"question_id": 562, "text": " Develop a solution to estimate the depth of objects in an image.\\n", "category": "generic"}
{"question_id": 563, "text": " We need to analyze some pictures from nature and classify them to protect some species of animals.\\n", "category": "generic"}
{"question_id": 564, "text": " We need to detect objects in an image provided by a user and describe their locations.\\n", "category": "generic"}
{"question_id": 565, "text": " We are creating an analysis platform for Counter-Strike: Global Offensive. Detect and locate players in the given image.\\n", "category": "generic"}
{"question_id": 566, "text": " Build an AI model that can recognize the objects in a given image and draw a boundary around them.\\n", "category": "generic"}
{"question_id": 567, "text": " Design a pipeline to create artistic variations of an input image.\\n", "category": "generic"}
{"question_id": 568, "text": " A user wants to improve the quality of a small sized picture from her vacation. We need to upscale the picture to 2x its size without losing quality.\\n", "category": "generic"}
{"question_id": 569, "text": " I want to create a visual representation based on a short description: \\\"A magical forest with unicorns and a rainbow.\\\".\\n", "category": "generic"}
{"question_id": 570, "text": " Explain how to use GPT-3 to create a slogan for an e-commerce website that sells eco-friendly products.\\n", "category": "generic"}
{"question_id": 571, "text": " I'm working on a project that requires generating high-quality images of faces for a set of characters in a video game.\\n", "category": "generic"}
{"question_id": 572, "text": " Our client wants to analyze videos for their marketing website. They need a quick solution to categorize video content without worrying about accuracy.\\n", "category": "generic"}
{"question_id": 573, "text": " I want to classify images of vehicles including cars, motorcycles, trucks, and bicycles, based on their appearance.\\n", "category": "generic"}
{"question_id": 574, "text": " I need to classify images of animals into their specific categories.\\n", "category": "generic"}
{"question_id": 575, "text": " We are building a location recommendation system that identifies possible locations for new stores based on images from potential locations. Use the StreetCLIP model to generate probabilities for various cities.\\n", "category": "generic"}
{"question_id": 576, "text": " We are receiving mixed reviews about our new product and we need to understand customers' reactions.\\n", "category": "generic"}
{"question_id": 577, "text": " Detect if there are any harmful messages in a chat room.\\n", "category": "generic"}
{"question_id": 578, "text": " I am developing software that needs to retrieve relevant information from a collection of documents based on a user's query.\\n", "category": "generic"}
{"question_id": 579, "text": " Our client is a media organization that wants to extract named entities from a large collection of news articles in order to identify key people and places\\n", "category": "generic"}
{"question_id": 580, "text": " Extract entities from a provided sentence mentioning various companies and their CEOs.\\n", "category": "generic"}
{"question_id": 581, "text": " I am developing a food application where food keywords need to be extracted from user's input text. The model should be able to recognize food-related named entities.\\n", "category": "generic"}
{"question_id": 582, "text": " Create a script for an AI bot that automatically adds punctuation to users' messages in a chat app.\\n", "category": "generic"}
{"question_id": 583, "text": " I am writing an article on the history of technology companies, and I want to extract the names of companies and people mentioned in the text.\\n", "category": "generic"}
{"question_id": 584, "text": " I have a diary entry and want to identify the names of people and locations mentioned in it.\\n", "category": "generic"}
{"question_id": 585, "text": " Our company has a deal with international clients, and it's important to detect the proper locations of meetings from multilingual texts provided to us.\\n", "category": "generic"}
{"question_id": 586, "text": " We need to extract information from a table to answer a user's question regarding a company's revenue.\\n", "category": "generic"}
{"question_id": 587, "text": " Develop a table-based question answering system that can answer queries based on the input data.\\n", "category": "generic"}
{"question_id": 588, "text": " Create a tool that receives a table and a question in natural language, and returns an answer to the question based on the inputted table.\\n", "category": "generic"}
{"question_id": 589, "text": " We are a medical company providing health FAQs. We need to answer customers' questions accurately.\\n", "category": "generic"}
{"question_id": 590, "text": " A group of students are doing a project on European capitals. They need to know the capital city of Germany.\\n", "category": "generic"}
{"question_id": 591, "text": " Assist us in creating a question answering system to provide quick answers for customer inquiries.\\n", "category": "generic"}
{"question_id": 592, "text": " We are worried about price inflation in our country. Can you answer our questions on price inflation using the BERT large cased whole word masking finetuned model on SQuAD?\\n", "category": "generic"}
{"question_id": 593, "text": " I am interested in developing a system that can categorize my text messages into different subjects like finances, health, and entertainment.\\n", "category": "generic"}
{"question_id": 594, "text": " An online magazine editor is trying to clujster articles into topics. We want to classify articles into categories like sport, politics, health, and technology written in French.\\n", "category": "generic"}
{"question_id": 595, "text": " We have a user manual in English that needs to be translated into Spanish to be displayed on our Spanish website.\\n", "category": "generic"}
{"question_id": 596, "text": " We need a classifier that can sort movie synopses in German into three categories: crime, tragedy, and theft.\\n", "category": "generic"}
{"question_id": 597, "text": " Write an abstract about the impacts of social media on mental health, summarizing key findings from previous studies.\\n", "category": "generic"}
{"question_id": 598, "text": " Help me to translate a Spanish text to English. The Spanish text is: \\\"Lo siento, pero no puedo ir a la reuni\\u00f3n debido a una emergencia personal. Avisar\\u00e9 al equipo y nos pondremos en contacto para reprogramar la reuni\\u00f3n.\\\"\\n", "category": "generic"}
{"question_id": 599, "text": " I'm building a chatbot and I need it to respond to user questions based on the persona of an elderly person.\\n", "category": "generic"}
{"question_id": 600, "text": " Design a chatbot that can have conversations about a variety of topics with the users.\\n", "category": "generic"}
{"question_id": 601, "text": " We have a new platform that offers various services related to digital art, and we want it to be able to produce creative story ideas based on a short description. Can you help me providing suggestions?\\n", "category": "generic"}
{"question_id": 602, "text": " We are creating a chatbot to answer customer queries on our website. Please provide a way to generate human-like text.\\n", "category": "generic"}
{"question_id": 603, "text": " To improve customer satisfaction, we want to automate the creation of written explanations that sound more natural and less robotic. Can you suggest a way to generate text that sounds conscious and alive?\\n", "category": "generic"}
{"question_id": 604, "text": " Our company is growing and expanding into multiple countries. We need a solution for translating English sentences to German to ensure effective communication.\\n", "category": "generic"}
{"question_id": 605, "text": " We need a solution for creating Polish subtitles for YouTube videos in Spanish. The AI should provide the translation.\\n", "category": "generic"}
{"question_id": 606, "text": " I am an English teacher looking to brainstorm synonyms for the word \\\"happy.\\\" Can a model help me generate similar words?\\n", "category": "generic"}
{"question_id": 607, "text": " Generate a sentence which will complete the following: \\\"Hello, I'm a ...\\\"\\n", "category": "generic"}
{"question_id": 608, "text": " Can you help me find the best model to fill in the gap in my legal document? I want a smaller model with higher efficiency but maintains a high level of accuracy.\\n", "category": "generic"}
{"question_id": 609, "text": " I am learning English literature. I plan to build a system that has a huge database of English sentences and keeps the important ones.\\n", "category": "generic"}
{"question_id": 610, "text": " Our company specializes in providing information on similar topics. We want to find similar sentences in a text document.\\n", "category": "generic"}
{"question_id": 611, "text": " The company is building an AI chatbot, and we need a way to process users' questions and tell if two questions are similar.\\n", "category": "generic"}
{"question_id": 612, "text": " I am a CEO, and I want to create an audio announcement that will be played to my customers. The text announcement is: \\\"Dear valued customers, we are glad to announce a 30% discount on our special offer. Hurry up!\\\"\\n", "category": "generic"}
{"question_id": 613, "text": " Our user would like to develop an audiobook using a Text-to-Speech API. Convert the text of a book into an audio file.\\n", "category": "generic"}
{"question_id": 614, "text": " In our language app, we want to include text to speech functionality for Hokkien, a dialect of Chinese, using the TAT-TTS dataset.\\n", "category": "generic"}
{"question_id": 615, "text": " Develop an application to transcribe audio files with punctuation marks for a podcast platform.\\n", "category": "generic"}
{"question_id": 616, "text": " Our company has an audio archive of Chinese podcasts, and we want to create transcripts for them.\\n", "category": "generic"}
{"question_id": 617, "text": " We are a podcast platform. We have a library of multiple podcasts that are mixed with background noise. We need to enhance the podcast audio quality to improve the user experience on our platform.\\n", "category": "generic"}
{"question_id": 618, "text": " A podcast producer is looking to improve the quality of their audio files by removing background noise. What can they do?\\n", "category": "generic"}
{"question_id": 619, "text": " I work at a call center, and I need to convert a recorded customer call into an audio file with a different voice without changing the content.\\n", "category": "generic"}
{"question_id": 620, "text": " In a customer support system, we have received a recorded audio file from a customer complaining about an issue in Czech language. Translate it to English language preserving the audio format.\\n", "category": "generic"}
{"question_id": 621, "text": " We are a podcast editing company aiming at separating speakers from a recorded audio.\\n", "category": "generic"}
{"question_id": 622, "text": " We are building a virtual assistant which can translate English speech input to Hokkien in real-time.\\n", "category": "generic"}
{"question_id": 623, "text": " A language service wants to incorporate a speech-to-speech translation feature that assists users in translating Hokkien to English on an audio file.\\n", "category": "generic"}
{"question_id": 624, "text": " Our company is building an interactive platform for children to learn random trivia quickly. We need a model to understand what is being spoken and what category it belongs to.\\n", "category": "generic"}
{"question_id": 625, "text": " We are a company that develops voice assistants, and we need to verify a user's voice to authenticate them.\\n", "category": "generic"}
{"question_id": 626, "text": " We recently launched a customer service hotline, and we want to estimate the demographics of callers.\\n", "category": "generic"}
{"question_id": 627, "text": " Create a smart speaker that can recognize voice commands such as \\\"Turn on the lights,\\\" \\\"Play music,\\\" or \\\"Set a timer.\\\"\\n", "category": "generic"}
{"question_id": 628, "text": " Our company's voice assistant needs to be able to detect voice activity in a conversation.\\n", "category": "generic"}
{"question_id": 629, "text": " Implement a conference call control program which can recognize if there are any interruptions among the speakers during the meeting.\\n", "category": "generic"}
{"question_id": 630, "text": " We are trying to create a solution for an HR department to predict whether a candidate would be a potential employee based on a list of background information.\\n", "category": "generic"}
{"question_id": 631, "text": " I have just downloaded a dataset that measures CO2 Emissions. I want to classify this dataset using a pre-trained model.\\n", "category": "generic"}
{"question_id": 632, "text": " We are building an app that simulates Pokemon battles. Can you help us predict the HP of a Pokemon given its input attributes?\\n", "category": "generic"}
{"question_id": 633, "text": " We are a content marketing agency and we are focusing on promoting our clients' products. We want to use a tool to generate interesting marketing messages.\\n", "category": "generic"}
{"question_id": 634, "text": " Analyze a set of sentences to find the most similar pairs.\\n", "category": "generic"}
{"question_id": 635, "text": " I need a tool to extract code syntax and named entities from a text taken from StackOverflow.\\n", "category": "generic"}
{"question_id": 636, "text": " Our video streaming platform wants to categorize thousands of movies into genres. Please help us classify them without any genre labels.\\n", "category": "generic"}
{"question_id": 637, "text": " Can you generate an image of a lighthouse on a foggy island, based on my description?\\n", "category": "generic"}
{"question_id": 638, "text": " I am an illustrator, I want to create an appealing image based on a text description for commercial purposes.\\n", "category": "generic"}
{"question_id": 639, "text": " We want to build a Twitter Bot that creates an image based on users' textual requests. Generate an image with an astronaut playing guitar in space using a model.\\n", "category": "generic"}
{"question_id": 640, "text": " Generate an image of a beautiful fantasy landscape based on the description provided: a peaceful scene in a lush green forest with a crystal-clear river flowing through it, under a blue sky with fluffy white clouds.\\n", "category": "generic"}
{"question_id": 641, "text": " Tell me a text summary and answer a question from an image.\\n###Input: img_url=\\\"https://example.com/image.jpg\\\", question=\\\"What is the main color of the object?\\\"\\n", "category": "generic"}
{"question_id": 642, "text": " My boss wants me to extract captions from images of people in different settings.\\n", "category": "generic"}
{"question_id": 643, "text": " Create an AI system capable of producing short videos based on text input in Persian and English.\\n", "category": "generic"}
{"question_id": 644, "text": " I am organizing a virtual party and want to create a short, autogenerated video based on a text description (e.g., \\\"cats playing with laser pointer\\\"). Can you accomplish this with a text-to-video generation API?\\n", "category": "generic"}
{"question_id": 645, "text": " I need to create a system that can answer questions related to a document provided. The system should use a pre-trained model.\\n", "category": "generic"}
{"question_id": 646, "text": " We are developing a document management system. We want to extract the relevant information from them through OCR text scanning and answering questions.\\n", "category": "generic"}
{"question_id": 647, "text": " The team is creating a home security system. We are currently looking at understanding the depth of objects in the video stream.\\n", "category": "generic"}
{"question_id": 648, "text": " I want to know the depth information of an image for a robot navigation project.\\n", "category": "generic"}
{"question_id": 649, "text": " A start-up is looking to develop a robot for navigation; they require a solution to estimate depth from a single image.\\n", "category": "generic"}
{"question_id": 650, "text": " We are building a smart security system to determine if a person is an adult\\n", "category": "generic"}
{"question_id": 651, "text": " Design a program that can help farmers detect diseases in bean crops by analyzing images of the crop leaves.\\n", "category": "generic"}
{"question_id": 652, "text": " To build an intelligent system to recognize and identify objects in submitted pictures within the household, which includes furniture, electronics and ornaments.\\n", "category": "generic"}
{"question_id": 653, "text": " Give me a solution to find out the license plate in the given car images.\\n", "category": "generic"}
{"question_id": 654, "text": " I want to build a social media app for outdoor enthusiasts, and I need to identify objects related to outdoor activities in images. Come up with a proper API call to identify these objects.\\n", "category": "generic"}
{"question_id": 655, "text": " I want an AI that can separate objects in an image based on semantic segmentation.\\n", "category": "generic"}
{"question_id": 656, "text": " Our image-processing service has to extract contextual information from images.\\n", "category": "generic"}
{"question_id": 657, "text": " Our company develops a city planning application. We need to segment streets, buildings, and trees in aerial photographs.\\n", "category": "generic"}
{"question_id": 658, "text": " We are building a product which can identify birds in the images. Design the model which can help us segment the birds in an image.\\n", "category": "generic"}
{"question_id": 659, "text": " In this Star Wars movie scene, I want to create a depth estimation for the stormtroopers.\\n", "category": "generic"}
{"question_id": 660, "text": " I am a drone maker that is building a navigation module for my drones. I need to sharpen the images captured from the drone in real-time.\\n", "category": "generic"}
{"question_id": 661, "text": " Develop a method to generate images of realistic-looking churches.\\n", "category": "generic"}
{"question_id": 662, "text": " Generate a classical image by using Diffusion Model\\n", "category": "generic"}
{"question_id": 663, "text": " We need a picture that has a nostalgic look in high quality for the cover of our upcoming magazine.\\n", "category": "generic"}
{"question_id": 664, "text": " We are an e-learning provider who wants to classify the content of a video lecture automatically.\\n", "category": "generic"}
{"question_id": 665, "text": " We need to classify actions of athletes in sports videos. Can you help us to analyze and classify these videos?\\n", "category": "generic"}
{"question_id": 666, "text": " We want to classify images supplied by our users into categories such as cats, dogs, birds, and more.\\n", "category": "generic"}
{"question_id": 667, "text": " Identify which smartphone brand is featured in an image and predict the intensity of luxury level.\\n", "category": "generic"}
{"question_id": 668, "text": " A Chinese social media company has approached us for help in content moderation. We recorded an API to classify images which don't belong to suitable content.\\n", "category": "generic"}
{"question_id": 669, "text": " We are working on environmental studies and there is a specific image we need to geolocalize. Let's get the probabilities of different cities for the given image.\\n", "category": "generic"}
{"question_id": 670, "text": " Our e-commerce platform needs automatic classification of product images without retraining. Develop a classifier for the e-commerce platform.\\n", "category": "generic"}
{"question_id": 671, "text": " We are developing a customer support platform for our telecommunication company in Spain. We want to know if they are happy or unhappy with our services, based on the content of their message.\\n", "category": "generic"}
{"question_id": 672, "text": " I need a function to find the most relevent passage given a question and several candidate passages.\\n", "category": "generic"}
{"question_id": 673, "text": " As a community manager, I would like to monitor my forum's comment section for toxic or harmful content. I want to find a solution that can flag these types of comments automatically, so I can address them promptly.\\n", "category": "generic"}
{"question_id": 674, "text": " I am making a keyword search engine that ranks text passages based on their importance regarding a given keyword.\\n", "category": "generic"}
{"question_id": 675, "text": " Identify the type of emotion in a movie review.\\n", "category": "generic"}
{"question_id": 676, "text": " We are developing a medical records analysis software that automatically recognizes biomedical entities from physicians' case reports.\\n", "category": "generic"}
{"question_id": 677, "text": " We have customer reviews of various software products. We want to extract company names in those reviews.\\n", "category": "generic"}
{"question_id": 678, "text": " I am working on a Chinese language project and I need to tokenize the sentences for better processing.\\n", "category": "generic"}
{"question_id": 679, "text": " I need a system that extracts all the well-known named entities such as person names, locations, and organizations from news articles.\\n", "category": "generic"}
{"question_id": 680, "text": " I have a list of Olympic Game host cities and their corresponding years. I want to know which year the games were held in Beijing.\\n", "category": "generic"}
{"question_id": 681, "text": " I have a table with data about different types of bards and their magical abilities. Let me see if I can find the best bard given the data in the table.\\n", "category": "generic"}
{"question_id": 682, "text": " I want to automate the process of answering questions about historical facts. When given a question and a surrounding context, it should provide an accurate response.\\n", "category": "generic"}
{"question_id": 683, "text": " I am a lawyer, I have a lot of text. I need a system which reads context and answers the questions based on the context.\\n", "category": "generic"}
{"question_id": 684, "text": " I want to create a question answering script that can help me answer questions about a given passage of text.\\n", "category": "generic"}
{"question_id": 685, "text": " To enhance our FAQ bot, we need to extract answers from a given knowledge base text.\\n", "category": "generic"}
{"question_id": 686, "text": " Our customer is a Spanish travel agency. They need to classify customer reviews into categories such as 'travel', 'cooking', and 'dancing'.\\n", "category": "generic"}
{"question_id": 687, "text": " Create a method to determine, as an entertainment recommendation system, which category a text message about a daily activity belongs to.\\n", "category": "generic"}
{"question_id": 688, "text": " We have written a summary of a new book's plot. Now, we want to ensure if the summary contains conflicting information.\\n", "category": "generic"}
{"question_id": 689, "text": " We have a user comment about a product, and we want to infer if the sentiment of the comment is positive or negative.\\n###Input: \\\"I recently purchased this product and it completely exceeded my expectations! The build quality is top-notch, and I've already recommended it to several friends.\\\"\\n", "category": "generic"}
{"question_id": 690, "text": " Extract a conclusion from the following text: \\\"Studies have been shown that owning a dog is good for you. Having a dog can help decrease stress levels, improve your mood, and increase physical activity.\\\"\\n###Input: Studies have been shown that owning a dog is good for you. Having a dog can help decrease stress levels, improve your mood, and increase physical activity.\\n", "category": "generic"}
{"question_id": 691, "text": " Our company needs a versatile NLP model to build a social media manager to generate summaries of lengthy articles for sharing on social media.\\n", "category": "generic"}
{"question_id": 692, "text": " We have a real estate website and would like to translate property descriptions in English to French.\\n", "category": "generic"}
{"question_id": 693, "text": " My friend wrote me an English text about the upcoming holiday. I need to translate it to Arabic so I can share it with my family.\\n###Input: \\\"My friend is planning a holiday trip for our families. He found a beautiful place with a beach, swimming pool, and a wide range of outdoor activities for kids. There's also a famous seafood restaurant nearby! I think our families will have a great time together.\\\"\\n", "category": "generic"}
{"question_id": 694, "text": " We received a customer feedback document which is very lengthy. We need a summarization of it.\\n###Input: The customer support service was excellent. All our concerns were attended to promptly by the friendly and knowledgeable staff. The user interface, however, could use some improvement. Navigating through the platform can be challenging, and it took us quite some time to find the relevant information we needed.\\nAdditionally, some of our team members faced technical issues while using the platform, particularly with the integration of third-party tools. We had to reach out to the support team multiple times to resolve these issues. Overall, while we genuinely appreciate your team's assistance, we expect better performance from the platform itself.\\n", "category": "generic"}
{"question_id": 695, "text": " I would like to give a brief overview of our team meeting to my supervisor, so I need a summary of the conversation.\\n###Input: \\\"Anna: In today's meeting, we discussed increasing marketing budget. Tom: I suggested allocating more funds to social media campaigns. Sarah: I proposed focusing on improving SEO. Anna: We agreed on investing in content creation, too. Tom: The team will revise the strategy and present it next week. Sarah: Let's determine new KPIs for evaluating our progress.\\\"\\n", "category": "generic"}
{"question_id": 696, "text": " We need to develop a conversational chatbot that can answer users' queries, ask questions, and have a friendly conversation.\\n", "category": "generic"}
{"question_id": 697, "text": " My daughter left her laptop logged in, and I found her English essay unfinished. There's a sentence that goes like \\\"In the story, the antagonist represents the <mask> nature of humanity.\\\" Can you help me complete her sentence with an appropriate word?\\n", "category": "generic"}
{"question_id": 698, "text": " We are developing a program to teach French to English speakers. The program should complete a sentence with a missing word in French.\\n", "category": "generic"}
{"question_id": 699, "text": " I have a multilingual document, and there is a missing word in the document. Please help me find out what this missing word might be.\\n", "category": "generic"}
{"question_id": 700, "text": " As a translation company, we are translating messages between co-workers in a multinational company. Translate the message from Hindi to French.\\n###Input: \\\"\\u0906\\u092a\\u0915\\u0940 \\u092a\\u094d\\u0930\\u0947\\u091c\\u093c\\u091f\\u0947\\u0936\\u0928 \\u0915\\u093e \\u0906\\u0927\\u093e\\u0930 \\u0905\\u091a\\u094d\\u091b\\u093e \\u0925\\u093e, \\u0932\\u0947\\u0915\\u093f\\u0928 \\u0921\\u0947\\u091f\\u093e \\u0935\\u093f\\u0936\\u094d\\u0932\\u0947\\u0937\\u0923 \\u092a\\u0930 \\u0927\\u094d\\u092f\\u093e\\u0928 \\u0926\\u0947\\u0928\\u093e \\u091a\\u093e\\u0939\\u093f\\u090f\\u0964\\\"\\n", "category": "generic"}
{"question_id": 701, "text": " Our task is to complete a given sentence with a missing word. The sentence is from an electronic health record.\\n", "category": "generic"}
{"question_id": 702, "text": " A Dutch friend asked for help in completing a sentence with a missing word. Can you fill in the blank?\\n###Input: \\\"Het is vandaag erg koud, dus vergeet niet je ___ mee te nemen.\\\"\\n", "category": "generic"}
{"question_id": 703, "text": " Our school needs to create fill-in-the-blank quizzes for students. Is it possible to generate a fill-in-the-blank question from the following sentence: \\\"The cat chased the mouse and then climbed the tree.\\\"\\n", "category": "generic"}
{"question_id": 704, "text": " We are building a customer support chatbot that needs to find the most related FAQ for a given customer query.\\n", "category": "generic"}
{"question_id": 705, "text": " We want to make a system that reads our email messages out loud in different voices.\\n", "category": "generic"}
{"question_id": 706, "text": " We are working on a language learning app for Chinese. We need to give audio examples for each lesson.\\n", "category": "generic"}
{"question_id": 707, "text": " We are building an AI chatbot that reads out user messages using synthesized human-like speech. For this purpose, we need to convert text messages to audio. \\n", "category": "generic"}
{"question_id": 708, "text": " I want to identify when people are speaking in an audio file.\\n", "category": "generic"}
{"question_id": 709, "text": " I have a collection of audio recordings from different sources, and I want to convert them into text to make an archive.\\n", "category": "generic"}
{"question_id": 710, "text": " A company developing an application for transcribing customer service calls requires a model that can understand spoken language.\\n", "category": "generic"}
{"question_id": 711, "text": " We are building an audio assistant. Apply noise suppression to our new voice commands.\\n", "category": "generic"}
{"question_id": 712, "text": " We have an audio recording with overlapping speakers talking. We need to remove the overlaps and make it a clear single speaker recording.\\n", "category": "generic"}
{"question_id": 713, "text": " We have just built an app for hosting podcasts, and we need a speech enhancement model to clean noise from the audio recordings submitted by our users.\\n", "category": "generic"}
{"question_id": 714, "text": " My coworker sent me a voice message in Spanish. Translate this audio message to English, so that I can understand it.\\n###Input: spanish_voice_message.wav\\n", "category": "generic"}
{"question_id": 715, "text": " We are developing a virtual assistant and need to integrate a speech enhancement feature in it.\\n", "category": "generic"}
{"question_id": 716, "text": " A kid's educational toy company is including a voice assistant that recognizes simple voice commands from children. They need an example on how to recognize spoken digits.\\n", "category": "generic"}
{"question_id": 717, "text": " Our company needs to analyze customer phone call recordings and identify specific numbers mentioned by customers during the call.\\n", "category": "generic"}
{"question_id": 718, "text": " You are building a virtual global tour guide that can identify languages from the audio of people speaking. Use a model to identify which language is being spoken.\\n", "category": "generic"}
{"question_id": 719, "text": " I am working as the head of customer service for a Spanish speaking market. I want to know the sentiment of my customers on their last call with our support agents.\\n", "category": "generic"}
{"question_id": 720, "text": " I have a recording of a meeting from which I need to extract the parts where people are talking and eliminate the silent parts.\\n", "category": "generic"}
{"question_id": 721, "text": " We are a company producing wine. Based on the chemical properties of our wine data, we need to analyze the quality of our products and determine whether they are good or bad.\\n", "category": "generic"}
{"question_id": 722, "text": " Our team is working on a podcast app, and we want to automatically generate transcripts with timestamps for each speaker.\\n", "category": "generic"}
{"question_id": 723, "text": " I have a large dataset with both numerical and categorical features related to customer behavior. I want to classify them into different segments for targeted marketing. How do I use a TabTransformer for this task?\\n", "category": "generic"}
{"question_id": 724, "text": " Determine if an employee's annual salary meets or exceeds $50000.\\n", "category": "generic"}
{"question_id": 725, "text": " Identify whether a newly-setup chemical plant is exceeding carbon emission limits based on a CSV file containing data collected.\\n###Input: data.csv\\n", "category": "generic"}
{"question_id": 726, "text": " I am working in a bank, I want to estimate the mortgage for a given housing using the housing's features.\\n", "category": "generic"}
{"question_id": 727, "text": " We are building a robot for hopping in a controlled environment. Train it to perform a hop using Decision Transformers.\\n**Input:<noinput>**\\n", "category": "generic"}
{"question_id": 728, "text": " We are building a robot for elderly care. The robot should be able to understand what's happening and take appropriate actions based on the elderly's current activities.\\n", "category": "generic"}
{"question_id": 729, "text": " To build a source code recommendation engine, we need to extract features from text and code using Transformer models.\\n", "category": "generic"}
{"question_id": 730, "text": " Our team needs to prepare a feature matrix based on the given source code's programming constructs and comments for data analysis.\\n", "category": "generic"}
{"question_id": 731, "text": " We are developing an interactive story app that would display a picture of each character as mentioned in the story given to our GPT-3 model.\\n", "category": "generic"}
{"question_id": 732, "text": " As a landscape architect, I want to generate a description of an image of a park I've designed to use for promotional purposes.\\n", "category": "generic"}
{"question_id": 733, "text": " I need to build a solution that generates textual descriptions for images in my database.\\n", "category": "generic"}
{"question_id": 734, "text": " We are a company managing hotel bookings. We need to answer our customer's questions regarding rental rates from our pricing document.\\n", "category": "generic"}
{"question_id": 735, "text": " Provide me a pipeline for extracting relevant information from an insurance policy document.\\n", "category": "generic"}
{"question_id": 736, "text": " Create an accurate system to estimate the distance between the camera and the objects in a photo captured by a drone flying over a terrain.\\n", "category": "generic"}
{"question_id": 737, "text": " We are a team of architects and civil engineers looking to estimate the depth of elements in architectural designs from 2D images of the structures. We want to implement a depth estimation model that will transform these images into depictions of depth.\\n", "category": "generic"}
{"question_id": 738, "text": " I have an image on my computer named \\\"eye.jpg\\\". I want to use a model to predict whether this image indicates diabetic retinopathy.\\n", "category": "generic"}
{"question_id": 739, "text": " We need to build a model that recognizes objects in images. Build a model using the Vision Transformer (ViT) for this purpose.\\n", "category": "generic"}
{"question_id": 740, "text": " My pet store website needs a tool to recognize different dog breeds from user uploaded images.\\n", "category": "generic"}
{"question_id": 741, "text": " As a programmer, I want to classify different animal species based on their images. Help me determine the type of species. \\n", "category": "generic"}
{"question_id": 742, "text": " We are a robotics company specializing in object detection for logistics. We need to detect objects in warehouses using an advanced object detection model.\\n", "category": "generic"}
{"question_id": 743, "text": " An IoT device collects images from different locations. Create a model to detect objects in these images to analyze the surroundings.\\n", "category": "generic"}
{"question_id": 744, "text": " I'm building a drone surveillance system for detecting airplanes in the sky. What can I use to achieve the required object detection?\\n", "category": "generic"}
{"question_id": 745, "text": " We are building an IoT device to monitor apartment corridors for security. Please detect objects in the image and notify if anything abnormal is detected.\\n", "category": "generic"}
{"question_id": 746, "text": " Design an application for a smart drone that can detect and segment objects within the area it's flying.\\n", "category": "generic"}
{"question_id": 747, "text": " I want to build a tool to recognize urban landscapes and identify different objects in the image.\\n", "category": "generic"}
{"question_id": 748, "text": " Show me how to detect defects of PCB boards from an image in real-time.\\n", "category": "generic"}
{"question_id": 749, "text": " I want to assess the condition of roads in a city by analyzing drone footage. Create an image segmentation model to identify potholes in images.\\n", "category": "generic"}
{"question_id": 750, "text": " We are building a road maintenance reporting application. We need to use images to identify and segment road potholes.\\n", "category": "generic"}
{"question_id": 751, "text": " The marketing team needs different variations of a product image to use in advertising and promotional materials.\\n", "category": "generic"}
{"question_id": 752, "text": " A movie studio needs to estimate the human pose of an actor from an image for an upcoming film project.\\n", "category": "generic"}
{"question_id": 753, "text": " Create a program to determine the depth map from an input image of a street filled with people.\\n", "category": "generic"}
{"question_id": 754, "text": " I need to create a new piece of art to add to my digital gallery that resembles WikiArt images.\\n", "category": "generic"}
{"question_id": 755, "text": " Our team is designing a butterfly-themed stationery set for children. We need to generate images of cute butterflies to use in the design.\\n", "category": "generic"}
{"question_id": 756, "text": " Design a mobile application that suggests pictures of butterflies whenever the user attempts to change their theme.\\n", "category": "generic"}
{"question_id": 757, "text": " We need to generate some vintage images for a promotion poster.\\n", "category": "generic"}
{"question_id": 758, "text": " Generate images of cute butterflies using myunus1/diffmodels_galaxies_scratchbook model.\\n", "category": "generic"}
{"question_id": 759, "text": " As part of the online platform's development, we need an AI that, given input text about a video, estimates the content of the video and judges its category.\\n", "category": "generic"}
{"question_id": 760, "text": " We need to develop a model to classify sports clips by identifying the type of sports being played in the video.\\n", "category": "generic"}
{"question_id": 761, "text": " We are designing an application for professional athletes. They need a tool to categorize their exercises based on videos.\\n", "category": "generic"}
{"question_id": 762, "text": " We are building a video analysis tool that can automatically detect the main action happening in a given video clip.\\n", "category": "generic"}
{"question_id": 763, "text": " I would like to create an application that identifies animals in Chinese language image captions. Specifically, we want to know if a picture includes a cat or a dog.\\n", "category": "generic"}
{"question_id": 764, "text": " I need a portable system to perform quick sentiment analysis of customer reviews.\\n", "category": "generic"}
{"question_id": 765, "text": " A language model has identified a recent book review and provided a summary of its content. Perform sentiment analysis on the summary to evaluate the reviewer's feelings.\\n", "category": "generic"}
{"question_id": 766, "text": " We have movie reviews on our website, and we need to display whether the review is positive or negative.\\n", "category": "generic"}
{"question_id": 767, "text": " I wish to automatically classify a given text's emotion. What Transformers model should I use?\\n", "category": "generic"}
{"question_id": 768, "text": " Develop a tool to analyze restaurant reviews from Yelp for positive or negative sentiments.\\n", "category": "generic"}
{"question_id": 769, "text": " We're creating a chatbot that will detect the user's emotion. I want to start by implementing the basic functionality of emotion detection from the user's responses.\\n", "category": "generic"}
{"question_id": 770, "text": " I am a journalist. I am writing an article about European start-ups. I need to see all the dates and company names in the text.\\n", "category": "generic"}
{"question_id": 771, "text": " Create a tool to extract entities from news articles to help journalists research important figures and organizations.\\n", "category": "generic"}
{"question_id": 772, "text": " A famous writer is working on a novel. He needs your help to predict the punctuation marks needed in his written draft.\\n", "category": "generic"}
{"question_id": 773, "text": " There's a collection of texts that we want to be able to analyze for their part-of-speech tags to better understand the structure of the sentences within the texts.\\n", "category": "generic"}
{"question_id": 774, "text": " I am working on a project where I want to make predictions from my data that is stored in structured tables. Find a pre-trained model for table question answering.\\n", "category": "generic"}
{"question_id": 775, "text": " I have a table in CSV format and a query related to it. Could you obtain an answer for my query?\\n", "category": "generic"}
{"question_id": 776, "text": " Develop a tool that helps me get answers to questions related to a specific text.\\n", "category": "generic"}
{"question_id": 777, "text": " A healthcare professional wants to get quick answers to COVID-19 related questions from the latest research articles.\\n", "category": "generic"}
{"question_id": 778, "text": " I have this app for sharing cooking recipes. Users upload photos and ask questions about the showcased recipe. I need to automatically answer their questions based on the recipe image provided.\\n", "category": "generic"}
{"question_id": 779, "text": " The company's legal team is working on a case. They need a highly accurate tool to extract answers from a large set of legal documents. Develop a tool for this purpose.\\n", "category": "generic"}
{"question_id": 780, "text": " We have received a customer inquiry. Help us categorize the inquiry into one of the following categories: \\\"sales\\\", \\\"technical support\\\", or \\\"billing\\\".\\n###Input: \\\"I am experiencing difficulty with the installation process of your software.\\\"\\n", "category": "generic"}
{"question_id": 781, "text": " Imagine you have a news aggregator platform, and you want it to separate news articles into categories like Politics, Sports, Technology, Business, and Entertainment. Implement the classification using this API.\\n", "category": "generic"}
{"question_id": 782, "text": " I need to classify German news articles into categories like crime, tragedy, and theft. The classifier should be able to understand German as well.\\n", "category": "generic"}
{"question_id": 783, "text": " The company has just received a document written in French, and they need it translated into English.\\n", "category": "generic"}
{"question_id": 784, "text": " A business collaboration project requires staff to translate Russian documents into English.\\n", "category": "generic"}
{"question_id": 785, "text": " In this fast world, a user spends very little time on reading news articles and requires summary of the news articles.\\n", "category": "generic"}
{"question_id": 786, "text": " We need to summarize a scientific article. The input must include all the important points discussed in the article, and the result should be a concise abstraction of the content.\\n", "category": "generic"}
{"question_id": 787, "text": " Now I need to create a summary of my chat with my friend last night.\\n###Input: conversation = '''Hannah: Hey, do you have Betty's number?\\nAmanda: Lemme check\\nAmanda: Sorry, can't find it.\\nAmanda: Ask Larry\\nAmanda: He called her last time we were at the park together\\nHannah: I don't know him well\\nAmanda: Don't be shy, he's very nice\\nHannah: If you say so..\\nHannah: I'd rather you texted him\\nAmanda: Just text him \\ud83d\\ude42\\nHannah: Urgh.. Alright\\nHannah: Bye\\nAmanda: Bye bye\\n'''\\n", "category": "generic"}
{"question_id": 788, "text": " As a news agency, we need a summarized version of a recent article about YouTube's new policy on vaccine misinformation.\\n###Input: Videos that say approved vaccines are dangerous and cause autism, cancer or infertility are among those that will be taken down, the company said. The policy includes the termination of accounts of anti-vaccine influencers. Tech giants have been criticised for not doing more to counter false health information on their sites. In July, US President Joe Biden said social media platforms were largely responsible for people's scepticism in getting vaccinated by spreading misinformation, and appealed for them to address the issue. YouTube, which is owned by Google, said 130,000 videos were removed from its platform since last year, when it implemented a ban on content spreading misinformation about Covid vaccines. In a blog post, the company said it had seen false claims about Covid jabs spill over into misinformation about vaccines in general. The new policy covers long-approved vaccines, such as those against measles or hepatitis B. We're expanding our medical misinformation policies on YouTube with new guidelines on currently administered vaccines that are approved and confirmed to be safe and effective by local health authorities and the WHO, the post said, referring to the World Health Organization.\\n", "category": "generic"}
{"question_id": 789, "text": " Let's build a Chatbot that can solve daily life problems of a research Assistant.\\n", "category": "generic"}
{"question_id": 790, "text": " We need to integrate the personal assistant we're building with the capability of having conversations with people, sometimes answering general knowledge questions.\\n", "category": "generic"}
{"question_id": 791, "text": " I want to create a chatbot for discussing gardening topics and tips, which can refer to external information while generating responses.\\n", "category": "generic"}
{"question_id": 792, "text": " We're trying to help out a friend who's developing an application for composing text. He is trying to create a bot that comes up with creative ideas for your paragraph.\\n", "category": "generic"}
{"question_id": 793, "text": " We are creating an AI newsletter application that generates summaries of news articles. We need the AI to generate a brief summary for a given article.\\n", "category": "generic"}
{"question_id": 794, "text": " I want a code completion tool to assist with finishing my incomplete Python code.\\n", "category": "generic"}
{"question_id": 795, "text": " We are making an AI copywriter for marketing content. Help me to provide content for a product relating to eco-friendly kitchenware.\\n", "category": "generic"}
{"question_id": 796, "text": " I am an astronaut in space, writing my diary every day. I need a summary of my diary before sharing it with my family.\\n", "category": "generic"}
{"question_id": 797, "text": " I have some paragraphs that I want to use as a source for generating questions, based on the information present in them.\\n", "category": "generic"}
{"question_id": 798, "text": " I am a novel writer. I plan to write some paragraphs, but I cannot find the exact word placeholder or missing word. Can you help me make a tool to complete the given text below?\\n", "category": "generic"}
{"question_id": 799, "text": " I am working in a dating chatapp development team. We want to generate sentences to make it more interactive.\\n", "category": "generic"}
{"question_id": 801, "text": " I am building a recommendation engine to group news articles. I need a way to determine the similarity between two sentences.\\n", "category": "generic"}
{"question_id": 802, "text": " The company needs a tool to analyze customers' reviews about their products. We need to find out which ones are positive, neutral, or negative.\\n", "category": "generic"}
{"question_id": 803, "text": " I am working on a project that ranks answers to questions based on their relevance. Can you help me find the most relevant answer to a specific question by using sentence similarity?\\n", "category": "generic"}
{"question_id": 804, "text": " Assist a client producing an audiobook in Japanese. They need a solution to convert their text script into spoken Japanese.\\n", "category": "generic"}
{"question_id": 805, "text": " We have a product for the German-speaking audience, and we need to generate some sample voiceovers for the promotional videos.\\n", "category": "generic"}
{"question_id": 806, "text": " I work as a transcriptionist, and I have an hour-long recording of a meeting. I need to identify and segment the speech of various speakers in the recording.\\n", "category": "generic"}
{"question_id": 807, "text": " We are a company focusing on transcription services for Arabic speakers. We need to convert their speech to text.\\n", "category": "generic"}
{"question_id": 808, "text": " An audio file is recorded in a conference and we need the text version of the conversation for record-keeping purposes.\\n", "category": "generic"}
{"question_id": 809, "text": " Provide a way to translate spoken English to spoken Hokkien for an audio file.\\n", "category": "generic"}
{"question_id": 810, "text": " We want to separate the speaker sources from the original audio file to filter the noise.\\n", "category": "generic"}
{"question_id": 811, "text": " Our goal is to analyze the emotions expressed in a user's recorded message.\\n", "category": "generic"}
{"question_id": 812, "text": " I want to make a conference call app which differentiates between the times when the user is speaking and when there is no voice activity. \\n", "category": "generic"}
{"question_id": 813, "text": " We want to analyze a conference call recording to identify the speakers and the segments of the conversation they participated in.\\n", "category": "generic"}
{"question_id": 814, "text": " Our company wants to predict the potential negative impact on the environment based on certain factors.\\n", "category": "generic"}
{"question_id": 815, "text": " I have a dataset with CO2 emissions in a CSV file, and I want to classify which sources have high or low emissions.\\n", "category": "generic"}
{"question_id": 816, "text": " I am building a recommendation engine to recommend linkbuilding strategy to clients. I have data in csv files that needs to be classified.\\n", "category": "generic"}
{"question_id": 817, "text": " Create a model to estimate the carbon emissions of a specific device by using features like idle power, standby power, and active power.\\n", "category": "generic"}
{"question_id": 818, "text": " We have a virtual reality investment in soccer simulations. We need an intelligent learning-based soccer player to make it more engaging and challenging.\\n", "category": "generic"}
{"question_id": 819, "text": " I want to build an AI that identifies the best marketing strategies for my website by trying different combinations of headlines and images.\\n", "category": "generic"}
{"question_id": 820, "text": " A team is working on a video game where the player needs to land the spaceship on the lunar surface without crashing. They want to implement an AI module that can play the game and test it.\\n", "category": "generic"}
{"question_id": 821, "text": " Develop an AI character that can play the SoccerTwos game with advanced strategies.\\n", "category": "generic"}
{"question_id": 822, "text": " Our gaming company is looking for a reinforcement learning solution to implement an artificial agent that can play SoccerTwos proficiently.\\n", "category": "generic"}
{"question_id": 823, "text": " Develop a language understanding feature for a chatbot that can help tourists with information on visiting Indonesia.\\n", "category": "generic"}
{"question_id": 824, "text": " I need to analyze images in real-time feeds from different locations for object recognition.\\n", "category": "generic"}
{"question_id": 825, "text": " Our travel agency needs to build a chatbot that helps tourists find the best attractions in various destinations. The chatbot should answer questions related to tourist attractions.\\n", "category": "generic"}
{"question_id": 826, "text": " We want to develop a chatbot that can engage with multilingual users. Please help us create a model to encode sentences and understand user input in various languages.\\n", "category": "generic"}
{"question_id": 827, "text": " We need to create a demo for a 3D printing company by generating images of some examples that the business will print.\\n", "category": "generic"}
{"question_id": 828, "text": " I work at an art school and our professor wants to create an AI chatbot that can study an image of a painting and answer questions about it.\\n", "category": "generic"}
{"question_id": 829, "text": " We are working on a project to develop an AI-based method to provide answers to questions about charts and diagrams. Please provide the necessary code for using the Pix2Struct model.\\n", "category": "generic"}
{"question_id": 830, "text": " Create a personalized online ad campaign for our new clothing line. The ad should generate a video showing a man wearing a stylish suit while walking in the city.\\n", "category": "generic"}
{"question_id": 831, "text": " We need to generate a short video showing Spider-Man water skiing in redshift style based on a textual prompt.\\n", "category": "generic"}
{"question_id": 832, "text": " Our company develops travel guides in Polish language. We are now planning to use artificial intelligence for quick captioning of Polish images.\\n", "category": "generic"}
{"question_id": 833, "text": " A user has difficulty in visualizing images and needs help answering questions about an image. We need a model to answer questions based on the image provided.\\n", "category": "generic"}
{"question_id": 834, "text": " A real estate company wants to extract information from a scan of a property listing. They need a solution to recognize and retrieve critical information from the OCR of the scan.\\n", "category": "generic"}
{"question_id": 835, "text": " We want to utilize the machine learning model for predicting molecular properties in the drug discovery domain.\\n", "category": "generic"}
{"question_id": 836, "text": " We need to estimate the depth of a scene in an image using a pretrained model. Can you please suggest a way?\\n", "category": "generic"}
{"question_id": 837, "text": " Our company is working on an autonomous robot and needs a solution to estimate the depth of objects in its environment.\\n", "category": "generic"}
{"question_id": 838, "text": " I want to create a video streaming app for plants, in which plants are categorized by species. I need to find the species of plants in an image.\\n", "category": "generic"}
{"question_id": 839, "text": " Develop an image classifier for distinguishing images of cats and dogs.\\n", "category": "generic"}
{"question_id": 840, "text": " We are building AI glasses that should tell us about things that we are seeing with classifications. We want to use a visual transformer architecture.\\n", "category": "generic"}
{"question_id": 841, "text": " A marketing firm has asked us to build an application to classify social media images into various categories.\\n", "category": "generic"}
{"question_id": 842, "text": " John is a car enthusiast and he wants to build an application that can recognize the car brand in a picture he takes.\\n", "category": "generic"}
{"question_id": 843, "text": " We are building an app to help Counter-Strike: Global Offensive players improve their gameplay. We need to detect players in a live game of CS:GO.\\n", "category": "generic"}
{"question_id": 844, "text": " The security department needs assistance to detect suspicious objects and people using a zero-shot text-conditioned object detection system.\\n", "category": "generic"}
{"question_id": 845, "text": " As a specialist in computer vision, we need to use the OwlViT model to identify objects in an image described by specific text phrases like \\\"a photo of a cat\\\" and \\\"a photo of a dog.\\\"\\n", "category": "generic"}
{"question_id": 846, "text": " A medical research team requests an automated procedure for detecting blood cells in microscopic images of blood samples. Develop a solution to address this need.\\n", "category": "generic"}
{"question_id": 847, "text": " Our client has a traffic camera system and wants to detect vehicles in the images captured. Implement a solution.\\n", "category": "generic"}
{"question_id": 848, "text": " Detect the location of players in an image from a Counter-Strike: Global Offensive (CS:GO) game.\\n###Input: image_path = \\\"path/to/your/csgo_image.jpg\\\"\\n", "category": "generic"}
{"question_id": 849, "text": " Develop a fashion app that segments and identifies clothing items in an uploaded image.\\n", "category": "generic"}
{"question_id": 850, "text": " Our team is working on a project to develop autonomous vehicles. We need a model to identify different segments of an image captured from the vehicle's camera.\\n", "category": "generic"}
{"question_id": 851, "text": " A real estate agency needs an application that can transform the floor plan images into simple straight line drawings, simplifying the visualization of the properties.\\n", "category": "generic"}
{"question_id": 852, "text": " I'm working on creating images of various scenes based on their textual descriptions. The models should also consider the actual positions and poses of the objects in the scenes.\\n", "category": "generic"}
{"question_id": 853, "text": " Develop a new product that uses machine learning to enhance the quality of low-resolution images by upscaling them to twice their size.\\n", "category": "generic"}
{"question_id": 854, "text": " An animal shelter needs an original cat image for a fundraising event poster. Generate the image.\\n", "category": "generic"}
{"question_id": 855, "text": " Develop a video content recommendation engine that can understand and generate multiple categories, such as sports, comedy, and news, based on the videos.\\n", "category": "generic"}
{"question_id": 856, "text": " Our customer is a fitness platform. We need to analyze workout videos for offering customized workout plans.\\n", "category": "generic"}
{"question_id": 857, "text": " We are a security company and we need a video classification model to analyze CCTV footage for suspicious activities.\\n", "category": "generic"}
{"question_id": 858, "text": " Design a model to classify the following image: a city park with a playground and a lake, surrounded by trees and skyscrapers.\\n", "category": "generic"}
{"question_id": 859, "text": " We are integrating a chatbot into our system. We want the chatbot to first detect the language of user input before providing a response.\\n", "category": "generic"}
{"question_id": 860, "text": " We have a dataset with customer reviews of our financial service app, and we'd like to analyze their sentiment.\\n", "category": "generic"}
{"question_id": 861, "text": " Our organization sells movies. We need to collect reviews from various platforms to understand the popularity of a movie.\\n", "category": "generic"}
{"question_id": 862, "text": " We want to enhance our search function by improving the ranking of search results.\\n", "category": "generic"}
{"question_id": 863, "text": " We are developing an AI chatbot to interact with users. We need the bot to recognize the user's emotions based on their text input.\\n", "category": "generic"}
{"question_id": 864, "text": " Please help me extract the names of people, organizations, and locations mentioned in the given text.\\n###Input: Hello, my name is John Doe, and I work at Microsoft. Tomorrow, I'll be going to a conference in San Francisco.\\n", "category": "generic"}
{"question_id": 865, "text": " The school wants a tool to teach foreign students Chinese grammar. They want you to develop a part-of-speech tagging system to detect the words' grammatical roles.\\n", "category": "generic"}
{"question_id": 866, "text": " Our company collects data on the salesperson performance in different regions for each month. We want to use the most suitable API from our subscribed_huggingface.co to analyze that in specific table format and then based on provided question, answer accordingly.\\n", "category": "generic"}
{"question_id": 867, "text": " Assist me in finding the accurate information in a table related to the Korean stock market.\\n", "category": "generic"}
{"question_id": 868, "text": " My company has a large data table of employees, containing their names, titles, departments, and hire dates. We need a tool that can find all employees with the title of \\\"Software Engineer\\\" hired in 2020.\\n", "category": "generic"}
{"question_id": 869, "text": " We received a business document in French. We need to extract some specific information from it. \\n", "category": "generic"}
{"question_id": 870, "text": " We have a coffee shop with different types of coffee on the menu. Determine the caffeine levels in each cup of coffee.\\n", "category": "generic"}
{"question_id": 871, "text": " You are the head of the QA department and you want to create an application that extracts answers from large product manuals. Explain how you can use this API for the project.\\n", "category": "generic"}
{"question_id": 872, "text": " I want to have a personal assistant app that can answer questions from a given text.\\n", "category": "generic"}
{"question_id": 873, "text": " I want to build a tool that helps me answer questions about specific information in a Korean newspaper article.\\n", "category": "generic"}
{"question_id": 874, "text": " Develop a tool to help our team members find answers to essential questions from a long document.\\n", "category": "generic"}
{"question_id": 875, "text": " There is a news article stating, \\\"Angela Merkel ist eine Politikerin in Deutschland und Vorsitzende der CDU.\\\" We need to determine which category this article should fall under.\\n###Input: Angela Merkel ist eine Politikerin in Deutschland und Vorsitzende der CDU.\\n", "category": "generic"}
{"question_id": 876, "text": " We have generated user reviews for movies. We need to check user opinions about the movie 'Inception' based on their reviews.\\n###Input: The movie 'Inception' is an exceptional piece of cinematic art. The storyline is thought-provoking and keeps the audience engaged till the end. The special effects are breathtaking and complement the plot perfectly.\\n", "category": "generic"}
{"question_id": 877, "text": " In our new app, we are building a feature that recommends books in different languages. To do this, first, we need to translate the book title and details from English to French. Help us to decide the best translation model to use here.\\n", "category": "generic"}
{"question_id": 878, "text": " Our multinational company is dealing with a French client. Please help us communicate in French by translating an English sentence into French.\\n###Input: \\\"Hello, how are you?\\\"\\n", "category": "generic"}
{"question_id": 879, "text": " We want to communicate product information to online customers. Translate the information from English to French.\\n###Input: \\\"Introducing the new eco-friendly water bottle made of high-quality stainless steel with double-wall insulation to keep your drinks cool for 24 hours or hot for 12 hours.\\\"\\n", "category": "generic"}
{"question_id": 880, "text": " Our Customer is a Swedish travel magazine with an English version. Translate this Swedish text to English for them: \\\"Stockholm \\u00e4r Sveriges huvudstad och st\\u00f6rsta stad. Den har en rik historia och erbjuder m\\u00e5nga kulturella och historiska sev\\u00e4rdheter.\\\"\\n###Input: \\\"Stockholm \\u00e4r Sveriges huvudstad och st\\u00f6rsta stad. Den har en rik historia och erbjuder m\\u00e5nga kulturella och historiska sev\\u00e4rdheter.\\\"\\n", "category": "generic"}
{"question_id": 881, "text": " I have written a small story in English about the adventures of a superhero who saves the day from evil villains. I would like to translate it into French so that my friends in France can read it too.\\n", "category": "generic"}
{"question_id": 882, "text": " Create an application that reads news articles and provides a brief summary of the article contents.\\n", "category": "generic"}
{"question_id": 883, "text": " We are building a platform for developers and want to provide automatic code documentation generation for Python functions.\\n", "category": "generic"}
{"question_id": 884, "text": " In order to engage our users and keep them interested in our platform, we require a conversational chatbot that discusses a wide range of topics.\\n", "category": "generic"}
{"question_id": 885, "text": " Use this API to get a suggestion on how to respond to a customer's complaint about the late delivery of their package.\\n###Input: {\\\"instruction\\\": \\\"How can I respond to a customer complaint about late delivery?\\\", \\\"knowledge\\\": \\\"The courier had external delays due to bad winter weather.\\\", \\\"dialog\\\": [\\\"Customer: My package is late. What's going on?\\\", \\\"Support: I apologize for the inconvenience. I'll check what's happening with the package and get back to you.\\\"]}\\n", "category": "generic"}
{"question_id": 886, "text": " A game studio is now creating a story for their new action game, they need a hint for creating the setting of the game.\\n", "category": "generic"}
{"question_id": 887, "text": " We are working on an AI chatbot for customer support and we need our chatbot to generate human-like responses to customers' questions.\\n", "category": "generic"}
{"question_id": 888, "text": " Translate the following English sentence to German: \\\"I have a doctor's appointment tomorrow morning.\\\"\\n###Input: \\\"I have a doctor's appointment tomorrow morning.\\\"\\n", "category": "generic"}
{"question_id": 889, "text": " I am struggling with grammar while writing. Thus, I want to create a grammar correction tool for myself.\\n", "category": "generic"}
{"question_id": 890, "text": " Our company is cooperating with a German partner. We have some materials in English, but need to translate them into German.\\n", "category": "generic"}
{"question_id": 891, "text": " The publisher has sent us a draft of an article, but some of the words have been masked. We need to identify the masked words.\\n###Input: \\\"<mask> are large, slow-moving reptiles native to the southeastern United States. They are well-adapted to life in <mask>, and they are a common sight in swamps, rivers, and lakes.\\\" \\n", "category": "generic"}
{"question_id": 892, "text": " Our company is exploring the Chinese market and we need to communicate effectively with our clients. Help me create a generic Chinese response.\\n", "category": "generic"}
{"question_id": 893, "text": " We are an educational platform improving student writing skills. We need a program that can fill in the blanks in sentences.\\n", "category": "generic"}
{"question_id": 894, "text": " I am building a plagiarism-detection tool. I need to evaluate the similarity between two sentences.\\n", "category": "generic"}
{"question_id": 895, "text": " We are working on creating an audiobook. Convert this text: \\\"The sun was shining brightly, and the birds were singing sweetly\\\" into speech.\\n###Input: The sun was shining brightly, and the birds were singing sweetly.\\n", "category": "generic"}
{"question_id": 896, "text": " A researcher needs information about how to use the Whisper ASR model to transcribe and analyze the sentiment of an audio file.\\n", "category": "generic"}
{"question_id": 897, "text": " I recently interviewed a person in Japanese. I need to transcribe the interview in order to find relevant quotes for my article.\\n", "category": "generic"}
{"question_id": 898, "text": " Our startup produces a podcast editing software. We need to convert speech to text automatically for transcription purposes.\\n", "category": "generic"}
{"question_id": 899, "text": " Lately, the quality of our audio has degraded due to background noise, and our robot needs to process speech to understand voice commands. Please help.\\n", "category": "generic"}
{"question_id": 900, "text": " Create a recommendation engine for a podcast platform that enhances the audio quality of low-quality recordings before recommending it to users seeking high-quality content.\\n", "category": "generic"}
{"question_id": 901, "text": " Create a system that translates and synthesizes speech from one language to another using the given model.\\n", "category": "generic"}
{"question_id": 902, "text": " I have a single channel audio recording containing the voices of two speakers very close, and I would like to separate the voices in this audio recording.\\n", "category": "generic"}
{"question_id": 903, "text": " We are building an app to improve people's public speaking skills. We want to analyze their emotional speech as feedback.\\n", "category": "generic"}
{"question_id": 904, "text": " A psychology company is building a revolutionary means to detect emotions of its clients. Help them create a system to understand emotions from spoken words.\\n", "category": "generic"}
{"question_id": 905, "text": " We are a company manufacturing AI-based toothbrushes for children. We want to analyze the emotion of children while they brush their teeth.\\n", "category": "generic"}
{"question_id": 906, "text": " I am a hearing impaired individual who relies on technology to lead a qualitative life. Develop an application that measures noise levels in the environment to help me decide if it's suitable for communication.\\n", "category": "generic"}
{"question_id": 907, "text": " Can you classify the audio clip to determine whether it is silent or contains speech?\\n", "category": "generic"}
{"question_id": 908, "text": " I need to predict digit categories based on some tabular data inputs. I need a pretrained model to use for classification.\\n", "category": "generic"}
{"question_id": 909, "text": " In surveillance operations, transcripts are used to turn the audio feed into chunks such that after processing transcripts there are no speakers talking over each other in the output.\\n", "category": "generic"}
{"question_id": 910, "text": " I work at GreenTech, a startup that provides eco-friendly solutions, and need to determine if a given set of input data will result in high carbon emissions or not.\\n", "category": "generic"}
{"question_id": 911, "text": " Can you figure a way to predict electricity consumption? We need to better allocate resources.\\n", "category": "generic"}
