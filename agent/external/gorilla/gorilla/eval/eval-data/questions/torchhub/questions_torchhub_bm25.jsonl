{"question_id": 1, "text": " What is an API that can be used to classify sports activities in videos?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 2, "text": " Identify an API capable of converting spoken language in a recording to text.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'RoBERTa\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/fairseq\\', model=\\'roberta.large\\', pretrained=True)\", \\'api_arguments\\': [\"\\'pytorch/fairseq\\'\", \"\\'roberta.large\\'\"], \\'python_environment_requirements\\': [\\'regex\\', \\'requests\\', \\'hydra-core\\', \\'omegaconf\\'], \\'example_code\\': [\\'import torch\\', \"roberta = torch.hub.load(\\'pytorch/fairseq\\', \\'roberta.large\\')\", \\'roberta.eval()\\', \"tokens = roberta.encode(\\'Hello world!\\')\", \\'last_layer_features = roberta.extract_features(tokens)\\', \\'all_layers = roberta.extract_features(tokens, return_all_hiddens=True)\\', \"roberta = torch.hub.load(\\'pytorch/fairseq\\', \\'roberta.large.mnli\\')\", \\'roberta.eval()\\', \"tokens = roberta.encode(\\'Roberta is a heavily optimized version of BERT.\\', \\'Roberta is not very optimized.\\')\", \"prediction = roberta.predict(\\'mnli\\', tokens).argmax().item()\", \"tokens = roberta.encode(\\'Roberta is a heavily optimized version of BERT.\\', \\'Roberta is based on BERT.\\')\", \"prediction = roberta.predict(\\'mnli\\', tokens).argmax().item()\", \"roberta.register_classification_head(\\'new_task\\', num_classes=3)\", \"logprobs = roberta.predict(\\'new_task\\', tokens)\"], \\'performance\\': {\\'dataset\\': \\'MNLI\\', \\'accuracy\\': \\'N/A\\'}, \\'description\\': \"RoBERTa is a robustly optimized version of BERT, a revolutionary self-supervised pretraining technique that learns to predict intentionally hidden (masked) sections of text. RoBERTa builds on BERT\\'s language masking strategy and modifies key hyperparameters, including removing BERT\\'s next-sentence pretraining objective, and training with much larger mini-batches and learning rates. RoBERTa was also trained on an order of magnitude more data than BERT, for a longer amount of time, allowing it to generalize even better to downstream tasks.\"}', metadata={})]", "category": "generic"}
{"question_id": 3, "text": " To analyze street photos, I need to segment different objects like pedestrians, vehicles, and buildings from a given image. Provide an API able to perform semantic segmentation on images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 4, "text": " To implement a lightweight object detection, I'm looking for a pre-trained model API that can detect and classify objects within an image in real-time.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 5, "text": " I need an image classification API that can handle millions of public images with thousands of hashtags. Please recommend one.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 6, "text": " Developers of a Virtual Reality event want to create a realistic digital crowd. Can you suggest a pretrained model to generate faces of celebrities?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Text-to-Speech\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Speech Synthesis\\', \\'api_name\\': \\'WaveGlow\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_waveglow\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_waveglow\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': {\\'load_waveglow_model\\': \"waveglow = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_waveglow\\', model_math=\\'fp32\\')\", \\'prepare_waveglow_model\\': [\\'waveglow = waveglow.remove_weightnorm(waveglow)\\', \"waveglow = waveglow.to(\\'cuda\\')\", \\'waveglow.eval()\\'], \\'load_tacotron2_model\\': \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp32\\')\", \\'prepare_tacotron2_model\\': [\"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\'], \\'synthesize_speech\\': [\\'text = \"hello world, I missed you so much\"\\', \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'save_audio\\': \\'write(\"audio.wav\", rate, audio_numpy)\\', \\'play_audio\\': \\'Audio(audio_numpy, rate=rate)\\'}, \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': None}, \\'description\\': \\'The Tacotron 2 and WaveGlow model form a text-to-speech system that enables users to synthesize natural-sounding speech from raw transcripts without any additional prosody information. The Tacotron 2 model produces mel spectrograms from input text using encoder-decoder architecture. WaveGlow is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 7, "text": " I need an API to classify images from a dataset with a high accuracy rate. Provide an appropriate API and the performance on the ImageNet dataset.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 8, "text": " A tourism website is building a feature to categorize photos into classes of landmarks. Recommend a machine learning API that will take an image and output which class the image falls into.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 9, "text": " A photographer at National Geographic is finding photos for the monthly magazine cover. They need a model to classify a picture of a cheetah running in the wild from other images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'HarDNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'PingoLH/Pytorch-HarDNet\\', model=\\'hardnet68\\', pretrained=True)\", \\'api_arguments\\': [{\\'name\\': \\'hardnet68\\', \\'type\\': \\'str\\', \\'description\\': \\'HarDNet-68 model\\'}], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'PingoLH/Pytorch-HarDNet\\', \\'hardnet68\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'hardnet68\\': {\\'Top-1 error\\': 23.52, \\'Top-5 error\\': 6.99}}}, \\'description\\': \\'Harmonic DenseNet (HarDNet) is a low memory trafficCNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet).\\'}', metadata={})]", "category": "generic"}
{"question_id": 10, "text": " DXmart needs to build a product image classification system for their e-commerce site. Provide an API that can classify product images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 11, "text": " Identify an API to perform efficient animal classification from user provided images without sacrificing model accuracy for a biodiversity conservation project.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SqueezeNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'squeezenet1_1\\', pretrained=True)\", \\'api_arguments\\': {\\'version\\': \\'v0.10.0\\', \\'model\\': [\\'squeezenet1_1\\'], \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'squeezenet1_1\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'squeezenet1_1\\': {\\'Top-1 error\\': 41.81, \\'Top-5 error\\': 19.38}}}, \\'description\\': \\'SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy.\\'}', metadata={})]", "category": "generic"}
{"question_id": 12, "text": " Recommend an API to build an Image Classifier that would better classify images with minimal computational resources.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'RoBERTa\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/fairseq\\', model=\\'roberta.large\\', pretrained=True)\", \\'api_arguments\\': [\"\\'pytorch/fairseq\\'\", \"\\'roberta.large\\'\"], \\'python_environment_requirements\\': [\\'regex\\', \\'requests\\', \\'hydra-core\\', \\'omegaconf\\'], \\'example_code\\': [\\'import torch\\', \"roberta = torch.hub.load(\\'pytorch/fairseq\\', \\'roberta.large\\')\", \\'roberta.eval()\\', \"tokens = roberta.encode(\\'Hello world!\\')\", \\'last_layer_features = roberta.extract_features(tokens)\\', \\'all_layers = roberta.extract_features(tokens, return_all_hiddens=True)\\', \"roberta = torch.hub.load(\\'pytorch/fairseq\\', \\'roberta.large.mnli\\')\", \\'roberta.eval()\\', \"tokens = roberta.encode(\\'Roberta is a heavily optimized version of BERT.\\', \\'Roberta is not very optimized.\\')\", \"prediction = roberta.predict(\\'mnli\\', tokens).argmax().item()\", \"tokens = roberta.encode(\\'Roberta is a heavily optimized version of BERT.\\', \\'Roberta is based on BERT.\\')\", \"prediction = roberta.predict(\\'mnli\\', tokens).argmax().item()\", \"roberta.register_classification_head(\\'new_task\\', num_classes=3)\", \"logprobs = roberta.predict(\\'new_task\\', tokens)\"], \\'performance\\': {\\'dataset\\': \\'MNLI\\', \\'accuracy\\': \\'N/A\\'}, \\'description\\': \"RoBERTa is a robustly optimized version of BERT, a revolutionary self-supervised pretraining technique that learns to predict intentionally hidden (masked) sections of text. RoBERTa builds on BERT\\'s language masking strategy and modifies key hyperparameters, including removing BERT\\'s next-sentence pretraining objective, and training with much larger mini-batches and learning rates. RoBERTa was also trained on an order of magnitude more data than BERT, for a longer amount of time, allowing it to generalize even better to downstream tasks.\"}', metadata={})]", "category": "generic"}
{"question_id": 13, "text": " I need to recognize dogs and cats from images. What API should I use to perform this task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 14, "text": " I need a suitable PyTorch API that can classify a wide range of images. Please provide me with instructions on how to load the pretrained model.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 15, "text": " I need to build an image classifier to identify objects in a photo. Suggest a suitable model that I can use for this purpose.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 16, "text": " A developer is building a mobile app to identify objects using the mobile camera. Suggest an API to classify object types given an image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 17, "text": " A wildlife organization is looking to classify photos taken on their CCTV cameras into 100 different animal species. Suggest an API to achieve this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SNNMLP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/Efficient-AI-Backbones\\', model=\\'snnmlp_b\\', pretrained=True)\", \\'api_arguments\\': [{\\'name\\': \\'snnmlp_b\\', \\'type\\': \\'str\\', \\'description\\': \\'SNNMLP Base model\\'}], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\', \\'urllib\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/Efficient-AI-Backbones\\', \\'snnmlp_b\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'print(torch.nn.functional.softmax(output[0], dim=0))\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'model\\': \\'SNNMLP Base\\', \\'top-1\\': 85.59}}, \\'description\\': \\'SNNMLP incorporates the mechanism of LIF neurons into the MLP models, to achieve better accuracy without extra FLOPs. We propose a full-precision LIF operation to communicate between patches, including horizontal LIF and vertical LIF in different directions. We also propose to use group LIF to extract better local features. With LIF modules, our SNNMLP model achieves 81.9%, 83.3% and 83.6% top-1 accuracy on ImageNet dataset with only 4.4G, 8.5G and 15.2G FLOPs, respectively.\\'}', metadata={})]", "category": "generic"}
{"question_id": 18, "text": " A self-driving car company is developing an autonomous vehicle that requires detecting objects, drivable area segmentation, and lane detection in real-time. Suggest an appropriate API for this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 19, "text": " I want an ML library that can determine the object distances in a photo without inputting more than one photo.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 20, "text": " I would like a simple method to turn spoken user commands into text, which AI API would you recommend?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 21, "text": " Write me an API to use as a pretrained model for classifying images into categories.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Semi-supervised and semi-weakly supervised ImageNet Models\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/semi-supervised-ImageNet1K-models\\', model=\\'resnet18_swsl\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'model\\': \\'resnet18_swsl\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'resnet18_swsl\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'description\\': \\'Semi-supervised and semi-weakly supervised ImageNet models achieve state-of-the-art accuracy of 81.2% on ImageNet for the widely used/adopted ResNet-50 model architecture.\\'}, \\'description\\': \"Semi-supervised and semi-weakly supervised ImageNet Models are introduced in the \\'Billion scale semi-supervised learning for image classification\\' paper. These models are pretrained on a subset of unlabeled YFCC100M public image dataset and fine-tuned with the ImageNet1K training dataset. They are capable of classifying images into different categories and are provided by the Facebook Research library.\"}', metadata={})]", "category": "generic"}
{"question_id": 22, "text": " A company wants to segment objects in the images for its e-commerce website. Give an API that can segment objects in images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 23, "text": " I'm working on a medical app and I want to classify images of skin lesions. Show me an API that can classify images with high efficiency and accuracy.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 24, "text": " What is an API that can classify an image of a dog into its specific breed from a list of 120 unique breeds?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 25, "text": " Can you give me an API that can classify food dishes in restaurant menus using image classification?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 26, "text": " For my mobile app, I need an efficient and light-weight model that can classify animals, plants, landmarks, etc. in an image fed via the device's camera. Suggest an API.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 27, "text": " For a wildlife photography website, suggest an API that can classify the animal species in a given photo.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 28, "text": " Please suggest an API that can detect and count the number of birds in an image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 29, "text": " Identify an API that can classify images and works with spiking neural networks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 30, "text": " What is an efficient API that can be used to categorize images and has a much lighter model with fewer parameters than AlexNet?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SqueezeNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'squeezenet1_0\\', pretrained=True)\", \\'api_arguments\\': {\\'version\\': \\'v0.10.0\\', \\'model\\': [\\'squeezenet1_0\\'], \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'squeezenet1_0\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'squeezenet1_0\\': {\\'Top-1 error\\': 41.9, \\'Top-5 error\\': 19.58}}}, \\'description\\': \\'SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy.\\'}', metadata={})]", "category": "generic"}
{"question_id": 31, "text": " Find me an API which will help identifying animals in a given image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Fine-grained image classifier\\', \\'api_name\\': \\'ntsnet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'nicolalandro/ntsnet-cub200\\', model=\\'ntsnet\\', pretrained=True, **{\\'topN\\': 6, \\'device\\':\\'cpu\\', \\'num_classes\\': 200})\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\', \\'topN\\': \\'6\\', \\'device\\': \\'cpu\\', \\'num_classes\\': \\'200\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': \"from torchvision import transforms\\\\nimport torch\\\\nimport urllib\\\\nfrom PIL import Image\\\\n\\\\ntransform_test = transforms.Compose([\\\\n transforms.Resize((600, 600), Image.BILINEAR),\\\\n transforms.CenterCrop((448, 448)),\\\\n transforms.ToTensor(),\\\\n transforms.Normalize((0.485, 0.456, 0.406), (0.229, 0.224, 0.225)),\\\\n])\\\\n\\\\nmodel = torch.hub.load(\\'nicolalandro/ntsnet-cub200\\', \\'ntsnet\\', pretrained=True, **{\\'topN\\': 6, \\'device\\':\\'cpu\\', \\'num_classes\\': 200})\\\\nmodel.eval()\\\\n\\\\nurl = \\'https://raw.githubusercontent.com/nicolalandro/ntsnet-cub200/master/images/nts-net.png\\'\\\\nimg = Image.open(urllib.request.urlopen(url))\\\\nscaled_img = transform_test(img)\\\\ntorch_images = scaled_img.unsqueeze(0)\\\\n\\\\nwith torch.no_grad():\\\\n top_n_coordinates, concat_out, raw_logits, concat_logits, part_logits, top_n_index, top_n_prob = model(torch_images)\\\\n\\\\n_, predict = torch.max(concat_logits, 1)\\\\npred_id = predict.item()\\\\nprint(\\'bird class:\\', model.bird_classes[pred_id])\", \\'performance\\': {\\'dataset\\': \\'CUB200 2011\\', \\'accuracy\\': \\'Not provided\\'}, \\'description\\': \\'This is an nts-net pretrained with CUB200 2011 dataset, which is a fine-grained dataset of birds species.\\'}', metadata={})]", "category": "generic"}
{"question_id": 32, "text": " My company is building a chatbot for a car dealership and we need a machine learning model that can classify cars from images. Can you suggest one?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 33, "text": " A wildlife conservationist wants to classify animals in their natural habitat with a high accuracy. Recommend an API that can assist in this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 34, "text": " A software engineer working at a computer vision company is looking for a model that can classify images efficiently on NVIDIA GPUs. Provide an API recommendation.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 35, "text": " Recommend an API to translate an English ebook to French.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'MobileNet v2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'mobilenet_v2\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\', \\'urllib\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision\\', \\'mobilenet_v2\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 28.12, \\'top-5_error\\': 9.71}}, \\'description\\': \\'The MobileNet v2 architecture is based on an inverted residual structure where the input and output of the residual block are thin bottleneck layers opposite to traditional residual models which use expanded representations in the input. MobileNet v2 uses lightweight depthwise convolutions to filter features in the intermediate expansion layer. Additionally, non-linearities in the narrow layers were removed in order to maintain representational power.\\'}', metadata={})]", "category": "generic"}
{"question_id": 36, "text": " In an attempt to streamline content moderation, Facebook is implementing an AI-enabled tool to identify potentially inappropriate images. Suggest an API that can recognize objects within an image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 37, "text": " The weatherman needs an AI which could read out the daily weather information. Tell me an API that generates spoken weather information from a written weather forecast.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 38, "text": " A developer needs to classify images using a model that does not require additional tricks for high accuracy. Recommend an API with a high top-1 accuracy without using any tricks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 39, "text": " I need an API that can help me identify the type of a cucumber. It should be able to tell me whether it's pickling, slicing, or burpless cucumber.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 40, "text": " I need to develop a self-driving car which can simultaneously recognize objects, drivable areas, and lanes. Recommend me an API to handle these tasks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 41, "text": " I'd like to detect voice activity in an audio file. What API can help me perform this task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 42, "text": " We wish to create an app to make coloring books from images. Recommend an API to extract the regions that should be colored.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 43, "text": " Imagine you were given a set of images and you need to tell what objects are on the pictures. Indicate an API that can classify the objects in the images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 44, "text": " My friend recommended the Densenet-201 model to classify images. Find an API that I can use for this model.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 45, "text": " Provide me with an API that can segment objects within an image into separate categories.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 46, "text": " Looking for a fast and efficient image classification API to suit my low-end device. What would you recommend?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Generative Adversarial Networks\\', \\'api_name\\': \\'DCGAN\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/pytorch_GAN_zoo:hub\\', model=\\'DCGAN\\', pretrained=True, useGPU=use_gpu)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\', \\'useGPU\\': \\'use_gpu\\'}, \\'python_environment_requirements\\': \\'Python 3\\', \\'example_code\\': {\\'import\\': [\\'import torch\\', \\'import matplotlib.pyplot as plt\\', \\'import torchvision\\'], \\'use_gpu\\': \\'use_gpu = True if torch.cuda.is_available() else False\\', \\'load_model\\': \"model = torch.hub.load(\\'facebookresearch/pytorch_GAN_zoo:hub\\', \\'DCGAN\\', pretrained=True, useGPU=use_gpu)\", \\'build_noise_data\\': \\'noise, _ = model.buildNoiseData(num_images)\\', \\'generate_images\\': \\'with torch.no_grad(): generated_images = model.test(noise)\\', \\'plot_images\\': [\\'plt.imshow(torchvision.utils.make_grid(generated_images).permute(1, 2, 0).cpu().numpy())\\', \\'plt.show()\\']}, \\'performance\\': {\\'dataset\\': \\'FashionGen\\', \\'accuracy\\': \\'N/A\\'}, \\'description\\': \\'DCGAN is a model designed in 2015 by Radford et. al. in the paper Unsupervised Representation Learning with Deep Convolutional Generative Adversarial Networks. It is a GAN architecture both very simple and efficient for low resolution image generation (up to 64x64).\\'}', metadata={})]", "category": "generic"}
{"question_id": 47, "text": " I need a model that can help identify which domain an image belongs to, such as artistic style or natural scenery. Recommend me an API that can do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 48, "text": " I want to know which dog breed a given image belongs to. Tell me an API that is capable of identifying dog breeds.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 49, "text": " I need to classify images into various categories based on their content. Can you suggest an API that can do this?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 50, "text": " Recommend an API to automatically fine-tune a neural network's architecture for optimal performance on a specific graphics processing unit (GPU) platform.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 51, "text": " A software engineer is trying to determine if an image contains a dog, cat or a horse. Identify an API that could be fine-tuned to achieve the objective.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 52, "text": " Can you suggest me an AI model that can classify images with 50x fewer parameters than AlexNet and better performance on a robotics project I'm working on?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SqueezeNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'squeezenet1_0\\', pretrained=True)\", \\'api_arguments\\': {\\'version\\': \\'v0.10.0\\', \\'model\\': [\\'squeezenet1_0\\'], \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'squeezenet1_0\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'squeezenet1_0\\': {\\'Top-1 error\\': 41.9, \\'Top-5 error\\': 19.58}}}, \\'description\\': \\'SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy.\\'}', metadata={})]", "category": "generic"}
{"question_id": 53, "text": " Recommend a way to recognize decorative and architectural elements in architectural design images using a pre-trained network.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'IBN-Net\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'XingangPan/IBN-Net\\', model=\\'se_resnet101_ibn_a\\', pretrained=True)\", \\'api_arguments\\': [{\\'name\\': \\'se_resnet101_ibn_a\\', \\'type\\': \\'str\\', \\'description\\': \\'SE-ResNet-101-IBN-a model\\'}], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\', \\'urllib\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'XingangPan/IBN-Net\\', \\'se_resnet101_ibn_a\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'se_resnet101_ibn_a\\': {\\'Top-1 acc\\': 78.75, \\'Top-5 acc\\': 94.49}}}, \\'description\\': \\'IBN-Net is a CNN model with domain/appearance invariance. Motivated by style transfer works, IBN-Net carefully unifies instance normalization and batch normalization in a single deep network. It provides a simple way to increase both modeling and generalization capacities without adding model complexity. IBN-Net is especially suitable for cross domain or person/vehicle re-identification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 54, "text": " Can you suggest an API that can automatically classify images for me?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 55, "text": " Suggest an API for classifying dog breeds given an image of a dog.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Semi-supervised and semi-weakly supervised ImageNet Models\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/semi-supervised-ImageNet1K-models\\', model=\\'resnet18_swsl\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'model\\': \\'resnet18_swsl\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'resnet18_swsl\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'description\\': \\'Semi-supervised and semi-weakly supervised ImageNet models achieve state-of-the-art accuracy of 81.2% on ImageNet for the widely used/adopted ResNet-50 model architecture.\\'}, \\'description\\': \"Semi-supervised and semi-weakly supervised ImageNet Models are introduced in the \\'Billion scale semi-supervised learning for image classification\\' paper. These models are pretrained on a subset of unlabeled YFCC100M public image dataset and fine-tuned with the ImageNet1K training dataset. They are capable of classifying images into different categories and are provided by the Facebook Research library.\"}', metadata={})]", "category": "generic"}
{"question_id": 56, "text": " Suggest an API designed for NVIDIA GPU and TensorRT performance optimization to classify images into different categories.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'GPUNet Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \\'api_arguments\\': {\\'repository\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_gpunet\\', \\'pretrained\\': \\'True\\', \\'model_type\\': \\'GPUNet-0\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'validators\\', \\'matplotlib\\', \\'timm==0.5.4\\'], \\'example_code\\': [\\'import torch\\', \"model_type = \\'GPUNet-0\\'\", \"precision = \\'fp32\\'\", \"gpunet = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'gpunet.to(device)\\', \\'gpunet.eval()\\'], \\'performance\\': {\\'dataset\\': \\'IMAGENET\\', \\'description\\': \\'GPUNet demonstrates state-of-the-art inference performance up to 2x faster than EfficientNet-X and FBNet-V3.\\'}, \\'description\\': \\'GPUNet is a family of Convolutional Neural Networks designed by NVIDIA using novel Neural Architecture Search (NAS) methods. They are optimized for NVIDIA GPU and TensorRT performance. GPUNet models are pretrained on the IMAGENET dataset and are capable of classifying images into different categories. The models are provided by the NVIDIA Deep Learning Examples library.\\'}', metadata={})]", "category": "generic"}
{"question_id": 57, "text": " Translate the given English text to French using machine learning API.\\n###Input: {\\\"text\\\": \\\"I like playing basketball.\\\"}\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 58, "text": " Recommend an API to identify the breed of a dog from a picture input.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'Inception_v3\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'inception_v3\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'1.9.0\\', \\'torchvision\\': \\'0.10.0\\'}, \\'example_code\\': {\\'import_libraries\\': \\'import torch\\', \\'load_model\\': \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'inception_v3\\', pretrained=True)\", \\'model_evaluation\\': \\'model.eval()\\'}, \\'performance\\': {\\'dataset\\': \\'imagenet\\', \\'accuracy\\': {\\'top-1_error\\': 22.55, \\'top-5_error\\': 6.44}}, \\'description\\': \\'Inception v3, also called GoogleNetv3, is a famous Convolutional Neural Network trained on the ImageNet dataset from 2015. It is based on the exploration of ways to scale up networks to utilize the added computation as efficiently as possible by using suitably factorized convolutions and aggressive regularization. The model achieves a top-1 error of 22.55% and a top-5 error of 6.44% on the ImageNet dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 59, "text": " I want to build an image classifier to boost the accuracy of the Vanilla Resnet-50 model on ImageNet data without using any data augmentation tricks. What API should I use?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 60, "text": " Create a 3D reconstruction of a scene from only one image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Video Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'3D ResNet\\', \\'api_name\\': \\'slow_r50\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/pytorchvideo\\', model=\\'slow_r50\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'json\\', \\'urllib\\', \\'pytorchvideo\\', \\'torchvision\\', \\'torchaudio\\', \\'torchtext\\', \\'torcharrow\\', \\'TorchData\\', \\'TorchRec\\', \\'TorchServe\\', \\'PyTorch on XLA Devices\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/pytorchvideo\\', \\'slow_r50\\', pretrained=True)\", \"device = \\'cpu\\'\", \\'model = model.eval()\\', \\'model = model.to(device)\\'], \\'performance\\': {\\'dataset\\': \\'Kinetics 400\\', \\'accuracy\\': {\\'top_1\\': 74.58, \\'top_5\\': 91.63}, \\'Flops (G)\\': 54.52, \\'Params (M)\\': 32.45}, \\'description\\': \"The 3D ResNet model is a Resnet-style video classification network pretrained on the Kinetics 400 dataset. It is based on the architecture from the paper \\'SlowFast Networks for Video Recognition\\' by Christoph Feichtenhofer et al.\"}', metadata={})]", "category": "generic"}
{"question_id": 61, "text": " A video editor is developing a software that will allow users to mute specific instruments in a song. Provide an API that can separate audio into multiple tracks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Audio Separation\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Music Source Separation\\', \\'api_name\\': \\'Open-Unmix\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'sigsep/open-unmix-pytorch\\', model=\\'umxhq\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'PyTorch >=1.6.0\\', \\'torchaudio\\'], \\'example_code\\': [\\'import torch\\', \"separator = torch.hub.load(\\'sigsep/open-unmix-pytorch\\', \\'umxhq\\')\", \\'audio = torch.rand((1, 2, 100000))\\', \\'original_sample_rate = separator.sample_rate\\', \\'estimates = separator(audio)\\'], \\'performance\\': {\\'dataset\\': \\'MUSDB18\\', \\'accuracy\\': \\'N/A\\'}, \\'description\\': \\'Open-Unmix provides ready-to-use models that allow users to separate pop music into four stems: vocals, drums, bass and the remaining other instruments. The models were pre-trained on the freely available MUSDB18 dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 62, "text": " I am working on a project where I need to convert a text document into an audio file. Can you suggest an API for text-to-speech conversion?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 63, "text": " Suggest an API for identifying objects in a picture taken at a city park.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 64, "text": " I have an image and I need to detect the different objects in that image. Give me an API that can do this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 65, "text": " I want to create a new collection of clothing designs. Recommend an API that can generate unique images of clothing items.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 66, "text": " I'm working on an image classification project where I need to identify the contents of an image. Can you suggest an API for that?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 67, "text": " List an API that will allow me to input text that will be transformed into an audio file.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 68, "text": " Find a model that is optimal for the task of person re-identification from a set of images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 69, "text": " Query an API that carries out vehicle or person re-identification tasks accurately.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 70, "text": " I need an image classification model that can classify objects in images with high accuracy. Suggest me an API.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 71, "text": " Help me find a way to classify different species of birds given an image from the Internet.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Fine-grained image classifier\\', \\'api_name\\': \\'ntsnet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'nicolalandro/ntsnet-cub200\\', model=\\'ntsnet\\', pretrained=True, **{\\'topN\\': 6, \\'device\\':\\'cpu\\', \\'num_classes\\': 200})\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\', \\'topN\\': \\'6\\', \\'device\\': \\'cpu\\', \\'num_classes\\': \\'200\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': \"from torchvision import transforms\\\\nimport torch\\\\nimport urllib\\\\nfrom PIL import Image\\\\n\\\\ntransform_test = transforms.Compose([\\\\n transforms.Resize((600, 600), Image.BILINEAR),\\\\n transforms.CenterCrop((448, 448)),\\\\n transforms.ToTensor(),\\\\n transforms.Normalize((0.485, 0.456, 0.406), (0.229, 0.224, 0.225)),\\\\n])\\\\n\\\\nmodel = torch.hub.load(\\'nicolalandro/ntsnet-cub200\\', \\'ntsnet\\', pretrained=True, **{\\'topN\\': 6, \\'device\\':\\'cpu\\', \\'num_classes\\': 200})\\\\nmodel.eval()\\\\n\\\\nurl = \\'https://raw.githubusercontent.com/nicolalandro/ntsnet-cub200/master/images/nts-net.png\\'\\\\nimg = Image.open(urllib.request.urlopen(url))\\\\nscaled_img = transform_test(img)\\\\ntorch_images = scaled_img.unsqueeze(0)\\\\n\\\\nwith torch.no_grad():\\\\n top_n_coordinates, concat_out, raw_logits, concat_logits, part_logits, top_n_index, top_n_prob = model(torch_images)\\\\n\\\\n_, predict = torch.max(concat_logits, 1)\\\\npred_id = predict.item()\\\\nprint(\\'bird class:\\', model.bird_classes[pred_id])\", \\'performance\\': {\\'dataset\\': \\'CUB200 2011\\', \\'accuracy\\': \\'Not provided\\'}, \\'description\\': \\'This is an nts-net pretrained with CUB200 2011 dataset, which is a fine-grained dataset of birds species.\\'}', metadata={})]", "category": "generic"}
{"question_id": 72, "text": " Your pet store is building a new image classifier for the different types of pets. Tell me which API can identify the breeds given pet images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 73, "text": " I want to recognize objects in an image. Can you find me an API that can do this?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 74, "text": " I'm a photographer and I need to classify images according to their category. Write the code to use a machine learning API to achieve that.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SNNMLP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/Efficient-AI-Backbones\\', model=\\'snnmlp_s\\', pretrained=True)\", \\'api_arguments\\': [{\\'name\\': \\'snnmlp_s\\', \\'type\\': \\'str\\', \\'description\\': \\'SNNMLP Small model\\'}], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\', \\'urllib\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/Efficient-AI-Backbones\\', \\'snnmlp_s\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'print(torch.nn.functional.softmax(output[0], dim=0))\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'model\\': \\'SNNMLP Small\\', \\'top-1\\': 83.3}}, \\'description\\': \\'SNNMLP incorporates the mechanism of LIF neurons into the MLP models, to achieve better accuracy without extra FLOPs. We propose a full-precision LIF operation to communicate between patches, including horizontal LIF and vertical LIF in different directions. We also propose to use group LIF to extract better local features. With LIF modules, our SNNMLP model achieves 81.9%, 83.3% and 83.6% top-1 accuracy on ImageNet dataset with only 4.4G, 8.5G and 15.2G FLOPs, respectively.\\'}', metadata={})]", "category": "generic"}
{"question_id": 75, "text": " I want to classify images accurately without latency. Help me find an API to do that.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 76, "text": " Imagine I am an app developer and need to build Instagram like app that can classify user's images for easy searching lateron. Please suggest a pre-trained AI API that can help me in my endeavors.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 77, "text": " A retailer would like to better categorize images of products on their website. Provide a model API that can perform image classification.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 78, "text": " Tesla wants to improve the back camera of their cars, and they are seeking an API for jointly handling object detection, drivable area segmentation, and lane detection. Provide a suitable API for this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 79, "text": " I need a Python library for calculating relative depth from a single image. What do you suggest?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Computing relative depth from a single image\\', \\'api_name\\': \\'MiDaS\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'intel-isl/MiDaS\\', model=\\'DPT_Large\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'intel-isl/MiDaS\\', \\'model\\': \\'model_type\\'}, \\'python_environment_requirements\\': \\'pip install timm\\', \\'example_code\\': [\\'import cv2\\', \\'import torch\\', \\'import urllib.request\\', \\'import matplotlib.pyplot as plt\\', \"url, filename = (\\'https://github.com/pytorch/hub/raw/master/images/dog.jpg\\', \\'dog.jpg\\')\", \\'urllib.request.urlretrieve(url, filename)\\', \"model_type = \\'DPT_Large\\'\", \"midas = torch.hub.load(\\'intel-isl/MiDaS\\', \\'DPT_Large\\')\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'midas.to(device)\\', \\'midas.eval()\\', \"midas_transforms = torch.hub.load(\\'intel-isl/MiDaS\\', \\'transforms\\')\", \"if model_type == \\'DPT_Large\\' or model_type == \\'DPT_Hybrid\\':\", \\' transform = midas_transforms.dpt_transform\\', \\'else:\\', \\' transform = midas_transforms.small_transform\\', \\'img = cv2.imread(filename)\\', \\'img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\\', \\'input_batch = transform(img).to(device)\\', \\'with torch.no_grad():\\', \\' prediction = midas(input_batch)\\', \\'prediction = torch.nn.functional.interpolate(\\', \\' prediction.unsqueeze(1),\\', \\' size=img.shape[:2],\\', \" mode=\\'bicubic\\',\", \\' align_corners=False,\\', \\').squeeze()\\', \\'output = prediction.cpu().numpy()\\', \\'plt.imshow(output)\\', \\'plt.show()\\'], \\'performance\\': {\\'dataset\\': \\'10 distinct datasets\\', \\'accuracy\\': \\'Multi-objective optimization\\'}, \\'description\\': \\'MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs.\\'}', metadata={})]", "category": "generic"}
{"question_id": 80, "text": " Tell me an API that I can use to classify images into different categories using a pre-trained ResNet50 model.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Semi-supervised and semi-weakly supervised ImageNet Models\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/semi-supervised-ImageNet1K-models\\', model=\\'resnet18_swsl\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'model\\': \\'resnet18_swsl\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'resnet18_swsl\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'description\\': \\'Semi-supervised and semi-weakly supervised ImageNet models achieve state-of-the-art accuracy of 81.2% on ImageNet for the widely used/adopted ResNet-50 model architecture.\\'}, \\'description\\': \"Semi-supervised and semi-weakly supervised ImageNet Models are introduced in the \\'Billion scale semi-supervised learning for image classification\\' paper. These models are pretrained on a subset of unlabeled YFCC100M public image dataset and fine-tuned with the ImageNet1K training dataset. They are capable of classifying images into different categories and are provided by the Facebook Research library.\"}', metadata={})]", "category": "generic"}
{"question_id": 81, "text": " I am developing an app for bird species classification. Suggest me an API that can identify bird species in images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 82, "text": " I need to analyze aerial images of agricultural fields to identify specific crop types. Can you suggest an API for classifying the crops in the images?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 83, "text": " Identify an API that can help me classify various objects in a given image efficiently and quickly.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 84, "text": " Find an API that allows me to classify pictures of animals with high accuracy.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 85, "text": " An AI engineer is searching for an API capable of image classification. Please provide an SDK that uses a pre-trained model for image recognition tasks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 86, "text": " Tell me an API that can predict the breed of a dog through its image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 87, "text": " A wildlife researcher wants to identify different bird species from a picture. Suggest a deep learning model that can help them achieve this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 88, "text": " What type of model is best for recognizing multiple objects in images? \\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 89, "text": " Find the species of an animal in a given photo using an API.\\n###Input: \\\"zebra.jpg\\\"\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 90, "text": " I need to classify images on different edge devices with various resource constraints. Suggest an API suitable for this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Once-for-all (OFA) Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'mit-han-lab/once-for-all\\', model=\\'ofa_supernet_mbv3_w10\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'mit-han-lab/once-for-all\\', \\'model\\': \\'ofa_supernet_mbv3_w10\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"super_net_name = \\'ofa_supernet_mbv3_w10\\'\", \"super_net = torch.hub.load(\\'mit-han-lab/once-for-all\\', super_net_name, pretrained=True).eval()\"], \\'performance\\': {\\'description\\': \\'OFA networks outperform state-of-the-art NAS methods (up to 4.0% ImageNet top1 accuracy improvement over MobileNetV3, or same accuracy but 1.5x faster than MobileNetV3, 2.6x faster than EfficientNet w.r.t measured latency) while reducing many orders of magnitude GPU hours and CO2 emission.\\'}, \\'description\\': \\'Once-for-all (OFA) networks are a family of neural networks designed by MIT Han Lab. They decouple training and search, achieving efficient inference across various edge devices and resource constraints. OFA networks are pretrained on the IMAGENET dataset and are capable of classifying images into different categories.\\'}', metadata={})]", "category": "generic"}
{"question_id": 91, "text": " Provide an API for converting text to speech, since the marketing team needs to generate realistic voices for a series of advertisements.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 92, "text": " I need an API that helps classify images with the highest accuracy. Tell me an API that can achieve this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 93, "text": " Pinterest wants to build a system that can categorize images uploaded by users. What API should they use for this task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 94, "text": " Recommend me an API that can compute a depth map from a single input image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Computing relative depth from a single image\\', \\'api_name\\': \\'MiDaS\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'intel-isl/MiDaS\\', model=\\'DPT_Hybrid\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'intel-isl/MiDaS\\', \\'model\\': \\'model_type\\'}, \\'python_environment_requirements\\': \\'pip install timm\\', \\'example_code\\': [\\'import cv2\\', \\'import torch\\', \\'import urllib.request\\', \\'import matplotlib.pyplot as plt\\', \"url, filename = (\\'https://github.com/pytorch/hub/raw/master/images/dog.jpg\\', \\'dog.jpg\\')\", \\'urllib.request.urlretrieve(url, filename)\\', \"model_type = \\'DPT_Large\\'\", \"midas = torch.hub.load(\\'intel-isl/MiDaS\\', \\'DPT_Hybrid\\')\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'midas.to(device)\\', \\'midas.eval()\\', \"midas_transforms = torch.hub.load(\\'intel-isl/MiDaS\\', \\'transforms\\')\", \"if model_type == \\'DPT_Large\\' or model_type == \\'DPT_Hybrid\\':\", \\' transform = midas_transforms.dpt_transform\\', \\'else:\\', \\' transform = midas_transforms.small_transform\\', \\'img = cv2.imread(filename)\\', \\'img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\\', \\'input_batch = transform(img).to(device)\\', \\'with torch.no_grad():\\', \\' prediction = midas(input_batch)\\', \\'prediction = torch.nn.functional.interpolate(\\', \\' prediction.unsqueeze(1),\\', \\' size=img.shape[:2],\\', \" mode=\\'bicubic\\',\", \\' align_corners=False,\\', \\').squeeze()\\', \\'output = prediction.cpu().numpy()\\', \\'plt.imshow(output)\\', \\'plt.show()\\'], \\'performance\\': {\\'dataset\\': \\'10 distinct datasets\\', \\'accuracy\\': \\'Multi-objective optimization\\'}, \\'description\\': \\'MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs.\\'}', metadata={})]", "category": "generic"}
{"question_id": 95, "text": " I am working on a project that involves bird image identification. Can you recommend an API that can classify bird species from images?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 96, "text": " Suggest an object detection API that is suitable for implementing real-time applications like a security camera.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 97, "text": " A mobile application needs a machine learning model for object classification from various user images. Suggest an appropriate API for this task. \\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 98, "text": " I have a dataset with labeled images of clothing items from several fashion brands, and I want to classify them by brand. Which API can help me perform a classification task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Text-to-Speech\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Speech Synthesis\\', \\'api_name\\': \\'WaveGlow\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_waveglow\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_waveglow\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': {\\'load_waveglow_model\\': \"waveglow = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_waveglow\\', model_math=\\'fp32\\')\", \\'prepare_waveglow_model\\': [\\'waveglow = waveglow.remove_weightnorm(waveglow)\\', \"waveglow = waveglow.to(\\'cuda\\')\", \\'waveglow.eval()\\'], \\'load_tacotron2_model\\': \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp32\\')\", \\'prepare_tacotron2_model\\': [\"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\'], \\'synthesize_speech\\': [\\'text = \"hello world, I missed you so much\"\\', \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'save_audio\\': \\'write(\"audio.wav\", rate, audio_numpy)\\', \\'play_audio\\': \\'Audio(audio_numpy, rate=rate)\\'}, \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': None}, \\'description\\': \\'The Tacotron 2 and WaveGlow model form a text-to-speech system that enables users to synthesize natural-sounding speech from raw transcripts without any additional prosody information. The Tacotron 2 model produces mel spectrograms from input text using encoder-decoder architecture. WaveGlow is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 99, "text": " Retrieve an API capable of re-identifying vehicles across different cameras by using appearance invariance.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Once-for-all (OFA) Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'mit-han-lab/once-for-all\\', model=\\'ofa_supernet_mbv3_w10\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'mit-han-lab/once-for-all\\', \\'model\\': \\'ofa_supernet_mbv3_w10\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"super_net_name = \\'ofa_supernet_mbv3_w10\\'\", \"super_net = torch.hub.load(\\'mit-han-lab/once-for-all\\', super_net_name, pretrained=True).eval()\"], \\'performance\\': {\\'description\\': \\'OFA networks outperform state-of-the-art NAS methods (up to 4.0% ImageNet top1 accuracy improvement over MobileNetV3, or same accuracy but 1.5x faster than MobileNetV3, 2.6x faster than EfficientNet w.r.t measured latency) while reducing many orders of magnitude GPU hours and CO2 emission.\\'}, \\'description\\': \\'Once-for-all (OFA) networks are a family of neural networks designed by MIT Han Lab. They decouple training and search, achieving efficient inference across various edge devices and resource constraints. OFA networks are pretrained on the IMAGENET dataset and are capable of classifying images into different categories.\\'}', metadata={})]", "category": "generic"}
{"question_id": 100, "text": " I want to classify some images using a state-of-the-art model. Can you provide me an API to help in this task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 101, "text": " Show me an API that can efficiently classify images on mobile platforms.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 102, "text": " We are developing an app that can guess the type of a picture. We need it to work on most platforms with almost the same efficiency. Give me an API that can do it.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 103, "text": " A company wants to develop a photo sharing app like Instagram. Recommend an API to recognize objects in the photos uploaded by users.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 104, "text": " Google Photos wants to create a way to classify images uploaded by users into different categories. Recommend an API for this purpose.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'GPUNet Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \\'api_arguments\\': {\\'repository\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_gpunet\\', \\'pretrained\\': \\'True\\', \\'model_type\\': \\'GPUNet-0\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'validators\\', \\'matplotlib\\', \\'timm==0.5.4\\'], \\'example_code\\': [\\'import torch\\', \"model_type = \\'GPUNet-0\\'\", \"precision = \\'fp32\\'\", \"gpunet = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'gpunet.to(device)\\', \\'gpunet.eval()\\'], \\'performance\\': {\\'dataset\\': \\'IMAGENET\\', \\'description\\': \\'GPUNet demonstrates state-of-the-art inference performance up to 2x faster than EfficientNet-X and FBNet-V3.\\'}, \\'description\\': \\'GPUNet is a family of Convolutional Neural Networks designed by NVIDIA using novel Neural Architecture Search (NAS) methods. They are optimized for NVIDIA GPU and TensorRT performance. GPUNet models are pretrained on the IMAGENET dataset and are capable of classifying images into different categories. The models are provided by the NVIDIA Deep Learning Examples library.\\'}', metadata={})]", "category": "generic"}
{"question_id": 105, "text": " Help me build a bird detection system. Recommend me an API that I can adapt for bird classification from photographs. \\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 106, "text": " I have an image with animals in it; I need to know the species. Can you suggest an image recognition API that can identify the species of animals in the given image?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 107, "text": " I want to create an AI tool that automates recognizing objects in an image. Recommend an API that can do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 108, "text": " Is there any API that can identify plants from an image I provide?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 109, "text": " A mobile app developer needs an image classification API that can be used on a range of mobile devices without the need to adjust the model size. Recommend an API that fits this purpose.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 110, "text": " I'm building an image classification app to classify animals. Tell me an API that can classify an input image into a specific category.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SqueezeNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'squeezenet1_0\\', pretrained=True)\", \\'api_arguments\\': {\\'version\\': \\'v0.10.0\\', \\'model\\': [\\'squeezenet1_0\\'], \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'squeezenet1_0\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'squeezenet1_0\\': {\\'Top-1 error\\': 41.9, \\'Top-5 error\\': 19.58}}}, \\'description\\': \\'SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy.\\'}', metadata={})]", "category": "generic"}
{"question_id": 111, "text": " I want to create a 3D visualization of a room using only a single image. How can I estimate the depth of the objects in the room from that image?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Computing relative depth from a single image\\', \\'api_name\\': \\'MiDaS\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'intel-isl/MiDaS\\', model=\\'DPT_Large\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'intel-isl/MiDaS\\', \\'model\\': \\'model_type\\'}, \\'python_environment_requirements\\': \\'pip install timm\\', \\'example_code\\': [\\'import cv2\\', \\'import torch\\', \\'import urllib.request\\', \\'import matplotlib.pyplot as plt\\', \"url, filename = (\\'https://github.com/pytorch/hub/raw/master/images/dog.jpg\\', \\'dog.jpg\\')\", \\'urllib.request.urlretrieve(url, filename)\\', \"model_type = \\'DPT_Large\\'\", \"midas = torch.hub.load(\\'intel-isl/MiDaS\\', \\'DPT_Large\\')\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'midas.to(device)\\', \\'midas.eval()\\', \"midas_transforms = torch.hub.load(\\'intel-isl/MiDaS\\', \\'transforms\\')\", \"if model_type == \\'DPT_Large\\' or model_type == \\'DPT_Hybrid\\':\", \\' transform = midas_transforms.dpt_transform\\', \\'else:\\', \\' transform = midas_transforms.small_transform\\', \\'img = cv2.imread(filename)\\', \\'img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\\', \\'input_batch = transform(img).to(device)\\', \\'with torch.no_grad():\\', \\' prediction = midas(input_batch)\\', \\'prediction = torch.nn.functional.interpolate(\\', \\' prediction.unsqueeze(1),\\', \\' size=img.shape[:2],\\', \" mode=\\'bicubic\\',\", \\' align_corners=False,\\', \\').squeeze()\\', \\'output = prediction.cpu().numpy()\\', \\'plt.imshow(output)\\', \\'plt.show()\\'], \\'performance\\': {\\'dataset\\': \\'10 distinct datasets\\', \\'accuracy\\': \\'Multi-objective optimization\\'}, \\'description\\': \\'MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs.\\'}', metadata={})]", "category": "generic"}
{"question_id": 112, "text": " Give me an API that can predict the category of an object given its image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 113, "text": " Can you provide a GAN API that can generate high-quality 64x64 images for an apparel ecommerce company?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 114, "text": " I am a city planner responsible for managing different areas of the city. Recommend an API that can segment roads, parks and buildings from a satellite image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 115, "text": " Recommend an API that can be used for bird species recognition using pictures taken by a wildlife photographer.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 116, "text": " I am starting a startup that recommends clothing to users based on images of their outfits. What is a good API for this?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Generative Adversarial Networks (GANs)\\', \\'api_name\\': \\'PGAN\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/pytorch_GAN_zoo:hub\\', model=\\'PGAN\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'facebookresearch/pytorch_GAN_zoo:hub\\', \\'model\\': \\'PGAN\\', \\'model_name\\': \\'celebAHQ-512\\', \\'pretrained\\': \\'True\\', \\'useGPU\\': \\'use_gpu\\'}, \\'python_environment_requirements\\': \\'Python 3\\', \\'example_code\\': {\\'import\\': \\'import torch\\', \\'use_gpu\\': \\'use_gpu = True if torch.cuda.is_available() else False\\', \\'load_model\\': \"model = torch.hub.load(\\'facebookresearch/pytorch_GAN_zoo:hub\\', \\'PGAN\\', model_name=\\'celebAHQ-512\\', pretrained=True, useGPU=use_gpu)\", \\'build_noise_data\\': \\'noise, _ = model.buildNoiseData(num_images)\\', \\'test\\': \\'generated_images = model.test(noise)\\', \\'plot_images\\': {\\'import_matplotlib\\': \\'import matplotlib.pyplot as plt\\', \\'import_torchvision\\': \\'import torchvision\\', \\'make_grid\\': \\'grid = torchvision.utils.make_grid(generated_images.clamp(min=-1, max=1), scale_each=True, normalize=True)\\', \\'imshow\\': \\'plt.imshow(grid.permute(1, 2, 0).cpu().numpy())\\', \\'show\\': \\'plt.show()\\'}}, \\'performance\\': {\\'dataset\\': \\'celebA\\', \\'accuracy\\': \\'High-quality celebrity faces\\'}, \\'description\\': \"Progressive Growing of GANs (PGAN) is a method for generating high-resolution images using generative adversarial networks. The model is trained progressively, starting with low-resolution images and gradually increasing the resolution until the desired output is achieved. This implementation is based on the paper by Tero Karras et al., \\'Progressive Growing of GANs for Improved Quality, Stability, and Variation\\'.\"}', metadata={})]", "category": "generic"}
{"question_id": 117, "text": " Generate an API that performs image classification using a small model with low computational requirements.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'HarDNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'PingoLH/Pytorch-HarDNet\\', model=\\'hardnet68\\', pretrained=True)\", \\'api_arguments\\': [{\\'name\\': \\'hardnet68\\', \\'type\\': \\'str\\', \\'description\\': \\'HarDNet-68 model\\'}], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'PingoLH/Pytorch-HarDNet\\', \\'hardnet68\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'hardnet68\\': {\\'Top-1 error\\': 23.52, \\'Top-5 error\\': 6.99}}}, \\'description\\': \\'Harmonic DenseNet (HarDNet) is a low memory trafficCNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet).\\'}', metadata={})]", "category": "generic"}
{"question_id": 118, "text": " I need an efficient AI-based classifier to identify products on grocery store shelves. Suggest an appropriate API to implement this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 119, "text": " I want to perform image classification for optimizing the storage space of a database. Provide an API that enables this while maintaining accuracy.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 120, "text": " I am a content writer for Marvel Studios and I am trying to categorize certain images of the characters based on their similarity. Recommend an API that can classify an image of a Marvel character.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 121, "text": " A digital artist needs an API that can recognize and classify images containing multiple objects. Which API would you suggest?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 122, "text": " Suggest an API for a wildlife conservation organization that could help them identify animals from images captured by their research cameras.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 123, "text": " What would be a suitable API for an application that classifies images of autonomous driving from different devices and should be efficient in terms of size?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 124, "text": " I am a developer at Audible and I am looking for an API that can convert text to speech, find something suitable.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 125, "text": " You are tasked to parse images in a storage platform to classify a set of new products. Suggest me an API that can help you do this classification task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 126, "text": " I am building an app to identify poisonous and non-poisonous mushrooms by taking a picture of it. Suggest an API to help me classify the pictures taken.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 127, "text": " Can you provide me an API for classifying a video content based on the actions performed in it?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Video Classification\\', \\'framework\\': \\'PyTorchVideo\\', \\'functionality\\': \\'X3D Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/pytorchvideo\\', model=\\'x3d_s\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'facebookresearch/pytorchvideo\\', \\'model\\': \\'x3d_s\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'pytorchvideo\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/pytorchvideo\\', \\'x3d_s\\', pretrained=True)\", \"device = \\'cpu\\'\", \\'model = model.eval()\\', \\'model = model.to(device)\\'], \\'performance\\': {\\'dataset\\': \\'Kinetics 400\\', \\'accuracy\\': {\\'top1\\': 73.33, \\'top5\\': 91.27}, \\'flops\\': 2.96, \\'params\\': 3.79}, \\'description\\': \"X3D model architectures are based on the paper \\'X3D: Expanding Architectures for Efficient Video Recognition\\' by Christoph Feichtenhofer. They are pretrained on the Kinetics 400 dataset. This model is capable of classifying video clips into different action categories. It is provided by the FAIR PyTorchVideo library.\"}', metadata={})]", "category": "generic"}
{"question_id": 128, "text": " A startup called \\\"DriveMe\\\" is building a vehicular safety app and wants to detect traffic objects, segment drivable areas, and detect lanes in real-time. Suggest an API to help them achieve their goal.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 129, "text": " Identify an API which detects voice activity in an audio file and share the code to load it.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 130, "text": " Help me identify various objects in an image. Suggest an API for performing image classification.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 131, "text": " A marketing company needs an API to classify images into animals and assign them different categories. Which API would you recommend them?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'GPUNet Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \\'api_arguments\\': {\\'repository\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_gpunet\\', \\'pretrained\\': \\'True\\', \\'model_type\\': \\'GPUNet-0\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'validators\\', \\'matplotlib\\', \\'timm==0.5.4\\'], \\'example_code\\': [\\'import torch\\', \"model_type = \\'GPUNet-0\\'\", \"precision = \\'fp32\\'\", \"gpunet = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_gpunet\\', pretrained=True, model_type=model_type, model_math=precision)\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'gpunet.to(device)\\', \\'gpunet.eval()\\'], \\'performance\\': {\\'dataset\\': \\'IMAGENET\\', \\'description\\': \\'GPUNet demonstrates state-of-the-art inference performance up to 2x faster than EfficientNet-X and FBNet-V3.\\'}, \\'description\\': \\'GPUNet is a family of Convolutional Neural Networks designed by NVIDIA using novel Neural Architecture Search (NAS) methods. They are optimized for NVIDIA GPU and TensorRT performance. GPUNet models are pretrained on the IMAGENET dataset and are capable of classifying images into different categories. The models are provided by the NVIDIA Deep Learning Examples library.\\'}', metadata={})]", "category": "generic"}
{"question_id": 132, "text": " Recommend an API for a mobile app that can identify fruits from images taken by the users.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 133, "text": " A city is planning to survey the land for urban development. Provide me with an API that can identify buildings and roads from an aerial photo.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 134, "text": " I need an efficient model for classifying animals in images taken by wildlife cameras. Suggest me an API for this purpose.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Semi-supervised and semi-weakly supervised ImageNet Models\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/semi-supervised-ImageNet1K-models\\', model=\\'resnet18_swsl\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'model\\': \\'resnet18_swsl\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'facebookresearch/semi-supervised-ImageNet1K-models\\', \\'resnet18_swsl\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'description\\': \\'Semi-supervised and semi-weakly supervised ImageNet models achieve state-of-the-art accuracy of 81.2% on ImageNet for the widely used/adopted ResNet-50 model architecture.\\'}, \\'description\\': \"Semi-supervised and semi-weakly supervised ImageNet Models are introduced in the \\'Billion scale semi-supervised learning for image classification\\' paper. These models are pretrained on a subset of unlabeled YFCC100M public image dataset and fine-tuned with the ImageNet1K training dataset. They are capable of classifying images into different categories and are provided by the Facebook Research library.\"}', metadata={})]", "category": "generic"}
{"question_id": 135, "text": " The company is creating a neural network model that can run efficiently on different hardware platforms. Tell me an API that specializes CNNs for different hardware.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 136, "text": " Farlando Corp has an application that runs on their customers' GPUs, and they want a neural network that is optimized on GPU performance. Recommend an API that they can use for image classification.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 137, "text": " I need an efficient model for image classification with good accuracy. Provide me with an API that uses LIF neurons.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 138, "text": " As a market research analyst, I want to find a tool to classify different product types using their images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Text-to-Speech\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Speech Synthesis\\', \\'api_name\\': \\'WaveGlow\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_waveglow\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'NVIDIA/DeepLearningExamples:torchhub\\', \\'model\\': \\'nvidia_waveglow\\', \\'model_math\\': \\'fp32\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': {\\'load_waveglow_model\\': \"waveglow = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_waveglow\\', model_math=\\'fp32\\')\", \\'prepare_waveglow_model\\': [\\'waveglow = waveglow.remove_weightnorm(waveglow)\\', \"waveglow = waveglow.to(\\'cuda\\')\", \\'waveglow.eval()\\'], \\'load_tacotron2_model\\': \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp32\\')\", \\'prepare_tacotron2_model\\': [\"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\'], \\'synthesize_speech\\': [\\'text = \"hello world, I missed you so much\"\\', \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'save_audio\\': \\'write(\"audio.wav\", rate, audio_numpy)\\', \\'play_audio\\': \\'Audio(audio_numpy, rate=rate)\\'}, \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': None}, \\'description\\': \\'The Tacotron 2 and WaveGlow model form a text-to-speech system that enables users to synthesize natural-sounding speech from raw transcripts without any additional prosody information. The Tacotron 2 model produces mel spectrograms from input text using encoder-decoder architecture. WaveGlow is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 139, "text": " A media company that works with image recognition is trying to identify an object in an image. Recommend an API that specializes in image recognition.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 140, "text": " Inform me of an API that can help identify famous landmarks from images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 141, "text": " I am working on an image classification project where accuracy is important, and I need a pretrained model that has a lower error rate when classifying images. What model might work for me?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 142, "text": " The New York Times wants to classify some information about Jim Henson. Recommend an API to analyze and classify the text.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 143, "text": " Recommend a pretrained API that classifies animals from an image given the photo of the animal.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 144, "text": " I have a picture of my dog and I want to classify its breed. Provide me an API to do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 145, "text": " A developer at Pinterest wants to automatically categorize uploaded images based on their content. Provide an API suggestion that can help with this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 146, "text": " A startup is working on a computer vision application supporting autonomous drones. Can you provide an API that can compute the relative depth of an object in a given image?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 147, "text": " Imagine you are trying to build podcast transcription for people who are impaired. Get an API to transcribe a sample podcast from Spotify.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 148, "text": " A tourist is planning to take a picture of a beautiful scene but wants to separate the people from the background. Recommend an API to help do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'Inception_v3\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'inception_v3\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'1.9.0\\', \\'torchvision\\': \\'0.10.0\\'}, \\'example_code\\': {\\'import_libraries\\': \\'import torch\\', \\'load_model\\': \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'inception_v3\\', pretrained=True)\", \\'model_evaluation\\': \\'model.eval()\\'}, \\'performance\\': {\\'dataset\\': \\'imagenet\\', \\'accuracy\\': {\\'top-1_error\\': 22.55, \\'top-5_error\\': 6.44}}, \\'description\\': \\'Inception v3, also called GoogleNetv3, is a famous Convolutional Neural Network trained on the ImageNet dataset from 2015. It is based on the exploration of ways to scale up networks to utilize the added computation as efficiently as possible by using suitably factorized convolutions and aggressive regularization. The model achieves a top-1 error of 22.55% and a top-5 error of 6.44% on the ImageNet dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 149, "text": " I took a photo and I want to detect all the objects in the image. Provide me with an API to do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 150, "text": " Find an API that can generate new images of various clothing styles in 64x64 resolution using Generative Adversarial Networks.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Generative Adversarial Networks\\', \\'api_name\\': \\'DCGAN\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'facebookresearch/pytorch_GAN_zoo:hub\\', model=\\'DCGAN\\', pretrained=True, useGPU=use_gpu)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\', \\'useGPU\\': \\'use_gpu\\'}, \\'python_environment_requirements\\': \\'Python 3\\', \\'example_code\\': {\\'import\\': [\\'import torch\\', \\'import matplotlib.pyplot as plt\\', \\'import torchvision\\'], \\'use_gpu\\': \\'use_gpu = True if torch.cuda.is_available() else False\\', \\'load_model\\': \"model = torch.hub.load(\\'facebookresearch/pytorch_GAN_zoo:hub\\', \\'DCGAN\\', pretrained=True, useGPU=use_gpu)\", \\'build_noise_data\\': \\'noise, _ = model.buildNoiseData(num_images)\\', \\'generate_images\\': \\'with torch.no_grad(): generated_images = model.test(noise)\\', \\'plot_images\\': [\\'plt.imshow(torchvision.utils.make_grid(generated_images).permute(1, 2, 0).cpu().numpy())\\', \\'plt.show()\\']}, \\'performance\\': {\\'dataset\\': \\'FashionGen\\', \\'accuracy\\': \\'N/A\\'}, \\'description\\': \\'DCGAN is a model designed in 2015 by Radford et. al. in the paper Unsupervised Representation Learning with Deep Convolutional Generative Adversarial Networks. It is a GAN architecture both very simple and efficient for low resolution image generation (up to 64x64).\\'}', metadata={})]", "category": "generic"}
{"question_id": 151, "text": " I am trying to classify an image to find its category. Please give me an API that can identify the content of an image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 152, "text": " I would like to convert text to natural sounding speech using Deep Learning. Can you provide me with an API to achieve this?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 153, "text": " Design a system to diagnose diseases from X-Ray images. Recommend an appropriate API for classifying diseases in the X-Ray images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 154, "text": " A smartphone company is developing an app that can classify object from a picture. Provide an API that can achieve this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 155, "text": " I want to create an app that recognizes items from pictures taken by users. Can you recommend any machine learning API for this purpose?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 156, "text": " Recommend an API that can be used for image classification tasks on a dataset of images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 157, "text": " Find out an API that can identify 102 different types of flowers from an image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 158, "text": " Can you recommend an API for image classification which is efficient in terms of computational resources and has decent accuracy?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'SqueezeNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'squeezenet1_1\\', pretrained=True)\", \\'api_arguments\\': {\\'version\\': \\'v0.10.0\\', \\'model\\': [\\'squeezenet1_1\\'], \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'squeezenet1_1\\', pretrained=True)\", \\'model.eval()\\', \\'from PIL import Image\\', \\'from torchvision import transforms\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'squeezenet1_1\\': {\\'Top-1 error\\': 41.81, \\'Top-5 error\\': 19.38}}}, \\'description\\': \\'SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy.\\'}', metadata={})]", "category": "generic"}
{"question_id": 159, "text": " A photography service needs a fast algorithm to recognize objects in their images from the ImageNet dataset out of the box. What API should they use?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 160, "text": " Can you suggest an API for classifying images in my dataset using a model with spiking neural networks?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 161, "text": " I am trying to recognize objects in an image using a popular image classification model. Which model should I use?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 162, "text": " I want to create an app to recognize objects in images. Which API is suitable for this task?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 163, "text": " Air Traffic Control needs an image classifier to identify if an image contains an aircraft or not. Suggest an API that would be suitable for this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 164, "text": " A smart fridge wants to identify food items from images taken from its camera. Provide an API to identify the food items.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 165, "text": " I want to count how many people are present in a room using an image. Tell me an API that can do this task.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 166, "text": " I am developing a website that can predict the content of an image based on its URL. What API would you recommend with a code example?\\n###Input: {\\\"image_url\\\": \\\"https://example.com/image.jpg\\\"}\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 167, "text": " A wildlife photographer wants to classify animals in images taken during a safari. Provide me with an API that can help classify these animals.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 168, "text": " I want to use my camera app to identify objects that I point it to. What API would you recommend?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 169, "text": " I am building an image classification model and want to achieve a high accuracy. Which API should I use?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 170, "text": " A photographer at a film studio wants to find the relative depth from a single image. Recommend an API that can compute relative depth from an input image.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Computing relative depth from a single image\\', \\'api_name\\': \\'MiDaS\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'intel-isl/MiDaS\\', model=\\'DPT_Large\\', pretrained=True)\", \\'api_arguments\\': {\\'repo_or_dir\\': \\'intel-isl/MiDaS\\', \\'model\\': \\'model_type\\'}, \\'python_environment_requirements\\': \\'pip install timm\\', \\'example_code\\': [\\'import cv2\\', \\'import torch\\', \\'import urllib.request\\', \\'import matplotlib.pyplot as plt\\', \"url, filename = (\\'https://github.com/pytorch/hub/raw/master/images/dog.jpg\\', \\'dog.jpg\\')\", \\'urllib.request.urlretrieve(url, filename)\\', \"model_type = \\'DPT_Large\\'\", \"midas = torch.hub.load(\\'intel-isl/MiDaS\\', \\'DPT_Large\\')\", \"device = torch.device(\\'cuda\\') if torch.cuda.is_available() else torch.device(\\'cpu\\')\", \\'midas.to(device)\\', \\'midas.eval()\\', \"midas_transforms = torch.hub.load(\\'intel-isl/MiDaS\\', \\'transforms\\')\", \"if model_type == \\'DPT_Large\\' or model_type == \\'DPT_Hybrid\\':\", \\' transform = midas_transforms.dpt_transform\\', \\'else:\\', \\' transform = midas_transforms.small_transform\\', \\'img = cv2.imread(filename)\\', \\'img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\\', \\'input_batch = transform(img).to(device)\\', \\'with torch.no_grad():\\', \\' prediction = midas(input_batch)\\', \\'prediction = torch.nn.functional.interpolate(\\', \\' prediction.unsqueeze(1),\\', \\' size=img.shape[:2],\\', \" mode=\\'bicubic\\',\", \\' align_corners=False,\\', \\').squeeze()\\', \\'output = prediction.cpu().numpy()\\', \\'plt.imshow(output)\\', \\'plt.show()\\'], \\'performance\\': {\\'dataset\\': \\'10 distinct datasets\\', \\'accuracy\\': \\'Multi-objective optimization\\'}, \\'description\\': \\'MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs.\\'}', metadata={})]", "category": "generic"}
{"question_id": 171, "text": " A bird watching society is developing an app that can identify birds in a picture. Provide a suitable API that can be used for classifying birds from images.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 172, "text": " Provide an API recommendation for a call center which wants to convert customer voice calls into text.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 173, "text": " Provide me with an API that can tackle city-scape segmentation in autonomous driving application.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Object Detection, Drivable Area Segmentation, Lane Detection\\', \\'api_name\\': \\'YOLOP\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'hustvl/yolop\\', model=\\'yolop\\', pretrained=True)\", \\'api_arguments\\': \\'pretrained\\', \\'python_environment_requirements\\': \\'pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt\\', \\'example_code\\': \"import torch\\\\nmodel = torch.hub.load(\\'hustvl/yolop\\', \\'yolop\\', pretrained=True)\\\\nimg = torch.randn(1,3,640,640)\\\\ndet_out, da_seg_out,ll_seg_out = model(img)\", \\'performance\\': {\\'dataset\\': \\'BDD100K\\', \\'accuracy\\': {\\'Object Detection\\': {\\'Recall(%)\\': 89.2, \\'mAP50(%)\\': 76.5, \\'Speed(fps)\\': 41}, \\'Drivable Area Segmentation\\': {\\'mIOU(%)\\': 91.5, \\'Speed(fps)\\': 41}, \\'Lane Detection\\': {\\'mIOU(%)\\': 70.5, \\'IOU(%)\\': 26.2}}}, \\'description\\': \\'YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset.\\'}', metadata={})]", "category": "generic"}
{"question_id": 174, "text": " I need an API to extract features from a collection of photographs taken at the 2022 Olympics.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Efficient networks by generating more features from cheap operations\\', \\'api_name\\': \\'GhostNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'huawei-noah/ghostnet\\', model=\\'ghostnet_1x\\', pretrained=True)\", \\'api_arguments\\': [\\'pretrained\\'], \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\', \\'PIL\\'], \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'huawei-noah/ghostnet\\', \\'ghostnet_1x\\', pretrained=True)\", \\'model.eval()\\', \\'input_image = Image.open(filename)\\', \\'preprocess = transforms.Compose([\\', \\' transforms.Resize(256),\\', \\' transforms.CenterCrop(224),\\', \\' transforms.ToTensor(),\\', \\' transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\', \\'])\\', \\'input_tensor = preprocess(input_image)\\', \\'input_batch = input_tensor.unsqueeze(0)\\', \\'if torch.cuda.is_available():\\', \" input_batch = input_batch.to(\\'cuda\\')\", \" model.to(\\'cuda\\')\", \\'with torch.no_grad():\\', \\' output = model(input_batch)\\', \\'probabilities = torch.nn.functional.softmax(output[0], dim=0)\\', \\'print(probabilities)\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'Top-1 acc\\': \\'73.98\\', \\'Top-5 acc\\': \\'91.46\\'}}, \\'description\\': \\'The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff.\\'}', metadata={})]", "category": "generic"}
{"question_id": 175, "text": " An E-commerce manager wants to develop an image classification system for their products. They need a powerful pre-trained model as a starting point. Recommend an API for this purpose.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Semantic Segmentation\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Neural Machine Translation\\', \\'api_name\\': \\'Transformer (NMT)\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/fairseq\\')\", \\'api_arguments\\': [\\'model_name\\', \\'tokenizer\\', \\'bpe\\', \\'beam\\', \\'sampling\\', \\'sampling_topk\\'], \\'python_environment_requirements\\': [\\'bitarray\\', \\'fastBPE\\', \\'hydra-core\\', \\'omegaconf\\', \\'regex\\', \\'requests\\', \\'sacremoses\\', \\'subword_nmt\\'], \\'example_code\\': \"import torch\\\\n\\\\nen2fr = torch.hub.load(\\'pytorch/fairseq\\', \\'transformer.wmt14.en-fr\\', tokenizer=\\'moses\\', bpe=\\'subword_nmt\\')\\\\n\\\\nen2fr.cuda()\\\\n\\\\nfr = en2fr.translate(\\'Hello world!\\', beam=5)\\\\nassert fr == \\'Bonjour \u00e0 tous !\\'\", \\'performance\\': {\\'dataset\\': [{\\'name\\': \"WMT\\'14\", \\'accuracy\\': \\'Not provided\\'}, {\\'name\\': \"WMT\\'18\", \\'accuracy\\': \\'Not provided\\'}, {\\'name\\': \"WMT\\'19\", \\'accuracy\\': \\'Not provided\\'}]}, \\'description\\': \"Transformer (NMT) is a powerful sequence-to-sequence modeling architecture that produces state-of-the-art neural machine translation systems. It is based on the paper \\'Attention Is All You Need\\' and has been improved using techniques such as large-scale semi-supervised training, back-translation, and noisy-channel reranking. It supports English-French and English-German translation as well as round-trip translation for paraphrasing.\"}', metadata={})]", "category": "generic"}
{"question_id": 176, "text": " I need an API to classify images with known objects. Suggest a suitable model that can do this.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 177, "text": " A delivery company wants to recognize if a package is damaged during shipment. Propose an API that can classify images into damaged and undamaged packages.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 178, "text": " An image recognition app needs to identify objects from the images it captures. Suggest an API which is optimized for GPUs.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 179, "text": " Show me an API that provides easy to use neural networks for classifying different types of wildlife on mobile platforms.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Once-for-all (OFA) Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'mit-han-lab/once-for-all\\', model=\\'ofa_supernet_mbv3_w10\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'mit-han-lab/once-for-all\\', \\'model\\': \\'ofa_supernet_mbv3_w10\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"super_net_name = \\'ofa_supernet_mbv3_w10\\'\", \"super_net = torch.hub.load(\\'mit-han-lab/once-for-all\\', super_net_name, pretrained=True).eval()\"], \\'performance\\': {\\'description\\': \\'OFA networks outperform state-of-the-art NAS methods (up to 4.0% ImageNet top1 accuracy improvement over MobileNetV3, or same accuracy but 1.5x faster than MobileNetV3, 2.6x faster than EfficientNet w.r.t measured latency) while reducing many orders of magnitude GPU hours and CO2 emission.\\'}, \\'description\\': \\'Once-for-all (OFA) networks are a family of neural networks designed by MIT Han Lab. They decouple training and search, achieving efficient inference across various edge devices and resource constraints. OFA networks are pretrained on the IMAGENET dataset and are capable of classifying images into different categories.\\'}', metadata={})]", "category": "generic"}
{"question_id": 180, "text": " Recommend an API for identifying defective parts in a manufacturing assembly line based on images taken by an inspection system.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 181, "text": " Identify an image classification API that can be used to determine if an object is a car, a bike, or a pedestrian.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Image Classification\\', \\'api_name\\': \\'AlexNet\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'pytorch/vision\\', model=\\'alexnet\\', pretrained=True)\", \\'api_arguments\\': {\\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': {\\'torch\\': \\'>=1.9.0\\', \\'torchvision\\': \\'>=0.10.0\\'}, \\'example_code\\': [\\'import torch\\', \"model = torch.hub.load(\\'pytorch/vision:v0.10.0\\', \\'alexnet\\', pretrained=True)\", \\'model.eval()\\'], \\'performance\\': {\\'dataset\\': \\'ImageNet\\', \\'accuracy\\': {\\'top-1_error\\': 43.45, \\'top-5_error\\': 20.91}}, \\'description\\': \\'AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks.\\'}', metadata={})]", "category": "generic"}
{"question_id": 182, "text": " I need an API to classify images efficiently without sacrificing too much accuracy. Can you provide me with one?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 183, "text": " To save the environment, a student wants to evaluate how green his schools area is. Tell me an AI API which can classify the images of plants in his environment and tell the name of the plants.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
{"question_id": 184, "text": " I need an efficient API to classify images on multiple edge devices with different resource constraints. Suggest one for me.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Once-for-all (OFA) Networks\\', \\'api_name\\': \\'torch.hub.load\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'mit-han-lab/once-for-all\\', model=\\'ofa_supernet_mbv3_w10\\', pretrained=True)\", \\'api_arguments\\': {\\'repository\\': \\'mit-han-lab/once-for-all\\', \\'model\\': \\'ofa_supernet_mbv3_w10\\', \\'pretrained\\': \\'True\\'}, \\'python_environment_requirements\\': [\\'torch\\', \\'torchvision\\'], \\'example_code\\': [\\'import torch\\', \"super_net_name = \\'ofa_supernet_mbv3_w10\\'\", \"super_net = torch.hub.load(\\'mit-han-lab/once-for-all\\', super_net_name, pretrained=True).eval()\"], \\'performance\\': {\\'description\\': \\'OFA networks outperform state-of-the-art NAS methods (up to 4.0% ImageNet top1 accuracy improvement over MobileNetV3, or same accuracy but 1.5x faster than MobileNetV3, 2.6x faster than EfficientNet w.r.t measured latency) while reducing many orders of magnitude GPU hours and CO2 emission.\\'}, \\'description\\': \\'Once-for-all (OFA) networks are a family of neural networks designed by MIT Han Lab. They decouple training and search, achieving efficient inference across various edge devices and resource constraints. OFA networks are pretrained on the IMAGENET dataset and are capable of classifying images into different categories.\\'}', metadata={})]", "category": "generic"}
{"question_id": 185, "text": " I want my app to be able to read aloud the text for audiobooks. Can you suggest me an API for converting text to speech?\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Classification\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Text-to-Speech\\', \\'api_name\\': \\'Tacotron 2\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \\'api_arguments\\': {\\'model_math\\': \\'fp16\\'}, \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'librosa\\', \\'unidecode\\', \\'inflect\\', \\'libsndfile1\\'], \\'example_code\\': [\\'import torch\\', \"tacotron2 = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tacotron2\\', model_math=\\'fp16\\')\", \"tacotron2 = tacotron2.to(\\'cuda\\')\", \\'tacotron2.eval()\\', \"text = \\'Hello world, I missed you so much.\\'\", \"utils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_tts_utils\\')\", \\'sequences, lengths = utils.prepare_input_sequence([text])\\', \\'with torch.no_grad():\\', \\' mel, _, _ = tacotron2.infer(sequences, lengths)\\', \\' audio = waveglow.infer(mel)\\', \\'audio_numpy = audio[0].data.cpu().numpy()\\', \\'rate = 22050\\'], \\'performance\\': {\\'dataset\\': \\'LJ Speech\\', \\'accuracy\\': \\'Not specified\\'}, \\'description\\': \\'The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech.\\'}', metadata={})]", "category": "generic"}
{"question_id": 186, "text": " An app wants to identify dog breeds from images taken by users. Recommend an API that can classify the dog breed given a photo of a dog.\\n \n Use this API documentation for reference: [Document(page_content='{\\'domain\\': \\'Object Detection\\', \\'framework\\': \\'PyTorch\\', \\'functionality\\': \\'Single Shot MultiBox Detector\\', \\'api_name\\': \\'SSD\\', \\'api_call\\': \"torch.hub.load(repo_or_dir=\\'NVIDIA/DeepLearningExamples:torchhub\\', model=\\'nvidia_ssd\\', pretrained=True)\", \\'api_arguments\\': [\\'model_name\\'], \\'python_environment_requirements\\': [\\'numpy\\', \\'scipy\\', \\'scikit-image\\', \\'matplotlib\\'], \\'example_code\\': \"import torch\\\\n\\\\nssd_model = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd\\')\\\\nutils = torch.hub.load(\\'NVIDIA/DeepLearningExamples:torchhub\\', \\'nvidia_ssd_processing_utils\\')\\\\n\\\\nssd_model.to(\\'cuda\\')\\\\nssd_model.eval()\\\\n\\\\ninputs = [utils.prepare_input(uri) for uri in uris]\\\\ntensor = utils.prepare_tensor(inputs)\\\\n\\\\nwith torch.no_grad():\\\\n detections_batch = ssd_model(tensor)\\\\n\\\\nresults_per_input = utils.decode_results(detections_batch)\\\\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]\", \\'performance\\': {\\'dataset\\': {\\'name\\': \\'COCO\\', \\'accuracy\\': \\'Not provided\\'}}, \\'description\\': \"The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper \\'SSD: Single Shot MultiBox Detector\\'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency.\"}', metadata={})]", "category": "generic"}
