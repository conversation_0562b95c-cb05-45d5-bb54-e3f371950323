{"name": "python", "word": "identifier", "rules": {"module": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_statement"}}, "_statement": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_simple_statements"}, {"type": "SYMBOL", "name": "_compound_statement"}]}, "_simple_statements": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_simple_statement"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ";"}, {"type": "SYMBOL", "name": "_simple_statement"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_newline"}]}, "_simple_statement": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "future_import_statement"}, {"type": "SYMBOL", "name": "import_statement"}, {"type": "SYMBOL", "name": "import_from_statement"}, {"type": "SYMBOL", "name": "print_statement"}, {"type": "SYMBOL", "name": "assert_statement"}, {"type": "SYMBOL", "name": "expression_statement"}, {"type": "SYMBOL", "name": "return_statement"}, {"type": "SYMBOL", "name": "delete_statement"}, {"type": "SYMBOL", "name": "raise_statement"}, {"type": "SYMBOL", "name": "pass_statement"}, {"type": "SYMBOL", "name": "break_statement"}, {"type": "SYMBOL", "name": "continue_statement"}, {"type": "SYMBOL", "name": "global_statement"}, {"type": "SYMBOL", "name": "nonlocal_statement"}, {"type": "SYMBOL", "name": "exec_statement"}]}, "import_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "import"}, {"type": "SYMBOL", "name": "_import_list"}]}, "import_prefix": {"type": "REPEAT1", "content": {"type": "STRING", "value": "."}}, "relative_import": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "import_prefix"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "dotted_name"}, {"type": "BLANK"}]}]}, "future_import_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "from"}, {"type": "STRING", "value": "__future__"}, {"type": "STRING", "value": "import"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_import_list"}, {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "_import_list"}, {"type": "STRING", "value": ")"}]}]}]}, "import_from_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "from"}, {"type": "FIELD", "name": "module_name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "relative_import"}, {"type": "SYMBOL", "name": "dotted_name"}]}}, {"type": "STRING", "value": "import"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "wildcard_import"}, {"type": "SYMBOL", "name": "_import_list"}, {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "_import_list"}, {"type": "STRING", "value": ")"}]}]}]}, "_import_list": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "dotted_name"}, {"type": "SYMBOL", "name": "aliased_import"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "dotted_name"}, {"type": "SYMBOL", "name": "aliased_import"}]}}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, "aliased_import": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "dotted_name"}}, {"type": "STRING", "value": "as"}, {"type": "FIELD", "name": "alias", "content": {"type": "SYMBOL", "name": "identifier"}}]}, "wildcard_import": {"type": "STRING", "value": "*"}, "print_statement": {"type": "CHOICE", "members": [{"type": "PREC", "value": 1, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "print"}, {"type": "SYMBOL", "name": "chevron"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}}, {"type": "PREC", "value": -10, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "print"}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}}]}, "chevron": {"type": "SEQ", "members": [{"type": "STRING", "value": ">>"}, {"type": "SYMBOL", "name": "expression"}]}, "assert_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "assert"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "expression"}]}}]}]}, "expression_statement": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "expression"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, {"type": "SYMBOL", "name": "assignment"}, {"type": "SYMBOL", "name": "augmented_assignment"}, {"type": "SYMBOL", "name": "yield"}]}, "named_expression": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_named_expression_lhs"}}, {"type": "STRING", "value": ":="}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "_named_expression_lhs": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}]}, "return_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "return"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "BLANK"}]}]}, "delete_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "del"}, {"type": "SYMBOL", "name": "_expressions"}]}, "_expressions": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "expression_list"}]}, "raise_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "raise"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "from"}, {"type": "FIELD", "name": "cause", "content": {"type": "SYMBOL", "name": "expression"}}]}, {"type": "BLANK"}]}]}, "pass_statement": {"type": "PREC_LEFT", "value": 0, "content": {"type": "STRING", "value": "pass"}}, "break_statement": {"type": "PREC_LEFT", "value": 0, "content": {"type": "STRING", "value": "break"}}, "continue_statement": {"type": "PREC_LEFT", "value": 0, "content": {"type": "STRING", "value": "continue"}}, "_compound_statement": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "if_statement"}, {"type": "SYMBOL", "name": "for_statement"}, {"type": "SYMBOL", "name": "while_statement"}, {"type": "SYMBOL", "name": "try_statement"}, {"type": "SYMBOL", "name": "with_statement"}, {"type": "SYMBOL", "name": "function_definition"}, {"type": "SYMBOL", "name": "class_definition"}, {"type": "SYMBOL", "name": "decorated_definition"}, {"type": "SYMBOL", "name": "match_statement"}]}, "if_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "if"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "consequence", "content": {"type": "SYMBOL", "name": "_suite"}}, {"type": "REPEAT", "content": {"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "elif_clause"}}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "else_clause"}}, {"type": "BLANK"}]}]}, "elif_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "elif"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "consequence", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "else_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "else"}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "match_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "match"}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "subject", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "subject", "content": {"type": "SYMBOL", "name": "expression"}}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "REPEAT", "content": {"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "case_clause"}}}]}, "case_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "case"}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "pattern", "content": {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "list_splat_pattern"}]}, "named": true, "value": "case_pattern"}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "pattern", "content": {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "list_splat_pattern"}]}, "named": true, "value": "case_pattern"}}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "guard", "content": {"type": "SYMBOL", "name": "if_clause"}}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "consequence", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "for_statement": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "for"}, {"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_left_hand_side"}}, {"type": "STRING", "value": "in"}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expressions"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}, {"type": "FIELD", "name": "alternative", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "else_clause"}, {"type": "BLANK"}]}}]}, "while_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "while"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "else_clause"}}, {"type": "BLANK"}]}]}, "try_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "try"}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "except_clause"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "else_clause"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "finally_clause"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "except_group_clause"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "else_clause"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "finally_clause"}, {"type": "BLANK"}]}]}, {"type": "SYMBOL", "name": "finally_clause"}]}]}, "except_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "except"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "as"}, {"type": "STRING", "value": ","}]}, {"type": "SYMBOL", "name": "expression"}]}, {"type": "BLANK"}]}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_suite"}]}, "except_group_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "except*"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "as"}, {"type": "SYMBOL", "name": "expression"}]}, {"type": "BLANK"}]}]}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_suite"}]}, "finally_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "finally"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_suite"}]}, "with_statement": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "with"}, {"type": "SYMBOL", "name": "with_clause"}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "with_clause": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "with_item"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "with_item"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "with_item"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "with_item"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}]}, "with_item": {"type": "PREC_DYNAMIC", "value": 1, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "function_definition": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "def"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "parameters", "content": {"type": "SYMBOL", "name": "parameters"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "->"}, {"type": "FIELD", "name": "return_type", "content": {"type": "SYMBOL", "name": "type"}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "parameters": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_parameters"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "lambda_parameters": {"type": "SYMBOL", "name": "_parameters"}, "list_splat": {"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "SYMBOL", "name": "expression"}]}, "dictionary_splat": {"type": "SEQ", "members": [{"type": "STRING", "value": "**"}, {"type": "SYMBOL", "name": "expression"}]}, "global_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "global"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "identifier"}]}}]}]}, "nonlocal_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "nonlocal"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "identifier"}]}}]}]}, "exec_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "exec"}, {"type": "FIELD", "name": "code", "content": {"type": "SYMBOL", "name": "string"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "in"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "expression"}]}}]}]}, {"type": "BLANK"}]}]}, "class_definition": {"type": "SEQ", "members": [{"type": "STRING", "value": "class"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "superclasses", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "argument_list"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_suite"}}]}, "parenthesized_list_splat": {"type": "PREC", "value": 1, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "parenthesized_list_splat"}, "named": true, "value": "parenthesized_expression"}, {"type": "SYMBOL", "name": "list_splat"}]}, {"type": "STRING", "value": ")"}]}}, "argument_list": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "list_splat"}, {"type": "SYMBOL", "name": "dictionary_splat"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "parenthesized_list_splat"}, "named": true, "value": "parenthesized_expression"}, {"type": "SYMBOL", "name": "keyword_argument"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "list_splat"}, {"type": "SYMBOL", "name": "dictionary_splat"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "parenthesized_list_splat"}, "named": true, "value": "parenthesized_expression"}, {"type": "SYMBOL", "name": "keyword_argument"}]}]}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "decorated_definition": {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "decorator"}}, {"type": "FIELD", "name": "definition", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "class_definition"}, {"type": "SYMBOL", "name": "function_definition"}]}}]}, "decorator": {"type": "SEQ", "members": [{"type": "STRING", "value": "@"}, {"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "_newline"}]}, "_suite": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_simple_statements"}, "named": true, "value": "block"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_indent"}, {"type": "SYMBOL", "name": "block"}]}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_newline"}, "named": true, "value": "block"}]}, "block": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_statement"}}, {"type": "SYMBOL", "name": "_dedent"}]}, "expression_list": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "expression"}]}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}]}]}}, "dotted_name": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "SYMBOL", "name": "identifier"}]}}]}, "_parameters": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "parameter"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "parameter"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, "_patterns": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "pattern"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, "parameter": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "typed_parameter"}, {"type": "SYMBOL", "name": "default_parameter"}, {"type": "SYMBOL", "name": "typed_default_parameter"}, {"type": "SYMBOL", "name": "list_splat_pattern"}, {"type": "SYMBOL", "name": "tuple_pattern"}, {"type": "SYMBOL", "name": "keyword_separator"}, {"type": "SYMBOL", "name": "positional_separator"}, {"type": "SYMBOL", "name": "dictionary_splat_pattern"}]}, "pattern": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}, {"type": "SYMBOL", "name": "subscript"}, {"type": "SYMBOL", "name": "attribute"}, {"type": "SYMBOL", "name": "list_splat_pattern"}, {"type": "SYMBOL", "name": "tuple_pattern"}, {"type": "SYMBOL", "name": "list_pattern"}]}, "tuple_pattern": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_patterns"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "list_pattern": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_patterns"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "default_parameter": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "typed_default_parameter": {"type": "PREC", "value": -1, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "type"}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "list_splat_pattern": {"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}, {"type": "SYMBOL", "name": "subscript"}, {"type": "SYMBOL", "name": "attribute"}]}]}, "dictionary_splat_pattern": {"type": "SEQ", "members": [{"type": "STRING", "value": "**"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}, {"type": "SYMBOL", "name": "subscript"}, {"type": "SYMBOL", "name": "attribute"}]}]}, "as_pattern": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "STRING", "value": "as"}, {"type": "FIELD", "name": "alias", "content": {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "expression"}, "named": true, "value": "as_pattern_target"}}]}}, "_expression_within_for_in_clause": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "lambda_within_for_in_clause"}, "named": true, "value": "lambda"}]}, "expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "comparison_operator"}, {"type": "SYMBOL", "name": "not_operator"}, {"type": "SYMBOL", "name": "boolean_operator"}, {"type": "SYMBOL", "name": "await"}, {"type": "SYMBOL", "name": "lambda"}, {"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "conditional_expression"}, {"type": "SYMBOL", "name": "named_expression"}, {"type": "SYMBOL", "name": "as_pattern"}]}, "primary_expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "binary_operator"}, {"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}, {"type": "SYMBOL", "name": "string"}, {"type": "SYMBOL", "name": "concatenated_string"}, {"type": "SYMBOL", "name": "integer"}, {"type": "SYMBOL", "name": "float"}, {"type": "SYMBOL", "name": "true"}, {"type": "SYMBOL", "name": "false"}, {"type": "SYMBOL", "name": "none"}, {"type": "SYMBOL", "name": "unary_operator"}, {"type": "SYMBOL", "name": "attribute"}, {"type": "SYMBOL", "name": "subscript"}, {"type": "SYMBOL", "name": "call"}, {"type": "SYMBOL", "name": "list"}, {"type": "SYMBOL", "name": "list_comprehension"}, {"type": "SYMBOL", "name": "dictionary"}, {"type": "SYMBOL", "name": "dictionary_comprehension"}, {"type": "SYMBOL", "name": "set"}, {"type": "SYMBOL", "name": "set_comprehension"}, {"type": "SYMBOL", "name": "tuple"}, {"type": "SYMBOL", "name": "parenthesized_expression"}, {"type": "SYMBOL", "name": "generator_expression"}, {"type": "SYMBOL", "name": "ellipsis"}]}, "not_operator": {"type": "PREC", "value": 12, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "not"}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "boolean_operator": {"type": "CHOICE", "members": [{"type": "PREC_LEFT", "value": 11, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "and"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": 10, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "or"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}]}, "binary_operator": {"type": "CHOICE", "members": [{"type": "PREC_LEFT", "value": 18, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "+"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 18, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "-"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 19, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "*"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 19, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "@"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 19, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "/"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 19, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "%"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 19, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "//"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_RIGHT", "value": 21, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "**"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 14, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "|"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 15, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "&"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 16, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "^"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 17, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "<<"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, {"type": "PREC_LEFT", "value": 17, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": ">>"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}]}, "unary_operator": {"type": "PREC", "value": 20, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "+"}, {"type": "STRING", "value": "-"}, {"type": "STRING", "value": "~"}]}}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "primary_expression"}}]}}, "comparison_operator": {"type": "PREC_LEFT", "value": 13, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "primary_expression"}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "operators", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "<"}, {"type": "STRING", "value": "<="}, {"type": "STRING", "value": "=="}, {"type": "STRING", "value": "!="}, {"type": "STRING", "value": ">="}, {"type": "STRING", "value": ">"}, {"type": "STRING", "value": "<>"}, {"type": "STRING", "value": "in"}, {"type": "ALIAS", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "not"}, {"type": "STRING", "value": "in"}]}, "named": false, "value": "not in"}, {"type": "STRING", "value": "is"}, {"type": "ALIAS", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "is"}, {"type": "STRING", "value": "not"}]}, "named": false, "value": "is not"}]}}, {"type": "SYMBOL", "name": "primary_expression"}]}}]}}, "lambda": {"type": "PREC", "value": -2, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "lambda"}, {"type": "FIELD", "name": "parameters", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "lambda_parameters"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "lambda_within_for_in_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "lambda"}, {"type": "FIELD", "name": "parameters", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "lambda_parameters"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "_expression_within_for_in_clause"}}]}, "assignment": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_left_hand_side"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "="}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_right_hand_side"}}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "type"}}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "type"}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_right_hand_side"}}]}]}]}, "augmented_assignment": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_left_hand_side"}}, {"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "+="}, {"type": "STRING", "value": "-="}, {"type": "STRING", "value": "*="}, {"type": "STRING", "value": "/="}, {"type": "STRING", "value": "@="}, {"type": "STRING", "value": "//="}, {"type": "STRING", "value": "%="}, {"type": "STRING", "value": "**="}, {"type": "STRING", "value": ">>="}, {"type": "STRING", "value": "<<="}, {"type": "STRING", "value": "&="}, {"type": "STRING", "value": "^="}, {"type": "STRING", "value": "|="}]}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_right_hand_side"}}]}, "_left_hand_side": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "SYMBOL", "name": "pattern_list"}]}, "pattern_list": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "pattern"}]}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}]}]}, "_right_hand_side": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "expression_list"}, {"type": "SYMBOL", "name": "assignment"}, {"type": "SYMBOL", "name": "augmented_assignment"}, {"type": "SYMBOL", "name": "yield"}]}, "yield": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "yield"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "from"}, {"type": "SYMBOL", "name": "expression"}]}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "BLANK"}]}]}]}}, "attribute": {"type": "PREC", "value": 22, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "object", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "STRING", "value": "."}, {"type": "FIELD", "name": "attribute", "content": {"type": "SYMBOL", "name": "identifier"}}]}}, "subscript": {"type": "PREC", "value": 22, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "STRING", "value": "["}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "subscript", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "slice"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "FIELD", "name": "subscript", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "slice"}]}}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}}, "slice": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "BLANK"}]}]}, {"type": "BLANK"}]}]}, "ellipsis": {"type": "STRING", "value": "..."}, "call": {"type": "PREC", "value": 22, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "function", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "arguments", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "generator_expression"}, {"type": "SYMBOL", "name": "argument_list"}]}}]}}, "typed_parameter": {"type": "PREC", "value": -1, "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "list_splat_pattern"}, {"type": "SYMBOL", "name": "dictionary_splat_pattern"}]}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "type"}}]}}, "type": {"type": "SYMBOL", "name": "expression"}, "keyword_argument": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "keyword_identifier"}]}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "list": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_collection_elements"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "set": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "SYMBOL", "name": "_collection_elements"}, {"type": "STRING", "value": "}"}]}, "tuple": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_collection_elements"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "dictionary": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair"}, {"type": "SYMBOL", "name": "dictionary_splat"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair"}, {"type": "SYMBOL", "name": "dictionary_splat"}]}]}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}, "pair": {"type": "SEQ", "members": [{"type": "FIELD", "name": "key", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "list_comprehension": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "SYMBOL", "name": "_comprehension_clauses"}, {"type": "STRING", "value": "]"}]}, "dictionary_comprehension": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "pair"}}, {"type": "SYMBOL", "name": "_comprehension_clauses"}, {"type": "STRING", "value": "}"}]}, "set_comprehension": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "SYMBOL", "name": "_comprehension_clauses"}, {"type": "STRING", "value": "}"}]}, "generator_expression": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "SYMBOL", "name": "_comprehension_clauses"}, {"type": "STRING", "value": ")"}]}, "_comprehension_clauses": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "for_in_clause"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "for_in_clause"}, {"type": "SYMBOL", "name": "if_clause"}]}}]}, "parenthesized_expression": {"type": "PREC", "value": 1, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "yield"}]}, {"type": "STRING", "value": ")"}]}}, "_collection_elements": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "yield"}, {"type": "SYMBOL", "name": "list_splat"}, {"type": "SYMBOL", "name": "parenthesized_list_splat"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "yield"}, {"type": "SYMBOL", "name": "list_splat"}, {"type": "SYMBOL", "name": "parenthesized_list_splat"}]}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, "for_in_clause": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "for"}, {"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_left_hand_side"}}, {"type": "STRING", "value": "in"}, {"type": "FIELD", "name": "right", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_expression_within_for_in_clause"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "_expression_within_for_in_clause"}]}}]}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}}, "if_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "if"}, {"type": "SYMBOL", "name": "expression"}]}, "conditional_expression": {"type": "PREC_RIGHT", "value": -1, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "STRING", "value": "if"}, {"type": "SYMBOL", "name": "expression"}, {"type": "STRING", "value": "else"}, {"type": "SYMBOL", "name": "expression"}]}}, "concatenated_string": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "string"}, {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "string"}}]}, "string": {"type": "SEQ", "members": [{"type": "FIELD", "name": "prefix", "content": {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_string_start"}, "named": false, "value": "\""}}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "FIELD", "name": "interpolation", "content": {"type": "SYMBOL", "name": "interpolation"}}, {"type": "FIELD", "name": "string_content", "content": {"type": "SYMBOL", "name": "string_content"}}]}}, {"type": "FIELD", "name": "suffix", "content": {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_string_end"}, "named": false, "value": "\""}}]}, "string_content": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "REPEAT1", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_escape_interpolation"}, {"type": "SYMBOL", "name": "escape_sequence"}, {"type": "SYMBOL", "name": "_not_escape_sequence"}, {"type": "SYMBOL", "name": "_string_content"}]}}}, "interpolation": {"type": "SEQ", "members": [{"type": "IMMEDIATE_TOKEN", "content": {"type": "STRING", "value": "{"}}, {"type": "FIELD", "name": "expression", "content": {"type": "SYMBOL", "name": "_f_expression"}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "="}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "type_conversion", "content": {"type": "SYMBOL", "name": "type_conversion"}}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "format_specifier", "content": {"type": "SYMBOL", "name": "format_specifier"}}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}, "_f_expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "expression_list"}, {"type": "SYMBOL", "name": "yield"}]}, "_escape_interpolation": {"type": "IMMEDIATE_TOKEN", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "{{"}, {"type": "STRING", "value": "}}"}]}}, "escape_sequence": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "\\"}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "u[a-fA-F\\d]{4}"}, {"type": "PATTERN", "value": "U[a-fA-F\\d]{8}"}, {"type": "PATTERN", "value": "x[a-fA-F\\d]{2}"}, {"type": "PATTERN", "value": "\\d{3}"}, {"type": "PATTERN", "value": "\\r?\\n"}, {"type": "PATTERN", "value": "['\"abfrntv\\\\]"}]}]}}}, "_not_escape_sequence": {"type": "IMMEDIATE_TOKEN", "content": {"type": "STRING", "value": "\\"}}, "format_specifier": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "PATTERN", "value": "[^{}\\n]+"}}}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "interpolation"}, "named": true, "value": "format_expression"}]}}]}, "type_conversion": {"type": "PATTERN", "value": "![a-z]"}, "integer": {"type": "TOKEN", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0x"}, {"type": "STRING", "value": "0X"}]}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "_?[A-Fa-f0-9]+"}}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[Ll]"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0o"}, {"type": "STRING", "value": "0O"}]}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "_?[0-7]+"}}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[Ll]"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0b"}, {"type": "STRING", "value": "0B"}]}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "_?[0-1]+"}}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[Ll]"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[Ll]"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[jJ]"}, {"type": "BLANK"}]}]}]}]}}, "float": {"type": "TOKEN", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "STRING", "value": "."}, {"type": "CHOICE", "members": [{"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "PATTERN", "value": "[eE][\\+-]?"}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}]}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "BLANK"}]}, {"type": "STRING", "value": "."}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "PATTERN", "value": "[eE][\\+-]?"}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}]}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}, {"type": "SEQ", "members": [{"type": "PATTERN", "value": "[eE][\\+-]?"}, {"type": "REPEAT1", "content": {"type": "PATTERN", "value": "[0-9]+_?"}}]}]}]}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[Ll]"}, {"type": "PATTERN", "value": "[jJ]"}]}, {"type": "BLANK"}]}]}}, "identifier": {"type": "PATTERN", "value": "[_\\p{XID_Start}][_\\p{XID_Continue}]*"}, "keyword_identifier": {"type": "PREC", "value": -3, "content": {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "print"}, {"type": "STRING", "value": "exec"}, {"type": "STRING", "value": "async"}, {"type": "STRING", "value": "await"}, {"type": "STRING", "value": "match"}]}, "named": true, "value": "identifier"}}, "true": {"type": "STRING", "value": "True"}, "false": {"type": "STRING", "value": "False"}, "none": {"type": "STRING", "value": "None"}, "await": {"type": "PREC", "value": 20, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "await"}, {"type": "SYMBOL", "name": "expression"}]}}, "comment": {"type": "TOKEN", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "#"}, {"type": "PATTERN", "value": ".*"}]}}, "positional_separator": {"type": "STRING", "value": "/"}, "keyword_separator": {"type": "STRING", "value": "*"}}, "extras": [{"type": "SYMBOL", "name": "comment"}, {"type": "PATTERN", "value": "[\\s\\f\\uFEFF\\u2060\\u200B]|\\\\\\r?\\n"}], "conflicts": [["primary_expression", "pattern"], ["primary_expression", "list_splat_pattern"], ["tuple", "tuple_pattern"], ["list", "list_pattern"], ["with_item", "_collection_elements"], ["named_expression", "as_pattern"], ["match_statement", "primary_expression"]], "precedences": [], "externals": [{"type": "SYMBOL", "name": "_newline"}, {"type": "SYMBOL", "name": "_indent"}, {"type": "SYMBOL", "name": "_dedent"}, {"type": "SYMBOL", "name": "_string_start"}, {"type": "SYMBOL", "name": "_string_content"}, {"type": "SYMBOL", "name": "_string_end"}, {"type": "SYMBOL", "name": "comment"}, {"type": "STRING", "value": "]"}, {"type": "STRING", "value": ")"}, {"type": "STRING", "value": "}"}], "inline": ["_simple_statement", "_compound_statement", "_suite", "_expressions", "_left_hand_side", "keyword_identifier"], "supertypes": ["_simple_statement", "_compound_statement", "expression", "primary_expression", "pattern", "parameter"]}