body {
  background-color: #f8f9fa;
  color: #212529;
}

.App {
  text-align: center;
  margin: 20px;
}

.container {
  margin-top: 20px;
}

button {
  margin-right: 10px;
}

.card {
  margin-top: 20px;
  background-color: #ffffff;
  color: #212529;
}

.card-body {
  background-color: #ffffff;
  color: #212529;
}

.mt-2 {
  margin-top: 20px;
}

.mt-4 {
  margin-top: 40px;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-info {
  background-color: #4CAF50;
  border-color: #4CAF50;
  color: #e9ecef;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.pre {
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  padding: 10px;
  color: #212529;
}

h1, h2 {
  color: #212529;
}

select.form-control {
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #ced4da;
}

select.form-control:focus {
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #ced4da;
}

body.light-theme {
  background-color: #f5f5f5;
  color: #333;
}

h1, h2, h3, h4, h5, h6, .navbar-brand, .nav-link {
  color: #007bff !important; /* Blue text for titles */
}

.navbar.light-theme {
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
}

.card.light-theme {
  background-color: #ffffff;
  color: #333;
  border: 1px solid #ddd;
}

.btn-primary.light-theme {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-light.light-theme {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline-dark.light-theme {
  color: #333;
  border-color: #333;
}

.table-light-theme th, .table-light-theme td {
  color: #333;
  border: 1px solid #ddd;
}

.table-light-theme th {
  background-color: #f1f1f1;
}

.table-light-theme tbody tr:hover {
  background-color: #e9e9e9;
}

body.light-theme {
  background-color: #ffffff;
  color: #000000;
}

body.light-theme .navbar {
  background-color: #f8f9fa;
}

body.light-theme .card {
  background-color: #ffffff;
  border-color: #ccc;
}

body.light-theme .card-title,
body.light-theme .card-text {
  color: #000000;
}
