// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "RAFT",

	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"build": {
        // Path is relative to the devcontainer.json file.
        "dockerfile": "Dockerfile"
    },

	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "sh ${containerWorkspaceFolder}/.devcontainer/raft/post-create.sh",

	// Configure tool-specific properties.
	"customizations": {
		"codespaces": {
			"openFiles": [
				"raft/README.md"
			]
		}
	}

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
