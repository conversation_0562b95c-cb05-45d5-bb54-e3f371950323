name: Validate API Zoo PR

on:
  pull_request:
    branches: 
      - main
    paths:
      - 'data/apizoo/**'

jobs:
  check-api-update:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Install jq
        run: sudo apt-get install jq
      
      - name: Check for duplicate APIs
        id: check
        run: |
          FILES=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} | grep 'data/apizoo/')
          ERROR_FLAG=0
          ISSUE_BODY=""
          for FILE in $FILES; do
            DATA=$(jq -c . < "$FILE")
            TMP_FILE=$(mktemp)
            echo "$DATA" > "$TMP_FILE"
            RESPONSE=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" --data-binary "@$TMP_FILE" https://apizooindex.gorilla-llm.com/api/validate)
            rm "$TMP_FILE"
            BODY=$(echo "$RESPONSE" | head -n -1)
            HTTP_STATUS=$(echo "$RESPONSE" | tail -n1)
            echo "Validation response for $FILE: $RESPONSE"
            if [ "$HTTP_STATUS" -ne 200 ]; then
              ERROR_FLAG=1
              ISSUE_BODY+="**Error validating new API documentation in $FILE for duplicates**\n\n- HTTP Status: $HTTP_STATUS\n- Response:\n\`\`\`\n"
              while read -r line; do
                ISSUE_BODY+="$line\n"
              done <<< "$BODY"
              ISSUE_BODY+="\`\`\`\n\n"
            fi
            if [[ "$RESPONSE" == *"\"duplicate\": true"* ]]; then
              echo "::set-output name=duplicate::true"
              break
            fi
          done
          if [ "$ERROR_FLAG" -eq 1 ]; then
            echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
            echo "$ISSUE_BODY" >> $GITHUB_ENV
            echo "EOF" >> $GITHUB_ENV
            echo "ERRORS_DETECTED=true" >> $GITHUB_ENV
          fi

      - name: Prepare Issue Content If Errors Were Detected
        if: env.ERRORS_DETECTED == 'true'
        run: |
          echo -e "name: API Zoo Index\nabout: Validation Error\n\n**Describe the issue**\nError(s) occurred while validating new API additions to the API Zoo Index.\n\n$ISSUE_BODY" > issue_content.md
          echo "::set-output name=issue_file::issue_content.md"

      - name: Create GitHub Issue on Error
        if: env.ERRORS_DETECTED == 'true'
        uses: peter-evans/create-issue-from-file@v5
        with:
          title: "[API Zoo] Error Validating New Additions To The API Zoo Index"
          content-filepath: ./issue_content.md
          labels: apizoo-index
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Comment on PR if duplicate API
        if: steps.check.outputs.duplicate == 'true'
        uses: peter-evans/create-or-update-comment@v1
        with:
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            :warning: **Duplicate API detected**: This PR should not be merged.
          token: ${{ secrets.GITHUB_TOKEN }}
