#!/usr/bin/env python3
"""Debug script for BFC evaluation via AgentEval framework."""

import os
import sys
import asyncio
import logging
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

import requests

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.engine.config import get_config
from src.adapters.bfc import BFCAdapter

logger = logging.getLogger(__name__)


def submit_bfc_task(
    api_url: str = "http://localhost:8001",
    model: str = "gpt-4o",
    test_category: str = "simple",
    num_entries: int = 5,
    **kwargs
) -> Optional[str]:
    """Submit a BFC task to the API server.
    
    Args:
        api_url: API server URL
        model: Model name
        test_category: Test category
        num_entries: Number of entries to process
        **kwargs: Additional parameters
        
    Returns:
        Task ID if successful, None otherwise
    """
    task_data = {
        "benchmark": "bfc",
        "model": model,
        "params": {
            "test_category": [test_category] if isinstance(test_category, str) else test_category,
            "num_entries": num_entries,
            "use_fc_mode": kwargs.get("use_fc_mode", False),
            "temperature": kwargs.get("temperature", 0.001),
            **kwargs
        }
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/tasks",
            json=task_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Task submitted successfully: {task_id}")
        print(f"📊 Task details: {json.dumps(result, indent=2)}")
        return task_id
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to submit task: {e}")
        return None


def check_task_status(api_url: str, task_id: str) -> Optional[Dict[str, Any]]:
    """Check task status.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        
    Returns:
        Task status if successful, None otherwise
    """
    try:
        response = requests.get(
            f"{api_url}/api/tasks/{task_id}",
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        print(f"📋 Task status: {result.get('status', 'unknown')}")
        return result
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to check task status: {e}")
        return None


def wait_for_completion(api_url: str, task_id: str, timeout: int = 600) -> bool:
    """Wait for task completion.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        timeout: Timeout in seconds
        
    Returns:
        True if completed successfully, False otherwise
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        status = check_task_status(api_url, task_id)
        if not status:
            return False
            
        task_status = status.get("status", "unknown")
        
        if task_status == "completed":
            print("✅ Task completed successfully!")
            return True
        elif task_status == "failed":
            print("❌ Task failed!")
            error = status.get("error", "Unknown error")
            print(f"Error: {error}")
            return False
        elif task_status in ["pending", "running"]:
            print(f"⏳ Task {task_status}... waiting...")
            time.sleep(10)
        else:
            print(f"❓ Unknown status: {task_status}")
            time.sleep(5)
    
    print("⏰ Timeout waiting for task completion")
    return False


async def test_adapter_directly():
    """Test the BFC adapter directly."""
    print("🧪 Testing BFC adapter directly...")

    # Load configuration from worker.yaml
    # When running from VSCode debug, cwd is set to workspaceFolder (agenteval)
    worker_config_path = "config/worker.yaml"
    if not Path(worker_config_path).exists():
        print(f"❌ Worker configuration file not found: {worker_config_path}")
        return False

    config = get_config(worker_config_path)

    # Initialize adapter
    adapter = BFCAdapter(config.get("benchmarks.bfc", {}))

    # Test parameters for FC mode
    fc_params = {
        "model": "Qwen/Qwen3-8B",
        "model_provider": "local",
        "test_category": ["simple"],
        "temperature": 0.1,
        "use_fc_mode": True,  # Test new FC mode parameter
        "num_threads": 1,
        "include_input_log": True,
        "num_entries": 2  # Small number for testing
    }

    try:
        print("📝 Testing FC mode execution...")
        print(f"   Params: {json.dumps(fc_params, indent=2)}")
        result = await adapter.execute(fc_params)
        print(f"✅ FC mode execution successful!")

        # Create a JSON-serializable version of the result
        serializable_result = {}
        for key, value in result.items():
            try:
                json.dumps(value)  # Test if value is serializable
                serializable_result[key] = value
            except (TypeError, ValueError):
                serializable_result[key] = str(value)  # Convert to string if not serializable

        print(f"📊 Result: {json.dumps(serializable_result, indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Direct adapter test failed: {e}")
        logger.exception("Direct adapter test failed")
        return False


async def test_inference_backend_integration():
    """Test BFC integration with inference backends."""
    print("\n🔧 Testing inference backend integration...")

    try:
        # Mock inference backend for testing
        class MockInferenceBackend:
            def __init__(self):
                self.name = "mock"
                self.api_key = "test-key"
                self.base_url = "http://localhost:8000"
                self.models = ["gpt-4", "claude-3-sonnet"]

            def completion(self, **kwargs):
                print(f"   🤖 Mock backend called with model: {kwargs.get('model', 'unknown')}")
                print(f"   📝 Messages: {len(kwargs.get('messages', []))}")
                print(f"   🔧 Tools: {len(kwargs.get('tools', []))}")

                # Mock response structure
                class MockChoice:
                    def __init__(self):
                        self.message = MockMessage()

                class MockMessage:
                    def __init__(self):
                        self.content = "Mock response"
                        self.tool_calls = None

                class MockUsage:
                    def __init__(self):
                        self.prompt_tokens = 10
                        self.completion_tokens = 5

                class MockResponse:
                    def __init__(self):
                        self.choices = [MockChoice()]
                        self.usage = MockUsage()

                return MockResponse()

            def supports_model(self, model: str) -> bool:
                return model in self.models

        # Test handler creation with inference backend
        import sys
        from pathlib import Path

        # Add BFC path
        bfc_path = str(Path("./external/gorilla/berkeley-function-call-leaderboard").resolve())
        if bfc_path not in sys.path:
            sys.path.insert(0, bfc_path)

        from bfcl_eval._llm_response_generation import build_handler

        mock_backend = MockInferenceBackend()

        # Test FC mode handler
        print("   🔧 Creating FC mode handler...")
        fc_handler = build_handler(
            model_name="gpt-4",
            temperature=0.1,
            inference_backend=mock_backend,
            use_fc_mode=True
        )
        print(f"   ✅ FC handler created: {type(fc_handler).__name__}")
        print(f"   📊 FC mode: {fc_handler.is_fc_model}")

        # Test prompting mode handler
        print("   🔧 Creating prompting mode handler...")
        prompting_handler = build_handler(
            model_name="gpt-4",
            temperature=0.1,
            inference_backend=mock_backend,
            use_fc_mode=False
        )
        print(f"   ✅ Prompting handler created: {type(prompting_handler).__name__}")
        print(f"   📊 FC mode: {prompting_handler.is_fc_model}")

        print("✅ Inference backend integration test successful!")
        return True

    except Exception as e:
        print(f"❌ Inference backend integration test failed: {e}")
        logger.exception("Inference backend integration test failed")
        return False


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Debug BFC evaluation")
    parser.add_argument("--api-url", default="http://localhost:8001", help="API server URL")
    parser.add_argument("--model", default="gpt-4", help="Model name")
    parser.add_argument("--test-category", default="simple", help="Test category")
    parser.add_argument("--num-entries", type=int, default=5, help="Number of entries")
    parser.add_argument("--use-fc-mode", action="store_true", help="Use function calling mode")
    parser.add_argument("--temperature", type=float, default=0.001, help="Temperature for inference")
    parser.add_argument("--test-direct", action="store_true", help="Test adapter directly")
    parser.add_argument("--test-backend", action="store_true", help="Test inference backend integration")
    parser.add_argument("--submit-only", action="store_true", help="Submit task only, don't wait")
    parser.add_argument("--timeout", type=int, default=600, help="Timeout for waiting")
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Starting BFC debug session...")
    
    if args.test_direct:
        print("🧪 Running direct adapter test...")
        success = asyncio.run(test_adapter_directly())
        if not success:
            sys.exit(1)
        return

    if args.test_backend:
        print("🔧 Running inference backend integration test...")
        success = asyncio.run(test_inference_backend_integration())
        if not success:
            sys.exit(1)
        return
    
    # Submit task via API
    print("📤 Submitting BFC task via API...")
    task_id = submit_bfc_task(
        api_url=args.api_url,
        model=args.model,
        test_category=args.test_category,
        num_entries=args.num_entries,
        use_fc_mode=args.use_fc_mode,
        temperature=args.temperature
    )
    
    if not task_id:
        print("❌ Failed to submit task")
        sys.exit(1)
    
    if args.submit_only:
        print(f"✅ Task submitted: {task_id}")
        return
    
    # Wait for completion
    print("⏳ Waiting for task completion...")
    success = wait_for_completion(args.api_url, task_id, args.timeout)
    
    if success:
        print("🎉 Debug session completed successfully!")
    else:
        print("❌ Debug session failed or timed out")
        sys.exit(1)


if __name__ == "__main__":
    main()
