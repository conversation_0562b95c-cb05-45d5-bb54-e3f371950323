# AgentEval 调试完全指南

> 🎯 **一站式调试解决方案** - 从环境配置到高级调试技巧的完整指南

## 📖 目录

- [快速开始](#快速开始)
- [环境配置](#环境配置)
- [VSCode调试配置](#vscode调试配置)
- [调试脚本使用](#调试脚本使用)
- [测试与调试](#测试与调试)
- [高级调试技巧](#高级调试技巧)
- [常见问题解决](#常见问题解决)
- [最佳实践](#最佳实践)

---

## 🚀 快速开始

### 30秒快速调试

```bash
# 1. 环境检查
python debug/scripts/setup_debug.py

# 2. 开始调试
code .  # 打开VSCode
# 按 Ctrl+Shift+D → 选择 "Debug Tau-Bench Script (Direct Test)" → 按 F5
```

### 调试配置概览

| 配置名称 | 用途 | 启动时间 | 适用场景 |
|---------|------|----------|----------|
| **Debug Tau-Bench Script (Direct Test)** | 直接测试tau-bench adapter | 1-2分钟 | 调试adapter逻辑问题 |
| **Debug BFC Script (Direct Test)** | 直接测试bfc adapter | 1-2分钟 | 调试BFC adapter逻辑 |
| **Debug Tau-Bench via AgentEval** | 通过worker调试tau-bench | 3-5分钟 | 调试worker处理流程 |
| **Debug BFC via AgentEval** | 通过worker调试bfc | 3-5分钟 | 调试worker处理流程 |
| **Debug API Server** | 调试API服务器 | 2-3分钟 | 调试API接口问题 |

---

## ⚙️ 环境配置

### 系统要求

- Python 3.8+
- VSCode + Python扩展
- 已配置的API密钥

### 环境变量设置

```bash
# 方法1：创建 .env 文件
cat > .env << EOF
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here
MISTRAL_API_KEY=your_mistral_key_here
EOF

# 方法2：临时设置
export OPENAI_API_KEY="your_key_here"
```

### 依赖安装

```bash
# 安装基础依赖
pip install -r requirements.txt

# 初始化外部基准测试
git submodule update --init --recursive

# 安装基准测试依赖
cd external/tau-bench && pip install -e . && cd ../..
cd external/berkeley-function-call-leaderboard && pip install -e . && cd ../..
```

### 环境验证

```bash
# 一键检查所有环境
python debug/scripts/setup_debug.py

# 手动验证关键组件
python -c "from src.adapters.tau_bench import TauBenchAdapter; print('✅ Tau-Bench OK')"
python -c "from src.adapters.bfc import BFCAdapter; print('✅ BFC OK')"

# 验证BFC inference backend集成
python debug/scripts/debug_bfc.py --test-backend
```

---

## 🔧 VSCode调试配置

### 文件结构

```
agenteval/
├── .vscode/
│   ├── launch.json              # VSCode调试配置
│   └── tasks.json               # VSCode任务配置
├── debug/
│   ├── scripts/
│   │   ├── debug_tau_bench.py   # Tau-Bench调试脚本
│   │   ├── debug_bfc.py         # BFC调试脚本
│   │   ├── debug_gaia.py        # GAIA调试脚本
│   │   └── setup_debug.py       # 环境检查脚本
│   └── vscode/
│       ├── launch.json          # 调试配置备份
│       └── tasks.json           # 任务配置备份
```

### 调试配置详解

#### 1. Direct Test 配置（推荐新手）
- **优点**：启动快，直接测试adapter逻辑
- **适用**：调试基本功能、参数传递问题
- **启动时间**：1-2分钟

#### 2. Via AgentEval 配置（中级）
- **优点**：测试完整worker流程
- **适用**：调试任务队列、worker处理逻辑
- **启动时间**：3-5分钟

#### 3. API Test 配置（高级）
- **优点**：端到端测试完整API流程
- **适用**：调试网络问题、API集成问题
- **启动时间**：5-10分钟

### 快捷键参考

| 操作 | 快捷键 | 说明 |
|------|--------|------|
| 开始调试 | `F5` | 开始或继续执行 |
| 单步执行 | `F10` | 执行下一行（不进入函数） |
| 进入函数 | `F11` | 进入函数内部 |
| 跳出函数 | `Shift+F11` | 跳出当前函数 |
| 停止调试 | `Shift+F5` | 停止调试会话 |
| 重启调试 | `Ctrl+Shift+F5` | 重新开始调试 |
| 切换断点 | `F9` | 在当前行设置/取消断点 |
| Debug Console | `Ctrl+Shift+Y` | 打开调试控制台 |

---

## 🧪 调试脚本使用

### debug_tau_bench.py

```bash
# 最简单：直接测试adapter
python debug/scripts/debug_tau_bench.py --test-direct

# 完整测试：通过API
python debug/scripts/debug_tau_bench.py --model gpt-4o --env retail --end-index 2

# 完整参数示例
python debug/scripts/debug_tau_bench.py \
  --api-url http://localhost:8000 \
  --model gpt-4o \
  --env retail \
  --agent-strategy tool-calling \
  --num-trials 1 \
  --task-split test \
  --start-index 0 \
  --end-index 2 \
  --timeout 600
```

### debug_bfc.py

```bash
# 最简单：直接测试adapter
python debug/scripts/debug_bfc.py --test-direct

# 完整测试：通过API
python debug/scripts/debug_bfc.py --model gpt-4o --test-category simple --num-entries 3

# 完整参数示例
python debug/scripts/debug_bfc.py \
  --api-url http://localhost:8001 \
  --model gpt-4o \
  --test-category simple \
  --num-entries 5 \
  --timeout 600
```

### debug_gaia.py

```bash
# 最简单：直接测试adapter
python debug/scripts/debug_gaia.py --test-direct

# 完整测试：通过API（单个难度级别）
python debug/scripts/debug_gaia.py --model gpt-4o --level 1 --num-tasks 2

# 完整测试：通过API（所有难度级别）
python debug/scripts/debug_gaia.py --model gpt-4o --level-all --num-tasks 5

# 完整参数示例
python debug/scripts/debug_gaia.py \
  --api-url http://localhost:8000 \
  --model gpt-4o \
  --level 1 \
  --num-tasks 3 \
  --max-steps 10 \
  --timeout 300 \
  --temperature 0.0 \
  --max-tokens 4096 \
  --wait-timeout 1200
```

---

## 🧪 调试技巧

### 基础调试方法

使用VSCode内置的调试功能来调试AgentEval项目：

1. **设置断点** - 在代码行号左侧点击设置断点
2. **启动调试** - 使用F5或点击调试按钮
3. **查看变量** - 在调试面板查看变量值
4. **单步执行** - 使用F10/F11进行单步调试

---

## 🛠️ 高级调试技巧

### 条件断点

**何时使用**：只想在特定条件下暂停时

**设置步骤**：
1. **右键点击断点** 🔴
2. **选择 "Edit Breakpoint..."**
3. **选择 "Expression"**
4. **输入条件**，例如：
   ```python
   params.get('model') == 'gpt-4o'
   len(self.tasks) > 5
   result.get('status') == 'failed'
   ```

### 日志断点

**何时使用**：想要记录信息但不中断执行时

**设置步骤**：
1. **右键点击断点** 🔴
2. **选择 "Edit Breakpoint..."**
3. **选择 "Log Message"**
4. **输入日志消息**，例如：
   ```
   Processing task with model: {model}, env: {env}
   Task result: {result}
   ```
5. **勾选 "Continue execution"**

### 监视变量

**设置步骤**：
1. **在Debug面板找到 "Watch" 区域**
2. **点击 "+" 按钮**
3. **输入要监视的表达式**：
   ```python
   params['model']
   len(self.tasks)
   result.get('status')
   self.config.get('timeout', 300)
   ```

### Debug Console使用

**打开方式**：按 `Ctrl+Shift+Y`

**常用命令**：
```python
# 查看变量
params
self.config
result

# 执行代码
len(params)
params.keys()
type(self._tau_bench_run)

# 调用方法
self.validate_params(params)
```

### 性能调试

```bash
# 性能分析
python -m cProfile -o profile.stats debug/scripts/debug_tau_bench.py --test-direct

# 内存监控
python -m memory_profiler debug/scripts/debug_tau_bench.py --test-direct

# 查看性能报告
python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(10)"
```

---

## 🚨 常见问题解决

### Python解释器找不到

**症状**：VSCode提示找不到Python解释器

**快速解决**：
```bash
# 1. 找到正确路径
which python

# 2. 修改 .vscode/launch.json 中的 "python" 字段
# 将路径更新为实际的Python路径
```

### 环境变量未设置

**症状**：API调用失败，提示API密钥错误

**快速解决**：
```bash
# 临时设置
export OPENAI_API_KEY="your_key_here"

# 或创建 .env 文件
echo "OPENAI_API_KEY=your_key_here" > .env

# 验证设置
echo $OPENAI_API_KEY
```

### 端口被占用

**症状**：启动API服务器时提示端口被占用

**快速解决**：
```bash
# 查找并终止占用进程
lsof -i :8000
kill -9 <PID>

# 或使用不同端口
python api_server.py --port 8001
```

### 外部依赖缺失

**症状**：导入tau-bench或BFC模块失败

**快速解决**：
```bash
# 初始化子模块
git submodule update --init --recursive

# 安装依赖
cd external/tau-bench && pip install -e . && cd ../..
cd external/berkeley-function-call-leaderboard && pip install -e . && cd ../..

# 验证安装
python -c "import tau_bench; print('✅ Tau-Bench OK')"
```

### 断点不生效

**症状**：设置断点后程序不会暂停

**快速解决**：
1. 确保断点是红色实心圆点 🔴
2. 检查 `"justMyCode": false` 在launch.json中
3. 重新设置断点
4. 确认代码路径正确

### 模块导入失败

**症状**：运行时出现导入错误

**快速解决**：
```bash
# 检查Python路径
python -c "import sys; print(sys.path)"

# 确保src目录在路径中
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# 验证模块导入
python -c "from src.engine.config import get_config; print('✅ Config OK')"
```

### 快速诊断

```bash
# 一键检查所有问题
python debug/scripts/setup_debug.py

# 手动检查关键组件
python -c "from src.engine.config import get_config; print('✅ Config OK')"
python -c "from src.adapters.tau_bench import TauBenchAdapter; print('✅ Adapter OK')"
python -c "from src.api.routes import app; print('✅ API OK')"
```

---

## 📋 最佳实践

### ✅ 推荐做法

1. **从简单开始**
   - 先使用Direct Test配置
   - 逐步增加复杂度

2. **合理设置断点**
   - 先设置少量断点
   - 使用条件断点避免过度中断
   - 定期清理无用断点

3. **充分利用工具**
   - 使用Debug Console查看变量
   - 使用Watch监视关键变量
   - 使用日志断点记录执行流程

4. **保存有用配置**
   - 保存常用的断点配置
   - 记录有效的调试参数组合

5. **结合日志调试**
   - 查看日志文件了解执行流程
   - 使用日志断点记录关键信息

### ❌ 避免做法

1. **不要过度设置断点**
   - 避免在循环中设置无条件断点
   - 不要一次设置太多断点

2. **不要忽略错误信息**
   - 仔细阅读错误堆栈
   - 不要跳过环境检查步骤

3. **不要在生产环境使用debug配置**
   - Debug配置包含开发用的参数
   - 生产环境应使用优化配置

4. **不要忽略性能影响**
   - Debug模式会影响性能
   - 大量断点会显著降低执行速度

---

## 🔧 BFC调试专题

### BFC与AgentEval集成调试

Berkeley Function Call Leaderboard (BFC) 已经集成了AgentEval的inference backends，支持以下新功能：

#### 1. Inference Backend集成测试

```bash
# 测试inference backend集成
python debug/scripts/debug_bfc.py --test-backend

# 测试FC模式
python debug/scripts/debug_bfc.py --test-direct --use-fc-mode

# 测试prompting模式
python debug/scripts/debug_bfc.py --test-direct
```

#### 2. 新参数说明

- `--use-fc-mode`: 启用函数调用模式
- `--temperature`: 设置推理温度
- `--test-backend`: 测试inference backend集成

#### 3. 调试要点

**FC vs Prompting模式**：
- FC模式：使用函数调用API
- Prompting模式：使用文本提示

**Backend选择**：
- 自动根据模型名称选择合适的backend
- 支持手动指定backend
- 向后兼容原有handlers

**常见问题**：
```bash
# 检查BFC路径
ls external/gorilla/berkeley-function-call-leaderboard/

# 验证handler创建
python -c "
import sys
sys.path.insert(0, './external/gorilla/berkeley-function-call-leaderboard')
from bfcl_eval._llm_response_generation import build_handler
print('✅ Handler import OK')
"

# 测试参数验证
python debug/scripts/debug_bfc.py --test-direct --use-fc-mode
```

---

## 🎓 学习路径

### 第1天：基础入门
1. ✅ 运行 `python debug/scripts/setup_debug.py` 检查环境
2. ✅ 尝试 "Debug Tau-Bench Script (Direct Test)"
3. ✅ 尝试 "Debug BFC Script (Direct Test)"
4. ✅ 尝试 "Debug GAIA Script (Direct Test)"
5. ✅ 学会设置断点和查看变量

### 第2天：深入理解
1. ✅ 学会设置和使用条件断点
2. ✅ 尝试 "Debug Tau-Bench via AgentEval"
3. ✅ 尝试 "Debug GAIA via AgentEval"
4. ✅ 测试BFC inference backend集成: `python debug/scripts/debug_bfc.py --test-backend`
5. ✅ 学会使用Debug Console

### 第3天：完整流程
1. ✅ 尝试 "Debug Tau-Bench Script (API Test)"
2. ✅ 尝试 "Debug GAIA Script (API Test)"
3. ✅ 学会调试完整的API流程
4. ✅ 掌握日志断点和监视变量

### 第4天：API调试
1. ✅ 学会调试API服务器
2. ✅ 使用断点调试API路由
3. ✅ 理解请求响应流程

### 第5天：高级技巧
1. ✅ 掌握性能调试
2. ✅ 学会自定义调试配置
3. ✅ 能够独立解决常见问题

---

## 🆘 紧急求助

### 如果所有方法都不工作：

1. **重置环境**：
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall

   # 重新初始化子模块
   git submodule update --init --recursive --force
   ```

2. **检查基础环境**：
   ```bash
   python debug/scripts/setup_debug.py
   ```

3. **手动测试**：
   ```bash
   # 测试基本功能
   python -c "from src.engine.config import get_config; print('✅ Config OK')"
   python -c "from src.adapters.tau_bench import TauBenchAdapter; print('✅ Adapter OK')"
   ```

4. **查看详细日志**：
   ```bash
   tail -f logs/*.log
   ```

---

## 🎉 成功标志

当您能够：
- ✅ 成功运行任何一个debug配置
- ✅ 在断点处暂停并查看变量
- ✅ 使用Debug Console执行代码
- ✅ 调试实际的tau-bench或bfc问题
- ✅ 调试API和工作器流程

**恭喜！您已经掌握了AgentEval的调试技能！**

---

## 📚 相关文档

- [API文档](docs/api_documentation.md) - API接口详细说明
- [系统架构](docs/system_architecture.md) - 系统整体架构
- [模块详情](docs/module_details.md) - 各模块详细说明
- [主README](README.md) - 项目总体介绍

---

**祝您调试愉快！** 🚀

> 💡 **提示**：调试是一个迭代过程，多练习会让您更加熟练。从简单开始，逐步深入，您很快就能成为调试专家！
