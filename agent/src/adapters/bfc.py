"""Berkeley Function Calling Leaderboard-v3 adapter for the Agent Evaluation Tool."""

import sys
import asyncio
from typing import Dict, Any, List
import logging
from pathlib import Path

from src.adapters.base import BaseAdapter
from src.benchmark_config.bfc import BFCConfig

logger = logging.getLogger(__name__)

class BFCAdapter(BaseAdapter):
    """Adapter for Berkeley Function Calling Leaderboard-v3 benchmark."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the BFC adapter.

        Args:
            config: Adapter configuration.
        """
        super().__init__(config)
        self.repo_path = Path(config.get("repo_path", "./external/gorilla/berkeley-function-call-leaderboard"))
        self._bfcl_generation = None
        self._bfcl_evaluation = None
        self._bfcl_constants = None
        self._initialize_bfc()

    def _initialize_bfc(self) -> None:
        """Initialize BFC by importing its modules."""
        try:
            # Add BFC repo to Python path
            bfc_path = str(self.repo_path.resolve())
            if bfc_path not in sys.path:
                sys.path.insert(0, bfc_path)

            # Check if the repo exists
            if not self.repo_path.exists():
                logger.error(f"BFC repository not found at {self.repo_path}")
                return

            # Import BFC modules
            from bfcl_eval._llm_response_generation import main as generation_main
            from bfcl_eval.eval_checker.eval_runner import main as evaluation_main
            from bfcl_eval.constants.category_mapping import TEST_COLLECTION_MAPPING
            from bfcl_eval.constants.model_config import MODEL_CONFIG_MAPPING

            self._bfcl_generation = generation_main
            self._bfcl_evaluation = evaluation_main
            self._bfcl_constants = {
                'TEST_COLLECTION_MAPPING': TEST_COLLECTION_MAPPING,
                'MODEL_CONFIG_MAPPING': MODEL_CONFIG_MAPPING
            }

            logger.info(f"Successfully initialized BFC from {self.repo_path}")

        except ImportError as e:
            logger.warning(f"Failed to initialize BFC due to missing dependencies: {e}")
            logger.warning("BFC functionality will be limited. Please install BFC dependencies.")
        except Exception as e:
            logger.error(f"Failed to initialize BFC: {e}")
            raise

    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a BFC evaluation.

        Args:
            params: Evaluation parameters.

        Returns:
            Evaluation results.
        """
        # Validate parameters
        params = await self.validate_params(params)

        try:
            if not self._bfcl_generation or not self._bfcl_evaluation:
                raise RuntimeError("BFC not properly initialized. Please check dependencies and repository path.")

            # Get inference backend from worker config
            from src.inference_backends.manager import backend_manager
            inference_backend = None

            # Try to get inference backend for the model
            model_name = params["model"]
            for backend_name, backend in backend_manager.backends.items():
                if backend.supports_model(model_name):
                    inference_backend = backend
                    logger.info(f"Using inference backend '{backend_name}' for model '{model_name}'")
                    break

            if not inference_backend:
                logger.warning(f"No inference backend found for model '{model_name}', using BFC's native handlers")

            # Use project-specific result directories
            # Create directories if they don't exist
            result_dir = Path("results/bfc/trajectory").resolve()
            score_dir = Path("results/bfc/score").resolve()
            result_dir.mkdir(parents=True, exist_ok=True)
            score_dir.mkdir(parents=True, exist_ok=True)

            # Create BFC configuration from validated parameters
            # Prepare config parameters, overriding specific values as needed
            config_params = params.copy()
            config_params["result_dir"] = str(result_dir)  # Override with absolute path
            config_params["allow_overwrite"] = True
            config_params["inference_backend"] = inference_backend

            bfc_config = BFCConfig(**config_params)

            # Convert to SimpleNamespace for BFC compatibility
            generation_args = bfc_config.to_namespace()

            # Run generation using the new function that supports inference backends
            from bfcl_eval._llm_response_generation import main_with_backend
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, main_with_backend, generation_args, inference_backend, params.get("use_fc_mode", False)
            )

            # Run evaluation using the new function that supports inference backends
            from bfcl_eval.eval_checker.eval_runner import main_with_backend as eval_main_with_backend
            await loop.run_in_executor(
                None,
                eval_main_with_backend,
                [params["model"]],  # model names
                params.get("test_category", ["all"]),  # test categories
                str(result_dir),  # result_dir (absolute path, BFC now supports this)
                str(score_dir),   # score_dir (absolute path, BFC now supports this)
                inference_backend,  # inference_backend
                params.get("use_fc_mode", False)  # use_fc_mode
            )

            # Collect results from BFC's standard output directories
            results = self._collect_bfc_results(result_dir, score_dir, params)

            return {
                "benchmark": "bfc",
                "model": params["model"],
                "test_categories": params.get("test_category", ["all"]),
                "results": results,
                "result_dir": str(result_dir),  # Include result directory for user reference
                "score_dir": str(score_dir)     # Include score directory for user reference
            }

        except Exception as e:
            logger.error(f"Error executing BFC: {e}")
            raise

    def _load_json_files(self, directory: Path, pattern: str = "*.json") -> Dict[str, Any]:
        """Load JSON files from a directory.

        Args:
            directory: Directory to search.
            pattern: File pattern to match.

        Returns:
            Dictionary mapping file stems to loaded JSON data.
        """
        import json
        results = {}

        if directory.exists():
            for file_path in directory.glob(pattern):
                try:
                    with open(file_path, 'r') as f:
                        results[file_path.stem] = json.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load JSON file {file_path}: {e}")

        return results

    def _load_csv_files(self, directory: Path, pattern: str = "*.csv") -> Dict[str, List[Dict[str, Any]]]:
        """Load CSV files from a directory.

        Args:
            directory: Directory to search.
            pattern: File pattern to match.

        Returns:
            Dictionary mapping file stems to loaded CSV data.
        """
        import csv
        results = {}

        if directory.exists():
            for file_path in directory.glob(pattern):
                try:
                    with open(file_path, 'r', newline='') as f:
                        reader = csv.DictReader(f)
                        results[file_path.stem] = list(reader)
                except Exception as e:
                    logger.warning(f"Failed to load CSV file {file_path}: {e}")

        return results

    def _extract_summary_metrics(self, score_dir: Path, model_name: str) -> Dict[str, float]:
        """Extract summary metrics from overall scores CSV.

        Args:
            score_dir: Directory containing score files.
            model_name: Name of the model to extract metrics for.

        Returns:
            Dictionary of summary metrics.
        """
        import csv

        overall_score_file = score_dir / "data_overall.csv"
        if not overall_score_file.exists():
            return {}

        try:
            with open(overall_score_file, 'r', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get("Model") == model_name:
                        return {
                            "overall_accuracy": float(row.get("Overall Acc", 0)),
                            "non_live_ast_accuracy": float(row.get("Non-Live AST Acc", 0)),
                            "non_live_exec_accuracy": float(row.get("Non-Live Exec Acc", 0)),
                            "live_accuracy": float(row.get("Live Acc", 0)),
                            "multi_turn_accuracy": float(row.get("Multi Turn Acc", 0)),
                            "relevance_detection": float(row.get("Relevance Detection", 0)),
                            "irrelevance_detection": float(row.get("Irrelevance Detection", 0))
                        }
        except Exception as e:
            logger.warning(f"Failed to extract summary metrics: {e}")

        return {}

    def _collect_bfc_results(self, result_dir: Path, score_dir: Path, params: Dict[str, Any]) -> Dict[str, Any]:
        """Collect BFC evaluation results from BFC's standard output directories.

        BFC saves results to:
        - result_dir/model_name/*_result.json (generation results)
        - score_dir/*.csv (evaluation scores)
        """
        results = {
            "generation_results": {},
            "evaluation_scores": {},
            "summary": {}
        }

        try:
            # Collect generation results from BFC's standard model directory
            model_result_dir = result_dir / params["model"].replace("/", "_")
            results["generation_results"] = self._load_json_files(model_result_dir)

            # Collect evaluation scores from BFC's standard score directory
            results["evaluation_scores"] = self._load_csv_files(score_dir)

            # Extract summary metrics
            results["summary"] = self._extract_summary_metrics(score_dir, params["model"])

        except Exception as e:
            logger.warning(f"Error collecting BFC results: {e}")

        return results

    def _get_valid_categories(self) -> set:
        """Get set of valid test categories.

        Returns:
            Set of valid category names.
        """
        if not self._bfcl_constants:
            return set()

        test_collection_mapping = self._bfcl_constants.get('TEST_COLLECTION_MAPPING', {})
        valid_categories = set(test_collection_mapping.keys())

        # Add individual test names
        for test_names in test_collection_mapping.values():
            valid_categories.update(test_names)
        valid_categories.add("all")

        return valid_categories

    def _validate_model(self, model_name: str) -> None:
        """Validate model name against BFC model config.

        Args:
            model_name: Name of the model to validate.
        """
        if not self._bfcl_constants:
            return

        model_config_mapping = self._bfcl_constants.get('MODEL_CONFIG_MAPPING', {})
        if model_name not in model_config_mapping:
            logger.warning(f"Model {model_name} not found in BFC model config. Proceeding anyway.")

    def _validate_test_categories(self, categories: List[str]) -> None:
        """Validate test categories against available ones.

        Args:
            categories: List of test categories to validate.

        Raises:
            ValueError: If any category is invalid.
        """
        valid_categories = self._get_valid_categories()
        if not valid_categories:
            return  # Skip validation if BFC not properly initialized

        for category in categories:
            if category not in valid_categories:
                raise ValueError(f"Invalid test category: {category}. Valid categories: {sorted(valid_categories)}")



    async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize BFC evaluation parameters using BFCConfig.

        Args:
            params: Evaluation parameters.

        Returns:
            Validated and normalized parameters.

        Raises:
            ValueError: If parameters are invalid.
        """
        try:
            # Set default result_dir if not provided
            config_params = params.copy()
            config_params["result_dir"] = config_params.get("result_dir", "results/bfc")

            # Create BFCConfig - this handles all validation, normalization, and defaults
            bfc_config = BFCConfig(**config_params)

            # Additional runtime validations that require BFC initialization
            self._validate_model(bfc_config.model)
            if bfc_config.test_category:
                self._validate_test_categories(bfc_config.test_category)

            # Return the validated parameters as a dictionary (excluding runtime-only fields)
            validated_params = bfc_config.model_dump()
            validated_params.pop("inference_backend", None)
            return validated_params

        except Exception as e:
            raise ValueError(f"Parameter validation failed: {str(e)}")
