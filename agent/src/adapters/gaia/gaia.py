"""GAIA adapter for the Agent Evaluation Tool."""

import json
import asyncio
import aiohttp
import yaml
from typing import Dict, Any, List
import logging
from pathlib import Path

from src.adapters.base import BaseAdapter
from src.benchmark_config.gaia import GAIAConfig
from .scorer import question_scorer

logger = logging.getLogger(__name__)

class GAIAAdapter(BaseAdapter):
    """GAIA adapter that acts as a client to send queries to the service."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the GAIA adapter.

        Args:
            config: Adapter configuration.
        """
        super().__init__(config)
        self.service_url = config.get("service_url", "http://localhost:8000")
        self.max_concurrent_requests = config.get("max_concurrent_requests", 4)
        self.request_timeout = config.get("request_timeout", 600)  # 10 minutes
        self.tasks = []
        self.worker_config = self._load_worker_config()
        self._initialize_gaia()

    def _load_worker_config(self) -> Dict[str, Any]:
        """Load worker configuration from worker.yaml file.

        Returns:
            Dictionary containing worker configuration.
        """
        try:
            worker_config_path = Path("agent/config/worker.yaml")
            if not worker_config_path.exists():
                logger.warning(f"Worker config file not found at {worker_config_path}")
                return {}

            with open(worker_config_path, 'r', encoding='utf-8') as f:
                worker_config = yaml.safe_load(f)

            return worker_config
        except Exception as e:
            logger.error(f"Error loading worker config: {e}")
            return {}

    def _get_llm_config_for_model(self, model_name: str) -> Dict[str, Any]:
        """Get LLM configuration for a specific model name.

        Args:
            model_name: Name of the model to find configuration for

        Returns:
            Dictionary containing LLM configuration for the model
        """
        llm_config = self.worker_config.get("llm", {})
        endpoints = llm_config.get("endpoints", [])

        if not endpoints:
            logger.warning("No LLM endpoints found in worker.yaml")
            return {}

        # Find endpoint that supports the requested model
        for endpoint in endpoints:
            models = endpoint.get("models", [])
            if model_name in models:
                return {
                    "model_platform": endpoint.get("name", "openai"),
                    "model_type": model_name,
                    "api_key": endpoint.get("api_key"),
                    "url": endpoint.get("url"),
                    "model_config_dict": {"temperature": 0},
                    "timeout": llm_config.get("default_timeout", 60)
                }

        # If model not found in any endpoint, try to match by endpoint name
        for endpoint in endpoints:
            endpoint_name = endpoint.get("name", "").lower()
            if (model_name.lower().startswith("gpt") and endpoint_name == "openai") or \
               (model_name.lower().startswith("claude") and endpoint_name == "anthropic") or \
               (endpoint_name in model_name.lower()):
                # Use the first model from this endpoint as fallback
                fallback_model = endpoint.get("models", [model_name])[0] if endpoint.get("models") else model_name
                return {
                    "model_platform": endpoint.get("name", "openai"),
                    "model_type": fallback_model,
                    "api_key": endpoint.get("api_key"),
                    "url": endpoint.get("url"),
                    "model_config_dict": {"temperature": 0},
                    "timeout": llm_config.get("default_timeout", 60)
                }

        # Fallback to first endpoint if no match found
        if endpoints:
            default_endpoint = endpoints[0]
            logger.warning(f"Model {model_name} not found in worker.yaml, using default endpoint: {default_endpoint.get('name')}")
            return {
                "model_platform": default_endpoint.get("name", "openai"),
                "model_type": model_name,  # Use the requested model name
                "api_key": default_endpoint.get("api_key"),
                "url": default_endpoint.get("url"),
                "model_config_dict": {"temperature": 0},
                "timeout": llm_config.get("default_timeout", 60)
            }

        return {}

    def _initialize_gaia(self) -> None:
        """Initialize GAIA by loading task definitions from external directory."""
        try:
            self.tasks = []

            # Load from external GAIA directory structure
            external_gaia_path = Path("./external/gaia/2023")

            if not external_gaia_path.exists():
                logger.error(f"External GAIA directory not found at {external_gaia_path}")
                return

            # Load validation data
            validation_metadata = external_gaia_path / "validation" / "metadata.jsonl"
            if validation_metadata.exists():
                validation_count = self._load_metadata_file(validation_metadata, "validation")
                logger.info(f"Loaded {validation_count} validation tasks")

            # Load test data
            test_metadata = external_gaia_path / "test" / "metadata.jsonl"
            if test_metadata.exists():
                test_count = self._load_metadata_file(test_metadata, "test")
                logger.info(f"Loaded {test_count} test tasks")

            logger.info(f"Total loaded {len(self.tasks)} GAIA tasks from external directory")

            if len(self.tasks) == 0:
                logger.error("No GAIA tasks loaded. Please check external data availability.")

        except Exception as e:
            logger.error(f"Failed to initialize GAIA: {e}")

    def _load_metadata_file(self, metadata_file: Path, split: str) -> int:
        """Load tasks from a metadata.jsonl file.

        Args:
            metadata_file: Path to the metadata.jsonl file
            split: Split name (validation or test)

        Returns:
            Number of tasks loaded
        """
        count = 0
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        item = json.loads(line)
                        task = {
                            "task_id": item.get("task_id", ""),
                            "question": item.get("Question", ""),
                            "level": item.get("Level", 1),
                            "final_answer": item.get("Final answer", ""),
                            "file_name": item.get("file_name", ""),
                            "file_path": self._get_file_path(item.get("file_name", ""), split),
                            "annotator_metadata": item.get("Annotator Metadata", {}),
                            "split": split
                        }
                        self.tasks.append(task)
                        count += 1
        except Exception as e:
            logger.error(f"Error loading metadata file {metadata_file}: {e}")

        return count

    def _get_file_path(self, file_name: str, split: str) -> str:
        """Get the full file path for a GAIA task file.

        Args:
            file_name: Name of the file
            split: Split name (validation or test)

        Returns:
            Full path to the file
        """
        if not file_name:
            return ""

        if split == "validation":
            return str(Path("./external/gaia/2023/validation") / file_name)
        elif split == "test":
            return str(Path("./external/gaia/2023/test") / file_name)
        else:
            return ""

    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a GAIA evaluation by sending queries to the service.

        Args:
            params: Evaluation parameters.

        Returns:
            Evaluation results.
        """
        # Validate parameters
        params = await self.validate_params(params)

        try:
            if not self.tasks:
                raise RuntimeError("GAIA tasks not loaded. Please check dataset availability.")

            # Filter tasks based on level if specified
            tasks_to_run = self.tasks
            level = params.get("level")
            if level is not None and level != "all":
                tasks_to_run = [task for task in self.tasks if task.get("level") == level]

            # Limit number of tasks if specified
            if params.get("num_tasks"):
                tasks_to_run = tasks_to_run[:params["num_tasks"]]

            logger.info(f"Starting GAIA evaluation with {len(tasks_to_run)} tasks")

            # Send queries to service and collect results
            results = await self._send_queries_to_service(tasks_to_run, params)

            # Process and aggregate results
            return self._process_execution_results(results, params)

        except Exception as e:
            logger.error(f"Error executing GAIA: {e}")
            raise

    async def _send_queries_to_service(self, tasks: List[Dict[str, Any]], params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Send GAIA queries to the service and collect results.

        Args:
            tasks: List of GAIA tasks to process.
            params: Evaluation parameters.

        Returns:
            List of task results.
        """
        # Use max_concurrent_requests from params if provided, otherwise use default
        max_concurrent = params.get("max_concurrent_requests", self.max_concurrent_requests)

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        # Create tasks for async processing
        async_tasks = []
        for task in tasks:
            async_task = self._send_single_query(semaphore, task, params)
            async_tasks.append(async_task)

        # Execute all tasks concurrently and collect results
        logger.info(f"Sending {len(async_tasks)} queries to service with max concurrency {max_concurrent}")
        results = await asyncio.gather(*async_tasks, return_exceptions=True)

        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task {i} failed: {result}")
                # Create a failed result
                task = tasks[i]
                processed_results.append({
                    "task_id": task.get("task_id", ""),
                    "question": task.get("question", ""),
                    "level": task.get("level", 1),
                    "predicted_answer": "",
                    "ground_truth": task.get("final_answer", ""),
                    "score": 0.0,
                    "is_correct": False,
                    "error": str(result),
                    "trajectory": [],
                    "metrics": {}
                })
            else:
                processed_results.append(result)

        return processed_results

    async def _send_single_query(self, semaphore: asyncio.Semaphore, task: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """Send a single GAIA query to the service.

        Args:
            semaphore: Semaphore to limit concurrent requests.
            task: GAIA task to process.
            params: Evaluation parameters.

        Returns:
            Task result.
        """
        async with semaphore:
            task_id = task.get("task_id", "")
            logger.info(f"Sending query for task {task_id}")

            try:
                # Get LLM configuration for the requested model
                model_name = params["model"]
                llm_config = self._get_llm_config_for_model(model_name)

                # Prepare request payload for the service
                request_payload = {
                    "benchmark": "gaia",  # Use existing gaia benchmark
                    "model": model_name,
                    "params": {
                        "query": task.get("question", ""),
                        "task_id": task_id,
                        "level": task.get("level", 1),
                        "file_name": task.get("file_name", ""),
                        "file_path": task.get("file_path", ""),
                        "annotator_metadata": task.get("annotator_metadata", {}),
                        "final_answer": task.get("final_answer", ""),  # Send ground truth for evaluation
                        "max_steps": params.get("max_steps", 15),
                        "timeout": params.get("timeout", 600)
                    },
                    "llm_config": llm_config  # Send LLM configuration to server
                }

                # Send request to service
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
                    async with session.post(f"{self.service_url}/tasks", json=request_payload) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            raise Exception(f"Service request failed with status {response.status}: {error_text}")

                        task_response = await response.json()
                        service_task_id = task_response["task_id"]

                    # Poll for task completion
                    result = await self._poll_task_completion(session, service_task_id)

                    # Extract results from service response
                    # Assume service already calculated the score and correctness
                    service_result = result.get("result", {})
                    predicted_answer = service_result.get("final_answer", "")
                    ground_truth = task.get("final_answer", "")

                    # Get score from service result, or calculate if not provided
                    if "score" in service_result:
                        score = float(service_result["score"])
                        is_correct = score > 0.5  # Assume score > 0.5 means correct
                    elif "is_correct" in service_result:
                        is_correct = bool(service_result["is_correct"])
                        score = 1.0 if is_correct else 0.0
                    else:
                        # Fallback: calculate score using local scorer
                        is_correct = question_scorer(predicted_answer, ground_truth)
                        score = 1.0 if is_correct else 0.0

                    # Return formatted result
                    return {
                        "task_id": task_id,
                        "question": task.get("question", ""),
                        "level": task.get("level", 1),
                        "predicted_answer": predicted_answer,
                        "ground_truth": ground_truth,
                        "score": score,
                        "is_correct": is_correct,
                        "trajectory": result.get("result", {}).get("trajectory", []),
                        "metrics": result.get("result", {}).get("metrics", {}),
                        "service_task_id": service_task_id
                    }

            except Exception as e:
                logger.error(f"Error processing task {task_id}: {e}")
                return {
                    "task_id": task_id,
                    "question": task.get("question", ""),
                    "level": task.get("level", 1),
                    "predicted_answer": "",
                    "ground_truth": task.get("final_answer", ""),
                    "score": 0.0,
                    "is_correct": False,
                    "error": str(e),
                    "trajectory": [],
                    "metrics": {}
                }

    async def _poll_task_completion(self, session: aiohttp.ClientSession, task_id: str) -> Dict[str, Any]:
        """Poll the service for task completion.

        Args:
            session: HTTP session.
            task_id: Service task ID.

        Returns:
            Task result.
        """
        poll_interval = 5  # seconds
        max_polls = self.request_timeout // poll_interval

        for _ in range(max_polls):
            try:
                async with session.get(f"{self.service_url}/tasks/{task_id}") as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Failed to get task status: {error_text}")

                    task_result = await response.json()
                    status = task_result.get("status")

                    if status == "completed":
                        logger.info(f"Task {task_id} completed successfully")
                        return task_result
                    elif status == "failed":
                        error = task_result.get("error", "Unknown error")
                        raise Exception(f"Task failed: {error}")
                    elif status == "cancelled":
                        raise Exception("Task was cancelled")

                    # Task is still running, wait and poll again
                    await asyncio.sleep(poll_interval)

            except Exception as e:
                if "Task failed:" in str(e) or "Task was cancelled" in str(e):
                    raise
                logger.warning(f"Error polling task {task_id}: {e}")
                await asyncio.sleep(poll_interval)

        raise Exception(f"Task {task_id} timed out after {self.request_timeout} seconds")

    def _process_execution_results(self, results: List[Dict[str, Any]], params: Dict[str, Any]) -> Dict[str, Any]:
        """Process GAIA execution results and calculate metrics.

        Args:
            results: Raw results from GAIA execution.
            params: Execution parameters.

        Returns:
            Processed results with metrics.
        """
        total_tasks = len(results)
        if total_tasks == 0:
            return {
                "benchmark": "gaia",
                "model": params["model"],
                "total_tasks": 0,
                "accuracy": 0.0,
                "level_breakdown": {},
                "results": [],
                "config": params
            }

        # Calculate overall metrics
        total_score = sum(r["score"] for r in results)
        accuracy = total_score / total_tasks

        # Calculate level-wise breakdown
        level_breakdown = {}
        for result in results:
            level = result["level"]
            if level not in level_breakdown:
                level_breakdown[level] = {"total": 0, "correct": 0, "accuracy": 0.0}

            level_breakdown[level]["total"] += 1
            if result["is_correct"]:
                level_breakdown[level]["correct"] += 1

        # Calculate accuracy for each level
        for level_data in level_breakdown.values():
            level_data["accuracy"] = level_data["correct"] / level_data["total"] if level_data["total"] > 0 else 0.0

        logger.info(f"GAIA evaluation completed: {sum(r['score'] for r in results)}/{total_tasks} correct (accuracy: {accuracy:.2%})")

        return {
            "benchmark": "gaia",
            "model": params["model"],
            "total_tasks": total_tasks,
            "accuracy": accuracy,
            "level_breakdown": level_breakdown,
            "results": results,
            "config": params,
            "summary": {
                "correct": int(total_score),
                "total": total_tasks,
                "accuracy": accuracy
            }
        }

    async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize GAIA evaluation parameters using GAIAConfig.

        Args:
            params: Evaluation parameters.

        Returns:
            Validated and normalized parameters.

        Raises:
            ValueError: If parameters are invalid.
        """
        try:
            # Create GAIAConfig - this handles all validation, normalization, and defaults
            gaia_config = GAIAConfig(**params)

            # Return the validated parameters as a dictionary
            validated_params = gaia_config.model_dump()
            return validated_params

        except Exception as e:
            raise ValueError(f"Parameter validation failed: {str(e)}")
