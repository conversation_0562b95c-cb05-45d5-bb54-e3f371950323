"""Inference backend manager."""

import logging
from typing import Dict, Any, List, Optional

from .base import InferenceBackend
from .openai_backend import OpenAIBackend
from .anthropic_backend import AnthropicBackend
from .local_backend import LocalBackend
from .google_backend import GoogleBackend

logger = logging.getLogger(__name__)


class InferenceBackendManager:
    """Manager for inference backends."""
    
    def __init__(self):
        self.backends: Dict[str, InferenceBackend] = {}
        self._backend_classes = {
            "openai": OpenAIBackend,
            "anthropic": AnthropicBackend,
            "local": LocalBackend,
            "google": GoogleBackend,
        }
        
    def register_backend(self, name: str, config: Dict[str, Any]) -> InferenceBackend:
        """Register a new inference backend.
        
        Args:
            name: Backend name.
            config: Backend configuration.
            
        Returns:
            Created backend instance.
        """
        backend_class = self._backend_classes.get(name.lower())
        if not backend_class:
            # Fallback to local backend for unknown providers
            logger.warning(f"Unknown backend '{name}', using LocalBackend")
            backend_class = LocalBackend
            
        backend = backend_class(config)
        self.backends[name] = backend
        logger.debug(f"Registered backend: {name}")
        return backend
    
    def get_backend(self, name: str) -> Optional[InferenceBackend]:
        """Get a registered backend by name.
        
        Args:
            name: Backend name.
            
        Returns:
            Backend instance or None if not found.
        """
        return self.backends.get(name)
    
    def load_backends_from_config(self, llm_endpoints: List[Dict[str, Any]]) -> None:
        """Load backends from LLM endpoints configuration.
        
        Args:
            llm_endpoints: List of LLM endpoint configurations.
        """
        self.backends.clear()
        
        for endpoint in llm_endpoints:
            name = endpoint.get("name", "").lower()
            if name:
                self.register_backend(name, endpoint)
                
        logger.info(f"Loaded {len(self.backends)} inference backends")
    
    def list_backends(self) -> List[str]:
        """List all registered backend names.
        
        Returns:
            List of backend names.
        """
        return list(self.backends.keys())


# Global backend manager instance
backend_manager = InferenceBackendManager()
