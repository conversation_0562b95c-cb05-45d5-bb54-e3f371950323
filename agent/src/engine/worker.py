"""Worker implementation for the Agent Evaluation Tool."""

import asyncio
import logging
import time
import traceback
from typing import Dict, Any, Optional, Set
from collections import defaultdict

from src.engine.queue import get_task_queue, TaskStatus
from src.adapters.base import BaseAdapter
from src.storage.db import get_storage

logger = logging.getLogger(__name__)

class Worker:
    """Worker for processing evaluation tasks."""

    def __init__(self, worker_id: str, adapters: Dict[str, BaseAdapter],
                 max_concurrent_tasks: int = 5, timeout_seconds: int = 3600,
                 model_concurrency: Optional[Dict[str, int]] = None,
                 config=None):
        """Initialize a worker.

        Args:
            worker_id: Unique worker ID.
            adapters: Dictionary mapping task types to adapters.
            max_concurrent_tasks: Maximum number of concurrent tasks.
            timeout_seconds: Task timeout in seconds.
            model_concurrency: Dictionary mapping model names to maximum concurrent tasks per model.
                               If None, no per-model limits are applied.
            config: Configuration object for storage initialization.
        """
        self.worker_id = worker_id
        self.adapters = adapters
        self.max_concurrent_tasks = max_concurrent_tasks
        self.timeout_seconds = timeout_seconds
        self.task_queue = get_task_queue()
        self.running = False
        self.tasks: Dict[str, asyncio.Task] = {}

        # Model concurrency limits
        self.model_concurrency = model_concurrency or {}

        # Track active tasks per model
        self.active_model_tasks: Dict[str, Set[str]] = defaultdict(set)

        # Storage for saving evaluation results
        self.storage = get_storage(config)

        # Statistics
        self.stats = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "tasks_cancelled": 0,
            "total_execution_time": 0.0,
            "model_stats": defaultdict(lambda: {
                "tasks_completed": 0,
                "tasks_failed": 0,
                "avg_execution_time": 0.0,
                "total_execution_time": 0.0
            })
        }

    async def start(self) -> None:
        """Start the worker."""
        if self.running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return

        self.running = True
        logger.info(f"Worker {self.worker_id} started")

        try:
            await self._process_tasks()
        except Exception as e:
            logger.error(f"Worker {self.worker_id} encountered an error: {e}")
            self.running = False

    async def stop(self) -> None:
        """Stop the worker."""
        if not self.running:
            logger.warning(f"Worker {self.worker_id} is not running")
            return

        self.running = False
        logger.info(f"Worker {self.worker_id} stopping...")

        # Cancel all running tasks
        for task_id, task in self.tasks.items():
            if not task.done():
                task.cancel()
                await self.task_queue.update_task_status(
                    task_id, TaskStatus.CANCELLED, error="Worker stopped"
                )

        logger.info(f"Worker {self.worker_id} stopped")

    async def _process_tasks(self) -> None:
        """Process tasks from the queue."""
        while self.running:
            # Check if we can process more tasks
            if len(self.tasks) >= self.max_concurrent_tasks:
                # Wait for a task to complete
                done, _ = await asyncio.wait(
                    [task for task in self.tasks.values() if not task.done()],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Clean up completed tasks
                for task in done:
                    for task_id, t in self.tasks.items():
                        if t == task:
                            # Remove from active model tasks
                            self._remove_from_active_model_tasks(task_id)
                            # Remove from tasks
                            del self.tasks[task_id]
                            break

            # Get a new task from the queue
            task_id = await self.task_queue.dequeue()
            if not task_id:
                # If no task is available, wait a bit to avoid busy-waiting
                await asyncio.sleep(0.1)
                continue

            # Get the task details
            task = await self.task_queue.get_task(task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                continue

            # Check if the task type is supported
            if task.task_type not in self.adapters:
                await self.task_queue.update_task_status(
                    task_id, TaskStatus.FAILED,
                    error=f"Unsupported task type: {task.task_type}"
                )
                continue

            # Check model-specific concurrency limits
            model = task.params.get("model", "default")
            if model in self.model_concurrency:
                max_model_tasks = self.model_concurrency[model]
                current_model_tasks = len(self.active_model_tasks[model])

                if current_model_tasks >= max_model_tasks:
                    logger.debug(f"Model {model} at concurrency limit ({current_model_tasks}/{max_model_tasks}), re-queueing task {task_id}")
                    # Re-queue the task for later processing
                    await self.task_queue.queue.put(task_id)
                    continue

            # Update task status to running
            await self.task_queue.update_task_status(task_id, TaskStatus.RUNNING)

            # Add to active model tasks
            self._add_to_active_model_tasks(task_id, model)

            # Process the task
            self.tasks[task_id] = asyncio.create_task(
                self._execute_task(task_id, task.task_type, task.params)
            )

    def _add_to_active_model_tasks(self, task_id: str, model: str) -> None:
        """Add a task to the active model tasks.

        Args:
            task_id: Task ID.
            model: Model name.
        """
        self.active_model_tasks[model].add(task_id)
        logger.debug(f"Added task {task_id} to active model tasks for {model} (now {len(self.active_model_tasks[model])})")

    def _remove_from_active_model_tasks(self, task_id: str) -> None:
        """Remove a task from the active model tasks.

        Args:
            task_id: Task ID.
        """
        for model, tasks in self.active_model_tasks.items():
            if task_id in tasks:
                tasks.remove(task_id)
                logger.debug(f"Removed task {task_id} from active model tasks for {model} (now {len(tasks)})")
                break

    async def _execute_task(self, task_id: str, task_type: str, params: Dict[str, Any]) -> None:
        """Execute a task.

        Args:
            task_id: Task ID.
            task_type: Task type.
            params: Task parameters.
        """
        model = params.get("model", "default")
        start_time = time.time()
        logger.info(f"Executing task {task_id} ({task_type}) with model {model}")

        try:
            # Get the adapter for the task type
            adapter = self.adapters[task_type]

            # Create a cancellable task wrapper
            async def cancellable_execute():
                # Create the main execution task
                execute_task = asyncio.create_task(adapter.execute(params))

                # Create a task status checker
                async def check_cancellation():
                    while not execute_task.done():
                        await asyncio.sleep(1)  # Check every second
                        task = await self.task_queue.get_task(task_id)
                        if task and task.status == TaskStatus.CANCELLED:
                            execute_task.cancel()
                            raise asyncio.CancelledError("Task was cancelled externally")

                checker_task = asyncio.create_task(check_cancellation())

                try:
                    # Wait for either execution to complete or cancellation
                    result = await execute_task
                    checker_task.cancel()
                    return result
                except asyncio.CancelledError:
                    checker_task.cancel()
                    raise
                finally:
                    if not checker_task.done():
                        checker_task.cancel()
                        try:
                            await checker_task
                        except asyncio.CancelledError:
                            pass

            # Execute the task with timeout and cancellation support
            result = await asyncio.wait_for(
                cancellable_execute(),
                timeout=self.timeout_seconds
            )

            # Calculate execution time
            execution_time = time.time() - start_time

            # Update statistics
            self.stats["tasks_completed"] += 1
            self.stats["total_execution_time"] += execution_time

            # Update model-specific statistics
            model_stats = self.stats["model_stats"][model]
            model_stats["tasks_completed"] += 1
            model_stats["total_execution_time"] += execution_time

            # Calculate average execution time
            if model_stats["tasks_completed"] > 0:
                model_stats["avg_execution_time"] = (
                    model_stats["total_execution_time"] / model_stats["tasks_completed"]
                )

            # Add execution time to result
            if isinstance(result, dict):
                result["execution_time"] = execution_time

            # Save result to persistent storage
            try:
                if isinstance(result, dict):
                    # Add task metadata to result for storage
                    storage_result = result.copy()
                    storage_result["task_id"] = task_id
                    storage_result["worker_id"] = self.worker_id

                    # Save to storage system
                    result_id = await self.storage.save_result(storage_result)
                    logger.info(f"Task {task_id} result saved to storage with ID: {result_id}")
                else:
                    logger.warning(f"Task {task_id} result is not a dict, skipping storage save")
            except Exception as e:
                logger.error(f"Failed to save task {task_id} result to storage: {e}")
                # Continue execution - don't fail the task just because storage failed

            # Update task status to completed
            await self.task_queue.update_task_status(
                task_id, TaskStatus.COMPLETED, result=result
            )

            logger.info(f"Task {task_id} completed successfully in {execution_time:.2f}s")

        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            logger.error(f"Task {task_id} timed out after {self.timeout_seconds} seconds")

            # Update statistics
            self.stats["tasks_failed"] += 1
            self.stats["model_stats"][model]["tasks_failed"] += 1

            await self.task_queue.update_task_status(
                task_id, TaskStatus.FAILED,
                error=f"Task timed out after {self.timeout_seconds} seconds"
            )

        except asyncio.CancelledError:
            execution_time = time.time() - start_time
            logger.warning(f"Task {task_id} was cancelled after {execution_time:.2f}s")

            # Update statistics
            self.stats["tasks_cancelled"] += 1

            await self.task_queue.update_task_status(
                task_id, TaskStatus.CANCELLED,
                error="Task was cancelled"
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Task {task_id} failed after {execution_time:.2f}s: {e}")
            logger.error(traceback.format_exc())

            # Update statistics
            self.stats["tasks_failed"] += 1
            self.stats["model_stats"][model]["tasks_failed"] += 1

            # Create a detailed error message
            error_message = f"{type(e).__name__}: {str(e)}"

            await self.task_queue.update_task_status(
                task_id, TaskStatus.FAILED,
                error=error_message
            )




