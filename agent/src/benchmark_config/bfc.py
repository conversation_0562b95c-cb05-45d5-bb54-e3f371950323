"""
Berkeley Function Calling Leaderboard benchmark configuration.

This module provides the configuration class for the BFC benchmark adapter.
"""

from typing import Optional, List, Any
from pathlib import Path
from pydantic import Field, field_validator, model_validator
from .base import BaseBenchmarkConfig


class BFCConfig(BaseBenchmarkConfig):
    """Configuration for Berkeley Function Calling Leaderboard benchmark."""
    test_category: List[str] = Field(default=["all"], description="Test categories to run")
    temperature: float = Field(default=0.0, ge=0.0, le=2.0, description="Model temperature")
    include_input_log: bool = Field(default=False, description="Include input log")
    exclude_state_log: bool = Field(default=False, description="Exclude state log")
    num_threads: int = Field(default=1, ge=1, description="Number of threads")
    skip_server_setup: bool = Field(default=False, description="Skip server setup")
    result_dir: str = Field(description="Results directory")
    allow_overwrite: bool = Field(default=True, description="Allow overwriting results")
    run_ids: bool = Field(default=False, description="Run with IDs")
    use_fc_mode: bool = Field(default=False, description="Use function calling mode")

    @field_validator('test_category', mode='before')
    @classmethod
    def normalize_test_category(cls, v: Any) -> List[str]:
        """Normalize test categories to list format."""
        if v is None:
            return ["all"]
        elif isinstance(v, str):
            return [v]
        elif isinstance(v, list):
            return v
        else:
            return ["all"]

    @model_validator(mode='after')
    def validate_test_categories_against_available(self) -> 'BFCConfig':
        """Validate test categories against available ones from BFC."""
        # This validation will be done in the adapter since it requires BFC initialization
        # We keep the basic structure here for consistency
        return self

    @property
    def model_list(self) -> List[str]:
        """Get model as a list format required by BFC.

        Returns:
            List containing the model name.
        """
        return [self.model]

    def to_namespace(self):
        """Convert to SimpleNamespace for BFC compatibility if needed.

        Returns:
            SimpleNamespace object with BFC-compatible format.
        """
        from types import SimpleNamespace
        config_dict = self.model_dump()
        config_dict["model"] = self.model_list  # BFC expects model as list
        return SimpleNamespace(**config_dict)
