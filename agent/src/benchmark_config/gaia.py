"""
GAIA benchmark configuration.

This module provides configuration classes for the GAIA benchmark,
ensuring type safety and parameter validation.
"""

from pydantic import Field, field_validator
from typing import Optional, Union
from .base import BaseBenchmarkConfig


class GAIAConfig(BaseBenchmarkConfig):
    """Configuration for GAIA benchmark."""
    max_steps: int = Field(default=10, ge=1, description="Maximum number of steps")
    timeout: int = Field(default=300, ge=1, description="Timeout in seconds")
    temperature: float = Field(default=0.0, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: int = Field(default=4096, ge=1, description="Maximum tokens")
    result_dir: str = Field(default="results/gaia", description="Results directory")
    level: Optional[Union[int, str]] = Field(default=None, description="GAIA difficulty level (1-3 or 'all')")
    num_tasks: Optional[int] = Field(default=None, ge=1, description="Number of tasks to run")
    service_url: str = Field(default="http://localhost:8000", description="Service URL for GAIA processing")
    max_concurrent_requests: int = Field(default=4, ge=1, description="Maximum concurrent requests to service")
    request_timeout: int = Field(default=600, ge=1, description="Request timeout in seconds")

    @field_validator('level')
    @classmethod
    def validate_level(cls, v) -> Union[int, str, None]:
        """Validate level parameter."""
        if v is None:
            return v
        if isinstance(v, str):
            if v == "all":
                return "all"
            else:
                raise ValueError("Level string must be exactly 'all' (case-sensitive)")
        if isinstance(v, int):
            if 1 <= v <= 3:
                return v
            else:
                raise ValueError("Level integer must be between 1 and 3")
        raise ValueError("Level must be an integer (1-3), 'all', or None")
