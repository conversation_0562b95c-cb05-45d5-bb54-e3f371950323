---
openapi: 3.0.1
info:
  version: v0
  title: Open AI Klarna product Api
  description: Search and compare prices from thousands of online shops. Only available in the US.
servers:
- url: https://www.klarna.com/us/shopping
tags:
- name: open-ai-product-endpoint
  description: Open AI Product Endpoint. Query for products.
paths:
  "/public/openai/v0/products":
    get:
      tags:
      - open-ai-product-endpoint
      summary: API for fetching Klarna product information
      operationId: productsUsingGET
      parameters:
      - name: q
        in: query
        description: A precise query that matches one very small category or product
          that needs to be searched for to find the products the user is looking for.
          If the user explicitly stated what they want, use that as a query. The query
          is as specific as possible to the product name or category mentioned by
          the user in its singular form, and don't contain any clarifiers like latest,
          newest, cheapest, budget, premium, expensive or similar. The query is always
          taken from the latest topic, if there is a new topic a new query is started.
        required: true
        schema:
          type: string
      - name: size
        in: query
        description: number of products returned
        required: false
        schema:
          type: integer
      - name: min_price
        in: query
        description: "(Optional) Minimum price in local currency for the product searched
          for. Either explicitly stated by the user or implicitly inferred from a
          combination of the user's request and the kind of product searched for."
        required: false
        schema:
          type: integer
      - name: max_price
        in: query
        description: "(Optional) Maximum price in local currency for the product searched
          for. Either explicitly stated by the user or implicitly inferred from a
          combination of the user's request and the kind of product searched for."
        required: false
        schema:
          type: integer
      responses:
        '200':
          description: Products found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ProductResponse"
        '503':
          description: one or more services are unavailable
      deprecated: false
components:
  schemas:
    Product:
      type: object
      properties:
        attributes:
          type: array
          items:
            type: string
        name:
          type: string
        price:
          type: string
        url:
          type: string
      title: Product
    ProductResponse:
      type: object
      properties:
        products:
          type: array
          items:
            "$ref": "#/components/schemas/Product"
      title: ProductResponse
