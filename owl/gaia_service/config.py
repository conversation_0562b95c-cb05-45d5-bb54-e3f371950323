"""
GAIA Evaluation Service Configuration

This module handles configuration management, environment variables,
and service settings for the GAIA evaluation service.
"""

import os
from typing import Optional, Dict, Any
from pathlib import Path

from pydantic import BaseSettings, Field
from dotenv import load_dotenv
from loguru import logger


class ServiceConfig(BaseSettings):
    """Configuration settings for the GAIA evaluation service."""
    
    # Service settings
    service_name: str = Field(default="GAIA Evaluation Service", env="SERVICE_NAME")
    service_version: str = Field(default="1.0.0", env="SERVICE_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # Model configuration
    model_platform: str = Field(default="openai", env="GAIA_MODEL_PLATFORM")
    coordinator_model: str = Field(default="gpt-4o", env="GAIA_COORDINATOR_MODEL")
    task_model: str = Field(default="gpt-4o", env="GAIA_TASK_MODEL")
    answerer_model: str = Field(default="gpt-4o", env="GAIA_ANSWERER_MODEL")
    model_temperature: float = Field(default=0.0, env="GAIA_TEMPERATURE")
    
    # Task processing settings
    max_concurrent_tasks: int = Field(default=5, env="MAX_CONCURRENT_TASKS")
    default_timeout_seconds: int = Field(default=1800, env="DEFAULT_TIMEOUT_SECONDS")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    max_replanning_tries: int = Field(default=2, env="MAX_REPLANNING_TRIES")
    
    # Storage settings
    temp_dir: str = Field(default="tmp/gaia_service", env="TEMP_DIR")
    data_dir: str = Field(default="data/gaia", env="DATA_DIR")
    results_dir: str = Field(default="results/gaia_service", env="RESULTS_DIR")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Fixed model configurations (loaded from environment)
    image_analysis_model_platform: str = Field(default="openai", env="IMAGE_ANALYSIS_MODEL_PLATFORM")
    image_analysis_model_type: str = Field(default="gpt-4o", env="IMAGE_ANALYSIS_MODEL_TYPE")
    audio_reasoning_model_platform: str = Field(default="openai", env="AUDIO_REASONING_MODEL_PLATFORM")
    audio_reasoning_model_type: str = Field(default="o3-mini", env="AUDIO_REASONING_MODEL_TYPE")

    # API Keys (loaded from environment)
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    azure_openai_api_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    
    # Search API keys
    google_api_key: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    google_cse_id: Optional[str] = Field(default=None, env="GOOGLE_CSE_ID")
    serpapi_api_key: Optional[str] = Field(default=None, env="SERPAPI_API_KEY")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


def load_config(env_file: Optional[str] = None) -> ServiceConfig:
    """
    Load configuration from environment variables and .env file.
    
    Args:
        env_file: Optional path to .env file
        
    Returns:
        ServiceConfig instance with loaded settings
    """
    # Load .env file if it exists
    env_path = env_file or ".env"
    if os.path.exists(env_path):
        load_dotenv(env_path)
        logger.info(f"Loaded environment variables from {env_path}")
    else:
        logger.info("No .env file found, using environment variables only")
    
    # Create and validate configuration
    config = ServiceConfig()
    
    # Create necessary directories
    for dir_path in [config.temp_dir, config.results_dir]:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # Validate required API keys based on model platform
    _validate_api_keys(config)
    
    logger.info(f"Configuration loaded: {config.service_name} v{config.service_version}")
    return config


def _validate_api_keys(config: ServiceConfig):
    """Validate that required API keys are available."""
    required_keys = []
    
    if config.model_platform == "openai":
        if not config.openai_api_key:
            required_keys.append("OPENAI_API_KEY")
    elif config.model_platform == "anthropic":
        if not config.anthropic_api_key:
            required_keys.append("ANTHROPIC_API_KEY")
    elif config.model_platform == "azure":
        if not config.azure_openai_api_key:
            required_keys.append("AZURE_OPENAI_API_KEY")
        if not config.azure_openai_endpoint:
            required_keys.append("AZURE_OPENAI_ENDPOINT")
    
    if required_keys:
        logger.warning(f"Missing required API keys: {', '.join(required_keys)}")
        logger.warning("Service may not function properly without these keys")


def setup_logging(config: ServiceConfig):
    """
    Setup logging configuration.
    
    Args:
        config: Service configuration
    """
    # Remove default logger
    logger.remove()
    
    # Add console logger
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=config.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
               "<level>{message}</level>",
        colorize=True
    )
    
    # Add file logger if specified
    if config.log_file:
        log_dir = Path(config.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            sink=config.log_file,
            level=config.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="7 days",
            compression="zip"
        )
        logger.info(f"File logging enabled: {config.log_file}")


def get_model_config(config: ServiceConfig) -> Dict[str, Any]:
    """
    Get model configuration dictionary.
    
    Args:
        config: Service configuration
        
    Returns:
        Dictionary with model configuration
    """
    return {
        "model_platform": config.model_platform,
        "coordinator_model": config.coordinator_model,
        "task_model": config.task_model,
        "answerer_model": config.answerer_model,
        "temperature": config.model_temperature
    }


def get_processing_config(config: ServiceConfig) -> Dict[str, Any]:
    """
    Get task processing configuration dictionary.
    
    Args:
        config: Service configuration
        
    Returns:
        Dictionary with processing configuration
    """
    return {
        "max_concurrent_tasks": config.max_concurrent_tasks,
        "default_timeout_seconds": config.default_timeout_seconds,
        "max_retries": config.max_retries,
        "max_replanning_tries": config.max_replanning_tries,
        "temp_dir": config.temp_dir,
        "data_dir": config.data_dir,
        "results_dir": config.results_dir
    }


# Global configuration instance
_config: Optional[ServiceConfig] = None


def get_config() -> ServiceConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = load_config()
    return _config


def set_config(config: ServiceConfig):
    """Set the global configuration instance."""
    global _config
    _config = config
