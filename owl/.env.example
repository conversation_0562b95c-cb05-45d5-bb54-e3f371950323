# GAIA Evaluation Service Configuration Example
# Copy this file to .env and fill in your actual values

# Model Configuration
GAIA_MODEL_PLATFORM=openai
GAIA_COORDINATOR_MODEL=gpt-4o
GAIA_TASK_MODEL=gpt-4o
GAIA_ANSWERER_MODEL=gpt-4o
GAIA_TEMPERATURE=0.0

# API Keys (fill in your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here

# Search API Keys (optional)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
SERPAPI_API_KEY=your_serpapi_api_key_here

# Service Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# Task Processing
MAX_CONCURRENT_TASKS=5
DEFAULT_TIMEOUT_SECONDS=1800
MAX_RETRIES=3
MAX_REPLANNING_TRIES=2

# Directories
TEMP_DIR=tmp/gaia_service
DATA_DIR=data/gaia
RESULTS_DIR=results/gaia_service
