"""
GAIA Evaluation Service Example Usage

This example demonstrates how to use the GAIA evaluation service
to submit queries and retrieve results with execution traces.
"""

import asyncio
import json
import time
from typing import Dict, Any

import httpx
from loguru import logger

# Service configuration
SERVICE_URL = "http://localhost:8000"
TIMEOUT = 30.0


class GAIAServiceClient:
    """Client for interacting with the GAIA evaluation service."""
    
    def __init__(self, base_url: str = SERVICE_URL):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL of the GAIA service
        """
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
    
    async def submit_evaluation(self, query: Dict[str, Any]) -> str:
        """
        Submit a GAIA query for evaluation.
        
        Args:
            query: Dictionary containing the GAIA query
            
        Returns:
            task_id: Unique identifier for tracking the evaluation
        """
        url = f"{self.base_url}/evaluate"
        
        response = await self.client.post(url, json=query)
        response.raise_for_status()
        
        result = response.json()
        return result["task_id"]
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of an evaluation task.
        
        Args:
            task_id: The task identifier
            
        Returns:
            Dictionary containing task status information
        """
        url = f"{self.base_url}/status/{task_id}"
        
        response = await self.client.get(url)
        response.raise_for_status()
        
        return response.json()
    
    async def get_evaluation_result(self, task_id: str) -> Dict[str, Any]:
        """
        Get the complete evaluation result.
        
        Args:
            task_id: The task identifier
            
        Returns:
            Dictionary containing evaluation result and execution trace
        """
        url = f"{self.base_url}/result/{task_id}"
        
        response = await self.client.get(url)
        response.raise_for_status()
        
        return response.json()
    
    async def wait_for_completion(self, task_id: str, poll_interval: float = 5.0, max_wait: float = 1800.0) -> Dict[str, Any]:
        """
        Wait for a task to complete and return the result.
        
        Args:
            task_id: The task identifier
            poll_interval: How often to check status (seconds)
            max_wait: Maximum time to wait (seconds)
            
        Returns:
            Dictionary containing the final result
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status = await self.get_task_status(task_id)
            
            if status["status"] == "completed":
                return await self.get_evaluation_result(task_id)
            elif status["status"] == "failed":
                raise Exception(f"Task {task_id} failed")
            
            logger.info(f"Task {task_id} status: {status['status']} ({status.get('progress_percentage', 0):.1f}%)")
            await asyncio.sleep(poll_interval)
        
        raise TimeoutError(f"Task {task_id} did not complete within {max_wait} seconds")
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def example_simple_evaluation():
    """Example of a simple GAIA evaluation."""
    logger.info("Running simple GAIA evaluation example...")
    
    client = GAIAServiceClient()
    
    try:
        # Define a simple GAIA query
        query = {
            "query": {
                "question": "What is the capital of France?",
                "level": 1,
                "final_answer": "Paris",
                "file_attachments": [],
                "additional_info": "This is a simple geography question."
            },
            "max_tries": 2,
            "max_replanning_tries": 1,
            "timeout_seconds": 300
        }
        
        # Submit the evaluation
        logger.info("Submitting evaluation...")
        task_id = await client.submit_evaluation(query)
        logger.info(f"Task submitted with ID: {task_id}")
        
        # Wait for completion
        logger.info("Waiting for completion...")
        result = await client.wait_for_completion(task_id, poll_interval=2.0)
        
        # Display results
        logger.success("Evaluation completed!")
        print_evaluation_result(result)
        
    except Exception as e:
        logger.error(f"Error during evaluation: {e}")
    finally:
        await client.close()


async def example_complex_evaluation():
    """Example of a more complex GAIA evaluation."""
    logger.info("Running complex GAIA evaluation example...")
    
    client = GAIAServiceClient()
    
    try:
        # Define a more complex GAIA query
        query = {
            "query": {
                "question": "How many studio albums were published by Mercedes Sosa between 2000 and 2009 (included)? You can use the latest 2022 version of english wikipedia.",
                "level": 1,
                "final_answer": "3",
                "file_attachments": [],
                "additional_info": "This requires web search and information extraction from Wikipedia."
            },
            "max_tries": 3,
            "max_replanning_tries": 2,
            "timeout_seconds": 600
        }
        
        # Submit the evaluation
        logger.info("Submitting complex evaluation...")
        task_id = await client.submit_evaluation(query)
        logger.info(f"Task submitted with ID: {task_id}")
        
        # Monitor progress
        logger.info("Monitoring progress...")
        result = await client.wait_for_completion(task_id, poll_interval=5.0)
        
        # Display results
        logger.success("Complex evaluation completed!")
        print_evaluation_result(result)
        print_execution_trace(result.get("execution_trace"))
        
    except Exception as e:
        logger.error(f"Error during complex evaluation: {e}")
    finally:
        await client.close()


async def example_batch_evaluations():
    """Example of running multiple evaluations concurrently."""
    logger.info("Running batch evaluations example...")
    
    client = GAIAServiceClient()
    
    try:
        # Define multiple queries
        queries = [
            {
                "query": {
                    "question": "What is 2 + 2?",
                    "level": 1,
                    "final_answer": "4",
                    "additional_info": "Simple arithmetic"
                },
                "max_tries": 1
            },
            {
                "query": {
                    "question": "What is the largest planet in our solar system?",
                    "level": 1,
                    "final_answer": "Jupiter",
                    "additional_info": "Astronomy question"
                },
                "max_tries": 1
            },
            {
                "query": {
                    "question": "Who wrote 'Romeo and Juliet'?",
                    "level": 1,
                    "final_answer": "William Shakespeare",
                    "additional_info": "Literature question"
                },
                "max_tries": 1
            }
        ]
        
        # Submit all evaluations
        task_ids = []
        for i, query in enumerate(queries):
            logger.info(f"Submitting evaluation {i+1}/{len(queries)}...")
            task_id = await client.submit_evaluation(query)
            task_ids.append(task_id)
            logger.info(f"Task {i+1} submitted with ID: {task_id}")
        
        # Wait for all to complete
        logger.info("Waiting for all evaluations to complete...")
        results = []
        for i, task_id in enumerate(task_ids):
            logger.info(f"Waiting for task {i+1}/{len(task_ids)}...")
            result = await client.wait_for_completion(task_id, poll_interval=2.0)
            results.append(result)
        
        # Display summary
        logger.success("All batch evaluations completed!")
        print_batch_summary(results)
        
    except Exception as e:
        logger.error(f"Error during batch evaluations: {e}")
    finally:
        await client.close()


def print_evaluation_result(result: Dict[str, Any]):
    """Print evaluation result in a formatted way."""
    print("\n" + "="*60)
    print("EVALUATION RESULT")
    print("="*60)
    
    eval_result = result.get("evaluation_result", {})
    print(f"Task ID: {result.get('task_id')}")
    print(f"Status: {result.get('status')}")
    print(f"Correct: {eval_result.get('correct', 'N/A')}")
    print(f"Model Answer: {eval_result.get('model_answer', 'N/A')}")
    print(f"Ground Truth: {eval_result.get('ground_truth', 'N/A')}")
    print(f"Score: {eval_result.get('score', 'N/A')}")
    
    if result.get("created_at"):
        print(f"Created: {result['created_at']}")
    if result.get("completed_at"):
        print(f"Completed: {result['completed_at']}")


def print_execution_trace(trace: Dict[str, Any]):
    """Print execution trace in a formatted way."""
    if not trace:
        return
    
    print("\n" + "="*60)
    print("EXECUTION TRACE")
    print("="*60)
    
    print(f"Total Duration: {trace.get('total_duration_ms', 0)} ms")
    print(f"Total Steps: {len(trace.get('steps', []))}")
    print(f"Replanning Attempts: {trace.get('replanning_attempts', 0)}")
    print(f"Errors: {len(trace.get('error_messages', []))}")
    
    steps = trace.get("steps", [])
    if steps:
        print("\nKey Steps:")
        for step in steps[:10]:  # Show first 10 steps
            print(f"  {step['step_id']}: {step['agent_name']} - {step['action_type']}")
            if len(step['content']) > 100:
                print(f"     {step['content'][:100]}...")
            else:
                print(f"     {step['content']}")


def print_batch_summary(results: list):
    """Print summary of batch evaluation results."""
    print("\n" + "="*60)
    print("BATCH EVALUATION SUMMARY")
    print("="*60)
    
    total = len(results)
    correct = sum(1 for r in results if r.get("evaluation_result", {}).get("correct", False))
    
    print(f"Total Evaluations: {total}")
    print(f"Correct: {correct}")
    print(f"Incorrect: {total - correct}")
    print(f"Accuracy: {correct/total*100:.1f}%" if total > 0 else "N/A")
    
    print("\nIndividual Results:")
    for i, result in enumerate(results):
        eval_result = result.get("evaluation_result", {})
        status = "✓" if eval_result.get("correct") else "✗"
        print(f"  {i+1}: {status} {eval_result.get('model_answer', 'N/A')} (expected: {eval_result.get('ground_truth', 'N/A')})")


async def main():
    """Main function to run examples."""
    logger.info("GAIA Service Client Examples")
    logger.info("="*40)
    
    try:
        # Run examples
        await example_simple_evaluation()
        await asyncio.sleep(2)
        
        await example_complex_evaluation()
        await asyncio.sleep(2)
        
        await example_batch_evaluations()
        
    except Exception as e:
        logger.error(f"Example failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
