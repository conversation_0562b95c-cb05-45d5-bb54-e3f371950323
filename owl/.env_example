# To use these environment variables:
# 1. Populate the .env file with your API keys.
# 2. Include the following code snippet in your Python script:
#    from dotenv import load_dotenv
#    import os
#
#    load_dotenv()  # Load environment variables from .env file

#===========================================
# Models API
#===========================================

# OpenAI API (https://platform.openai.com/signup)
OPENAI_API_KEY="Fill your API key here"

# Anthropic API (https://www.anthropic.com/)
ANTHROPIC_API_KEY="Fill your API key here"

# Hugging Face API (https://huggingface.co/join)
HF_TOKEN="Fill your API key here"

# Azure OpenAI API (https://azure.microsoft.com/products/cognitive-services/openai-service/)
AZURE_OPENAI_API_KEY="Fill your API key here"
AZURE_API_VERSION="Fill your API Version here"
AZURE_DEPLOYMENT_NAME="Fill your Deployment Name here"
AZURE_OPENAI_BASE_URL="Fill your Base URL here"

#===========================================
# Tools & Services API
#===========================================

# Google Search API (https://developers.google.com/custom-search/v1/overview)
GOOGLE_API_KEY="Fill your API key here"
SEARCH_ENGINE_ID="Fill your Search Engine ID here"


# Firecrawl API (https://www.firecrawl.dev/)
FIRECRAWL_API_KEY="Fill your API key here"

# Chunkr API (https://chunkr.ai/)
CHUNKR_API_KEY="Fill your API key here"
