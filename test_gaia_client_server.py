#!/usr/bin/env python3
"""
Test script for GAIA client-server architecture.

This script tests the integration between:
- Client: agent/src/adapters/gaia/gaia.py
- Server: owl/gaia_service/main.py

It simulates the flow where the client sends GAIA tasks to the server,
the server performs inference and evaluation, and returns results with traces.
"""

import asyncio
import json
import sys
import time
from pathlib import Path

import aiohttp
from loguru import logger

# Add the agent directory to the path
sys.path.insert(0, str(Path(__file__).parent / "agent" / "src"))

from adapters.gaia.gaia import GAIAAdapter


async def test_server_health(service_url: str) -> bool:
    """Test if the GAIA service is running and healthy."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{service_url}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"Server health: {health_data}")
                    return True
                else:
                    logger.error(f"Server health check failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Failed to connect to server: {e}")
        return False


async def test_direct_api_call(service_url: str) -> bool:
    """Test direct API call to the /tasks endpoint."""
    try:
        # Prepare a simple GAIA task request
        request_payload = {
            "benchmark": "gaia",
            "model": "gpt-4o",
            "params": {
                "query": "What is 2 + 2?",
                "task_id": "test_001",
                "level": 1,
                "file_name": "",
                "file_path": "",
                "annotator_metadata": {},
                "final_answer": "4",  # Add ground truth for evaluation
                "max_steps": 5,
                "timeout": 300
            }
        }

        async with aiohttp.ClientSession() as session:
            # Submit task
            logger.info("Submitting task via direct API call...")
            async with session.post(f"{service_url}/tasks", json=request_payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Task submission failed: {response.status} - {error_text}")
                    return False

                task_response = await response.json()
                task_id = task_response["task_id"]
                logger.info(f"Task submitted successfully: {task_id}")

            # Poll for completion
            logger.info("Polling for task completion...")
            max_polls = 60  # 5 minutes max
            poll_interval = 5

            for i in range(max_polls):
                async with session.get(f"{service_url}/tasks/{task_id}") as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Failed to get task status: {error_text}")
                        return False

                    task_result = await response.json()
                    status = task_result.get("status")
                    logger.info(f"Poll {i+1}: Task status = {status}")

                    if status == "completed":
                        logger.success("Task completed successfully!")
                        logger.info(f"Result: {json.dumps(task_result, indent=2)}")
                        return True
                    elif status == "failed":
                        error = task_result.get("error", "Unknown error")
                        logger.error(f"Task failed: {error}")
                        return False

                    await asyncio.sleep(poll_interval)

            logger.error("Task timed out")
            return False

    except Exception as e:
        logger.error(f"Direct API test failed: {e}")
        return False


async def test_gaia_adapter(service_url: str) -> bool:
    """Test the GAIA adapter client."""
    try:
        # Configure the GAIA adapter
        config = {
            "service_url": service_url,
            "max_concurrent_requests": 1,
            "request_timeout": 300
        }

        # Create adapter instance
        adapter = GAIAAdapter(config)

        # Prepare evaluation parameters
        params = {
            "model": "gpt-4o",
            "level": 1,
            "num_tasks": 1,
            "max_steps": 5,
            "timeout": 300
        }

        logger.info("Testing GAIA adapter...")
        
        # Note: This will try to load actual GAIA data from external/gaia/2023
        # For testing, we'll create a mock task if no data is available
        if not adapter.tasks:
            logger.warning("No GAIA tasks loaded. Creating a mock task for testing.")
            adapter.tasks = [{
                "task_id": "mock_001",
                "question": "What is the capital of France?",
                "level": 1,
                "final_answer": "Paris",
                "file_name": "",
                "file_path": "",
                "annotator_metadata": {},
                "split": "test"
            }]

        # Execute evaluation
        logger.info("Starting GAIA evaluation...")
        results = await adapter.execute(params)

        # Check results
        if results and results.get("total_tasks", 0) > 0:
            logger.success("GAIA adapter test completed successfully!")
            logger.info(f"Results summary: {results.get('summary', {})}")
            return True
        else:
            logger.error("GAIA adapter test failed - no results")
            return False

    except Exception as e:
        logger.error(f"GAIA adapter test failed: {e}")
        return False


async def main():
    """Main test function."""
    service_url = "http://localhost:8000"
    
    logger.info("Starting GAIA client-server architecture test...")
    logger.info(f"Service URL: {service_url}")

    # Test 1: Server health check
    logger.info("\n=== Test 1: Server Health Check ===")
    if not await test_server_health(service_url):
        logger.error("Server health check failed. Make sure the GAIA service is running.")
        logger.info("To start the service, run: python -m owl.gaia_service.main")
        return False

    # Test 2: Direct API call
    logger.info("\n=== Test 2: Direct API Call ===")
    if not await test_direct_api_call(service_url):
        logger.error("Direct API call test failed.")
        return False

    # Test 3: GAIA adapter
    logger.info("\n=== Test 3: GAIA Adapter ===")
    if not await test_gaia_adapter(service_url):
        logger.error("GAIA adapter test failed.")
        return False

    logger.success("\n🎉 All tests passed! GAIA client-server architecture is working correctly.")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
