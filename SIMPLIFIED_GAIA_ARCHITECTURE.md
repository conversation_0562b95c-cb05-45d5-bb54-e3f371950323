# 简化的 GAIA 客户端-服务器架构

## 概述

根据您的建议，我已经简化了服务器端的逻辑，直接基于 `owl/run_gaia_workforce.py` 的评估流程来实现。新的架构更加简洁，去除了不必要的复杂性。

## 核心变化

### 1. 简化的核心服务 (`owl/gaia_service/simple_core.py`)

**基于 `run_gaia_workforce.py` 的核心逻辑**：
- 直接使用 `workforce.process_task()` 进行推理
- 使用 `workforce.get_workforce_final_answer()` 获取答案
- 使用 `benchmark.question_scorer()` 进行评分
- 捕获 `workforce.get_overall_task_solve_trajectory()` 作为轨迹

**简化的处理流程**：
```python
async def _run_single_task(self, query: GAIAQuery, max_replanning_tries: int, trace: ExecutionTrace):
    # 1. 创建 CAMEL Task
    camel_task = Task(content=query.question, additional_info=query.additional_info)
    
    # 2. 停止 workforce（如果正在运行）
    if self.workforce.is_running():
        self.workforce.stop()
    
    # 3. 处理任务
    processed_task = self.workforce.process_task(camel_task, max_replanning_tries=max_replanning_tries)
    
    # 4. 获取答案
    answer = self.workforce.get_workforce_final_answer(processed_task)
    
    # 5. 评分
    score = self.benchmark.question_scorer(answer, query.final_answer)
    
    # 6. 返回结果
    return EvaluationResult(correct=score, model_answer=answer, ...)
```

### 2. 简化的 Workforce 工厂 (`owl/gaia_service/simple_workforce_factory.py`)

**直接复制 `run_gaia_workforce.py` 的逻辑**：
- `construct_agent_list()` - 完全相同的 agent 配置
- `construct_workforce()` - 完全相同的 workforce 构建
- 相同的工具配置和模型设置

### 3. 数据流简化

**客户端 → 服务器**：
```json
{
    "benchmark": "gaia",
    "model": "gpt-4o",
    "params": {
        "query": "问题内容",
        "final_answer": "正确答案",
        "level": 1,
        "file_path": "文件路径",
        ...
    }
}
```

**服务器处理**：
1. 接收请求 → 创建 CAMEL Task
2. 调用 `workforce.process_task()`
3. 获取答案和轨迹
4. 评分并返回结果

**服务器 → 客户端**：
```json
{
    "task_id": "uuid",
    "status": "completed",
    "result": {
        "final_answer": "模型答案",
        "score": 1.0,
        "is_correct": true,
        "trajectory": [...],
        "workforce_trajectory": [...]
    }
}
```

## 文件结构

```
owl/gaia_service/
├── core.py                     # 简化的核心服务（基于 run_gaia_workforce.py）
├── workforce_factory.py        # 基于 run_gaia_workforce.py 的工厂
├── api.py                      # API 端点
├── main.py                     # 主服务
├── models.py                   # 数据模型
├── config.py                   # 配置
└── __init__.py                 # 包初始化
```

**已删除的复杂文件**：
- ~~`core.py`~~ (旧版复杂实现)
- ~~`workforce_factory.py`~~ (旧版复杂实现)
- ~~`trace_collector.py`~~ (不必要的抽象层)
- ~~`simple_core.py`~~ (重命名为 core.py)
- ~~`simple_workforce_factory.py`~~ (重命名为 workforce_factory.py)

## 使用方法

### 1. 启动服务器

```bash
python start_service.py
```

### 2. 测试架构

```bash
python test_gaia_client_server.py
```

### 3. 运行 GAIA 评估

```bash
cd agent
python -m src.main --benchmark gaia --model gpt-4o --level 1 --num-tasks 5
```

## 关键优势

### 1. **简化的逻辑**
- 直接基于 `run_gaia_workforce.py` 的成熟逻辑
- 去除了不必要的中间层和复杂的转换
- 保持了原有的评估质量

### 2. **更少的代码**
- `simple_core.py` 只有 ~250 行代码
- `simple_workforce_factory.py` 直接复制原有逻辑
- 减少了维护成本

### 3. **更好的兼容性**
- 与现有的 workforce 完全兼容
- 保持了原有的工具和 agent 配置
- 轨迹格式与原版一致

### 4. **更容易调试**
- 逻辑流程清晰简单
- 错误处理更直接
- 日志输出更有意义

## 与原版的对比

| 方面 | 原版复杂架构 | 简化架构 |
|------|-------------|----------|
| 核心逻辑 | 复杂的转换和包装 | 直接调用 workforce |
| 代码行数 | ~500+ 行 | ~250 行 |
| 依赖关系 | 多层抽象 | 直接依赖 |
| 调试难度 | 较高 | 较低 |
| 维护成本 | 较高 | 较低 |

## 配置

服务器仍然支持通过环境变量进行配置：

```bash
# .env 文件
OPENAI_API_KEY=your_api_key_here
HOST=0.0.0.0
PORT=8000
TEMP_DIR=tmp/gaia_service
```

## 总结

简化后的架构：

✅ **保持了完整的功能** - 推理、评估、轨迹记录  
✅ **大幅简化了代码** - 减少了 50% 的代码量  
✅ **提高了可维护性** - 逻辑清晰，易于理解  
✅ **保持了兼容性** - 与现有客户端完全兼容  
✅ **基于成熟逻辑** - 直接使用 `run_gaia_workforce.py` 的逻辑  

这个简化的架构更符合您的要求，在 `run_gaia_workforce.py` 的基础上封装了一层服务，去除了数据读取逻辑（由客户端处理），保持了核心的评估流程。
