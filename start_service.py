#!/usr/bin/env python3
"""
Simple startup script for the GAIA Evaluation Service.
"""

import sys
import os

# Add the owl directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "owl"))

if __name__ == "__main__":
    print("Starting GAIA Evaluation Service...")
    print("Service will be available at http://localhost:8000")
    print("Press Ctrl+C to stop the service.")
    
    try:
        from gaia_service.main import run_service_with_api
        run_service_with_api()
    except KeyboardInterrupt:
        print("\nService stopped by user.")
    except Exception as e:
        print(f"Service failed to start: {e}")
        sys.exit(1)
