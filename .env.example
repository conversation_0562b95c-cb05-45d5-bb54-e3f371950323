# GAIA Service Configuration Example
# Copy this file to .env and fill in your API keys

# Service Settings
SERVICE_NAME="GAIA Evaluation Service"
SERVICE_VERSION="1.0.0"
DEBUG=false

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Model Configuration
GAIA_MODEL_PLATFORM=openai
GAIA_COORDINATOR_MODEL=gpt-4o
GAIA_TASK_MODEL=gpt-4o
GAIA_ANSWERER_MODEL=gpt-4o
GAIA_TEMPERATURE=0.0

# Task Processing Settings
MAX_CONCURRENT_TASKS=5
DEFAULT_TIMEOUT_SECONDS=1800
MAX_RETRIES=3
MAX_REPLANNING_TRIES=2

# Storage Settings
TEMP_DIR=tmp/gaia_service
DATA_DIR=data/gaia
RESULTS_DIR=results/gaia_service

# Logging Settings
LOG_LEVEL=INFO
# LOG_FILE=logs/gaia_service.log

# API Keys (Required)
OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
# AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here

# Search API Keys (Optional, for web search functionality)
# GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_CSE_ID=your_google_cse_id_here
# SERPAPI_API_KEY=your_serpapi_api_key_here
